(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5721],{638:(e,s,a)=>{"use strict";a.d(s,{kn:()=>c,i8:()=>d,ET:()=>x,Zb:()=>g,Ay:()=>j,Fn:()=>n,VZ:()=>m,MI:()=>b,it:()=>u,e6:()=>p,Ox:()=>o});var l=a(1990),r=a(2302);let t={getShippingPartners:()=>r.uE.get("/shipping/partners/"),checkServiceability:e=>r.uE.get("/shipping/serviceable-areas/check_serviceability/?pin_code=".concat(e)),getAvailableDeliverySlots:e=>r.uE.post("/shipping/delivery-slots/available_slots/",e),calculateShippingRates:e=>r.uE.post("/shipping/shipping-rates/calculate/",e),getUserShipments:e=>{let s=new URLSearchParams;return e&&Object.entries(e).forEach(e=>{let[a,l]=e;void 0!==l&&s.append(a,l.toString())}),r.uE.get("/shipping/shipments/?".concat(s.toString()))},trackShipment:e=>r.uE.get("/shipping/shipments/".concat(e,"/track/"))},i=(0,l.zD)("shipping/fetchPartners",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.getShippingPartners()).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to fetch shipping partners")}}),d=(0,l.zD)("shipping/checkServiceability",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.checkServiceability(e.pin_code)).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to check serviceability")}}),n=(0,l.zD)("shipping/fetchAvailableSlots",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.getAvailableDeliverySlots(e)).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to fetch delivery slots")}}),c=(0,l.zD)("shipping/calculateRates",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.calculateShippingRates(e)).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to calculate shipping rates")}}),o=(0,l.zD)("shipping/trackShipment",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.trackShipment(e)).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to track shipment")}}),m=(0,l.zD)("shipping/fetchUserShipments",async(e,s)=>{let{rejectWithValue:a}=s;try{return(await t.getUserShipments()).data}catch(e){var l,r;return a((null==(r=e.response)||null==(l=r.data)?void 0:l.message)||"Failed to fetch shipments")}}),h=(0,l.Z0)({name:"shipping",initialState:{partners:[],serviceableAreas:[],deliverySlots:[],shipments:[],currentShipment:null,shippingRates:[],selectedDeliverySlot:null,selectedShippingAddress:null,loading:!1,error:null},reducers:{clearError:e=>{e.error=null},setSelectedDeliverySlot:(e,s)=>{e.selectedDeliverySlot=s.payload},setSelectedShippingAddress:(e,s)=>{e.selectedShippingAddress=s.payload},clearShippingRates:e=>{e.shippingRates=[]},clearDeliverySlots:e=>{e.deliverySlots=[]},setCurrentShipment:(e,s)=>{e.currentShipment=s.payload},resetShippingState:e=>{e.selectedDeliverySlot=null,e.selectedShippingAddress=null,e.shippingRates=[],e.deliverySlots=[],e.error=null}},extraReducers:e=>{e.addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,s)=>{e.loading=!1,e.partners=s.payload||[]}).addCase(i.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(d.pending,e=>{e.loading=!0,e.error=null}).addCase(d.fulfilled,(e,s)=>{e.loading=!1,s.payload&&s.payload.serviceable&&s.payload.areas&&(e.serviceableAreas=s.payload.areas)}).addCase(d.rejected,(e,s)=>{e.loading=!1,e.error=s.payload,e.serviceableAreas=[]}).addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,s)=>{e.loading=!1,e.deliverySlots=s.payload||[]}).addCase(n.rejected,(e,s)=>{e.loading=!1,e.error=s.payload,e.deliverySlots=[]}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,s)=>{e.loading=!1,s.payload?e.shippingRates=Array.isArray(s.payload)?s.payload:[s.payload]:e.shippingRates=[]}).addCase(c.rejected,(e,s)=>{e.loading=!1,e.error=s.payload,e.shippingRates=[]}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,s)=>{var a;e.loading=!1,e.currentShipment=(null==(a=s.payload)?void 0:a.shipment)||null}).addCase(o.rejected,(e,s)=>{e.loading=!1,e.error=s.payload,e.currentShipment=null}).addCase(m.pending,e=>{e.loading=!0,e.error=null}).addCase(m.fulfilled,(e,s)=>{e.loading=!1,s.payload?Array.isArray(s.payload)?e.shipments=s.payload:s.payload.results?e.shipments=s.payload.results:e.shipments=[]:e.shipments=[]}).addCase(m.rejected,(e,s)=>{e.loading=!1,e.error=s.payload})}}),{clearError:x,setSelectedDeliverySlot:u,setSelectedShippingAddress:p,clearShippingRates:g,clearDeliverySlots:v,setCurrentShipment:b,resetShippingState:y}=h.actions,j=h.reducer},2815:(e,s,a)=>{"use strict";a.d(s,{R:()=>r});var l=a(5155);function r(e){let{size:s="md",text:a,className:r=""}=e;return(0,l.jsx)("div",{className:"flex items-center justify-center ".concat(r),children:(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,l.jsxs)("svg",{className:"animate-spin ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[s]," text-blue-600"),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a&&(0,l.jsx)("p",{className:"text-sm text-gray-600",children:a})]})})}a(2115)},3224:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>h,Ej:()=>i,Jw:()=>c,Qg:()=>n});var l=a(1990),r=a(2302),t=a(7141);let i=(0,l.zD)("wishlist/fetch",async(e,s)=>{let{rejectWithValue:a}=s;try{var l;let e=await r.uE.get(t.Sn.WISHLIST.LIST);if(e.success&&e.data)return e.data;return a((null==(l=e.error)?void 0:l.message)||"Failed to fetch wishlist")}catch(e){return a(e.message||"Failed to fetch wishlist")}}),d=(0,l.zD)("wishlist/add",async(e,s)=>{let{rejectWithValue:a}=s;try{var l;let s=await r.uE.post(t.Sn.WISHLIST.ADD,{product_id:e});if(s.success&&s.data)return s.data;return a((null==(l=s.error)?void 0:l.message)||"Failed to add to wishlist")}catch(e){return a(e.message||"Failed to add to wishlist")}}),n=(0,l.zD)("wishlist/remove",async(e,s)=>{let{rejectWithValue:a}=s;try{var l;let s=await r.uE.delete(t.Sn.WISHLIST.REMOVE(e));if(s.success)return e;return a((null==(l=s.error)?void 0:l.message)||"Failed to remove from wishlist")}catch(e){return a(e.message||"Failed to remove from wishlist")}}),c=(0,l.zD)("wishlist/clear",async(e,s)=>{let{rejectWithValue:a}=s;try{var l;let e=await r.uE.delete(t.Sn.WISHLIST.CLEAR);if(e.success)return null;return a((null==(l=e.error)?void 0:l.message)||"Failed to clear wishlist")}catch(e){return a(e.message||"Failed to clear wishlist")}}),o=(0,l.Z0)({name:"wishlist",initialState:{wishlist:null,loading:!1,error:null},reducers:{clearError:e=>{e.error=null}},extraReducers:e=>{e.addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,s)=>{e.loading=!1,e.wishlist=s.payload,e.error=null}).addCase(i.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(d.pending,e=>{e.loading=!0,e.error=null}).addCase(d.fulfilled,(e,s)=>{e.loading=!1,e.wishlist?e.wishlist.items.push(s.payload):e.wishlist={id:"temp-id",items:[s.payload],created_at:new Date().toISOString()},e.error=null}).addCase(d.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,s)=>{e.loading=!1,e.wishlist&&(e.wishlist.items=e.wishlist.items.filter(e=>e.id!==s.payload)),e.error=null}).addCase(n.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,e=>{e.loading=!1,e.wishlist&&(e.wishlist.items=[]),e.error=null}).addCase(c.rejected,(e,s)=>{e.loading=!1,e.error=s.payload})}}),{clearError:m}=o.actions,h=o.reducer},3915:(e,s,a)=>{"use strict";a.d(s,{p:()=>r});var l=a(5155);function r(e){let{label:s,error:a,helperText:r,className:t="",id:i,...d}=e,n=i||"input-".concat(Math.random().toString(36).substr(2,9)),c=a?"".concat(n,"-error"):void 0,o=r?"".concat(n,"-helper"):void 0,m="".concat("block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors touch-manipulation"," ").concat(a?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400"," ").concat("disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed disabled:border-gray-200"," ").concat(t);return(0,l.jsxs)("div",{className:"space-y-1",children:[s&&(0,l.jsx)("label",{htmlFor:n,className:"block text-sm font-medium text-gray-700",children:s}),(0,l.jsx)("input",{id:n,className:m,"aria-invalid":a?"true":"false","aria-describedby":[c,o].filter(Boolean).join(" ")||void 0,...d}),a&&(0,l.jsx)("p",{id:c,className:"text-sm text-red-600",role:"alert",children:a}),r&&!a&&(0,l.jsx)("p",{id:o,className:"text-sm text-gray-500",children:r})]})}a(2115)},4180:(e,s,a)=>{Promise.resolve().then(a.bind(a,8462))},8462:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var l=a(5155),r=a(2115),t=a(4540),i=a(3741);function d(e){let{children:s,className:a="",...r}=e;return(0,l.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm ".concat(a),...r,children:s})}a(3915),a(2815),a(3224),a(3568);var n=a(638);let c=e=>{let{onSelect:s,selectedSlotId:a,pincode:c}=e,o=(0,t.wA)(),{deliverySlots:m,selectedDeliverySlot:h,loading:x,error:u}=(0,t.d4)(e=>e.shipping),[p,g]=(0,r.useState)(""),[v,b]=(0,r.useState)([]),y=a||(null==h?void 0:h.id);return((0,r.useEffect)(()=>{if(c){let e=[];for(let s=0;s<7;s++){let a=new Date;a.setDate(a.getDate()+s),e.push(a.toISOString().split("T")[0])}b(e),g(e[0]),o((0,n.Fn)({delivery_date:e[0],pin_code:c}))}},[o,c]),(0,r.useEffect)(()=>{p&&c&&o((0,n.Fn)({delivery_date:p,pin_code:c}))},[o,p,c]),c)?x?(0,l.jsx)("div",{className:"text-center py-4",children:"Loading delivery slots..."}):u?(0,l.jsxs)("div",{className:"text-center py-4 text-red-500",children:["Error loading delivery slots: ",u]}):(0,l.jsxs)("div",{className:"delivery-slot-selector",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Select Delivery Date"}),(0,l.jsx)("div",{className:"flex overflow-x-auto space-x-2 pb-2 mb-4",children:v.map(e=>(0,l.jsx)(i.A,{onClick:()=>g(e),variant:p===e?"primary":"outline",className:"whitespace-nowrap",children:new Date(e).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})},e))}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Select Delivery Time"}),0===m.length?(0,l.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No delivery slots available for this date."}):(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:m.map(e=>{let a=y===e.id,r=e.is_active&&(e.available_capacity||0)>0;return(0,l.jsx)("div",{className:"cursor-pointer transition-all ".concat(r?a?"border-2 border-blue-500 bg-blue-50":"hover:border-gray-300":"opacity-50 cursor-not-allowed"),onClick:()=>r&&(e=>{o((0,n.it)(e)),s&&s(e)})(e),children:(0,l.jsxs)(d,{children:[(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:e.name}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:(e=>"".concat(e.start_time," - ").concat(e.end_time))(e)}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.day_of_week_display})]}),(0,l.jsx)("div",{className:"text-right",children:e.additional_fee>0?(0,l.jsxs)("span",{className:"text-sm font-medium text-orange-600",children:["+₹",e.additional_fee]}):(0,l.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"Free"})})]}),(0,l.jsxs)("div",{className:"mt-3 flex justify-between items-center",children:[(0,l.jsx)("div",{className:"text-xs text-gray-500",children:e.available_capacity?"".concat(e.available_capacity," slots left"):"Limited slots"}),r?a?(0,l.jsxs)("span",{className:"text-xs text-blue-600 font-medium flex items-center",children:[(0,l.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Selected"]}):null:(0,l.jsx)("span",{className:"text-xs text-red-600 font-medium",children:"Not available"})]})]})},e.id)})})]}):(0,l.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Please enter a pincode to view available delivery slots."})},o=e=>{let{addresses:s,onAddressSelect:a,onAddNewAddress:i,onEditAddress:d,className:c=""}=e,o=(0,t.wA)(),{selectedShippingAddress:m,serviceableAreas:h,loading:x,error:u}=(0,t.d4)(e=>e.shipping),[p,g]=(0,r.useState)(null),[v,b]=(0,r.useState)({});(0,r.useEffect)(()=>{let e=s.find(e=>e.is_default)||s[0];e&&!p&&y(e)},[s]),(0,r.useEffect)(()=>()=>{o((0,n.ET)())},[o]);let y=async e=>{g(e);let s=(e=>({first_name:e.first_name,last_name:e.last_name,company:e.company,address_line_1:e.address_line_1,address_line_2:e.address_line_2,city:e.city,state:e.state,postal_code:e.postal_code,country:e.country,phone:e.phone}))(e);if(o((0,n.e6)(s)),null==a||a(s),e.postal_code){b(s=>({...s,[e.id]:{serviceable:!1,loading:!0}}));try{let s=await o((0,n.i8)({pin_code:e.postal_code}));n.i8.fulfilled.match(s)?b(s=>({...s,[e.id]:{serviceable:!0,loading:!1}})):b(a=>({...a,[e.id]:{serviceable:!1,loading:!1,error:s.payload}}))}catch(s){b(s=>({...s,[e.id]:{serviceable:!1,loading:!1,error:"Failed to check serviceability"}}))}}};return s.length?(0,l.jsx)("div",{className:"shipping-address-manager ".concat(c),children:(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Delivery Address"}),(0,l.jsxs)("button",{onClick:i,className:"text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add New"]})]}),(0,l.jsx)("div",{className:"space-y-3",children:s.map(e=>{let s=(null==p?void 0:p.id)===e.id,a=v[e.id];return(0,l.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-all duration-200 ".concat(s?"border-blue-500 bg-blue-50 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),onClick:()=>y(e),children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex items-start flex-1",children:[(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 mt-1 flex-shrink-0 ".concat(s?"border-blue-500 bg-blue-500":"border-gray-300"),children:s&&(0,l.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center mb-2",children:[(0,l.jsx)("div",{className:"text-gray-600 mr-2",children:(e=>{switch(e){case"HOME":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})});case"WORK":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2zm4-3a1 1 0 00-1 1v1h2V4a1 1 0 00-1-1zm-3 4a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})});default:return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})})}})(e.type)}),(0,l.jsxs)("span",{className:"font-medium text-gray-900",children:[e.first_name," ",e.last_name]}),e.is_default&&(0,l.jsx)("span",{className:"ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:"Default"})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-1",children:(e=>[e.address_line_1,e.address_line_2,e.city,e.state,e.postal_code].filter(Boolean).join(", "))(e)}),e.phone&&(0,l.jsxs)("p",{className:"text-gray-500 text-sm",children:["Phone: ",e.phone]}),a&&(0,l.jsx)("div",{className:"mt-2",children:a.loading?(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,l.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full mr-2"}),"Checking serviceability..."]}):a.serviceable?(0,l.jsxs)("div",{className:"flex items-center text-sm text-green-600",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Delivery available"]}):(0,l.jsxs)("div",{className:"flex items-center text-sm text-red-600",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),a.error||"Not serviceable"]})})]})]}),(0,l.jsx)("button",{onClick:s=>{s.stopPropagation(),null==d||d(e)},className:"text-gray-400 hover:text-gray-600 ml-2",children:(0,l.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})]})},e.id)})}),u&&(0,l.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsx)("p",{className:"text-red-700 text-sm",children:u})]})})]})}):(0,l.jsx)("div",{className:"shipping-address-manager ".concat(c),children:(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Delivery Address"}),(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsxs)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"No delivery addresses found"}),(0,l.jsx)("button",{onClick:i,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Add New Address"})]})]})})},m=e=>{let{shipment:s,className:a=""}=e,r=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a="w-6 h-6 ".concat(s?"text-white":"text-gray-400");switch(e){case"PENDING":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})});case"PROCESSING":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})});case"SHIPPED":case"IN_TRANSIT":return(0,l.jsxs)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:[(0,l.jsx)("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),(0,l.jsx)("path",{d:"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707L16 7.586A1 1 0 0015.414 7H14z"})]});case"OUT_FOR_DELIVERY":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})});case"DELIVERED":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"FAILED_DELIVERY":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"RETURNED":case"CANCELLED":return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return(0,l.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm-1-4a1 1 0 112 0 1 1 0 01-2 0zm1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}},t=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(s)switch(e){case"DELIVERED":return"bg-green-500";case"SHIPPED":case"IN_TRANSIT":case"OUT_FOR_DELIVERY":return"bg-blue-500";case"PROCESSING":return"bg-yellow-500";case"CANCELLED":case"RETURNED":return"bg-red-500";case"FAILED_DELIVERY":return"bg-orange-500";default:return"bg-gray-500"}return"bg-gray-300"},i=(()=>{let e=[];s.tracking_updates&&s.tracking_updates.length>0&&s.tracking_updates.forEach(s=>{e.push({status:s.status,status_display:s.status_display,description:s.description,location:s.location,timestamp:s.timestamp,isFromTracking:!0})});let a=new Set(e.map(e=>e.status));return a.has("PENDING")||e.push({status:"PENDING",status_display:"Order Placed",description:"Your order has been placed and is being prepared for shipment.",timestamp:s.created_at,isFromTracking:!1}),s.shipped_at&&!a.has("SHIPPED")&&e.push({status:"SHIPPED",status_display:"Shipped",description:"Your order has been shipped and is on its way.",timestamp:s.shipped_at,isFromTracking:!1}),s.delivered_at&&!a.has("DELIVERED")&&e.push({status:"DELIVERED",status_display:"Delivered",description:"Your order has been successfully delivered.",timestamp:s.delivered_at,isFromTracking:!1}),e.sort((e,s)=>new Date(s.timestamp).getTime()-new Date(e.timestamp).getTime())})(),d=s.status,n=["PENDING","PROCESSING","SHIPPED","IN_TRANSIT","OUT_FOR_DELIVERY","DELIVERED"],c=n.indexOf(d);return(0,l.jsxs)("div",{className:"tracking-timeline ".concat(a),children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Tracking Timeline"}),(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsx)("div",{className:"flex items-center justify-between",children:n.map((e,s)=>{let a=s<=c,i=e===d;return(0,l.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,l.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat(a?t(e,!0):"bg-gray-300"," ").concat(i?"ring-4 ring-blue-200":""),children:r(e,a)}),(0,l.jsx)("div",{className:"mt-2 text-center",children:(0,l.jsx)("p",{className:"text-xs font-medium ".concat(a?"text-gray-900":"text-gray-500"),children:(e=>({PENDING:"Order Placed",PROCESSING:"Processing",SHIPPED:"Shipped",IN_TRANSIT:"In Transit",OUT_FOR_DELIVERY:"Out for Delivery",DELIVERED:"Delivered",FAILED_DELIVERY:"Delivery Failed",RETURNED:"Returned",CANCELLED:"Cancelled"})[e]||e)(e)})}),s<n.length-1&&(0,l.jsx)("div",{className:"absolute h-0.5 w-full mt-5 ".concat(s<c?"bg-blue-500":"bg-gray-300"),style:{left:"50%",right:"-50%",zIndex:-1}})]},e)})})}),i.length>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-4",children:"Detailed History"}),(0,l.jsx)("div",{className:"space-y-4",children:i.map((e,s)=>{let{date:a,time:d}=(e=>{let s=new Date(e);return{date:s.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),time:s.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})}})(e.timestamp),n=0===s;return(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsxs)("div",{className:"flex-shrink-0 mr-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(n?t(e.status,!0):"bg-gray-300"),children:r(e.status,n)}),s<i.length-1&&(0,l.jsx)("div",{className:"w-0.5 h-8 bg-gray-300 mx-auto mt-2"})]}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("p",{className:"text-sm font-medium ".concat(n?"text-gray-900":"text-gray-700"),children:e.status_display}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-xs text-gray-500",children:a}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:d})]})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),e.location&&(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-1 flex items-center",children:[(0,l.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),e.location]})]})]},"".concat(e.timestamp,"-").concat(e.status))})})]}),0===i.length&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,l.jsx)("p",{className:"text-gray-600",children:"No tracking information available yet"})]})]})},h=e=>{let{orderId:s,trackingNumber:a,showSearch:i=!0,className:d=""}=e,c=(0,t.wA)(),{currentShipment:o,shipments:h,loading:x,error:u}=(0,t.d4)(e=>e.shipping),[p,g]=(0,r.useState)(a||""),[v,b]=(0,r.useState)("tracking");(0,r.useEffect)(()=>{if(a)y(a);else if(s){let e=h.find(e=>e.order===s);e?c((0,n.MI)(e)):c((0,n.VZ)())}return()=>{c((0,n.ET)())}},[c,a,s]),(0,r.useEffect)(()=>{if(s&&h.length>0){let e=h.find(e=>e.order===s);e&&c((0,n.MI)(e))}},[c,s,h]);let y=async e=>{if(e.trim())try{await c((0,n.Ox)(e.trim())).unwrap()}catch(e){console.error("Failed to track shipment:",e)}};return(0,l.jsxs)("div",{className:"order-tracking-interface ".concat(d),children:[i&&(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Track Your Order"}),(0,l.jsx)("form",{onSubmit:e=>{e.preventDefault(),p.trim()&&y(p.trim())},className:"space-y-4",children:(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("label",{htmlFor:"search-query",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Tracking Number or Order ID"}),(0,l.jsx)("input",{id:"search-query",type:"text",value:p,onChange:e=>g(e.target.value),placeholder:"e.g., TRK123456789 or ORD123456",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsx)("button",{type:"submit",disabled:x||!p.trim(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:x?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Tracking..."]}):"Track"})})]})})]}),u&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsx)("p",{className:"text-red-700",children:u})]})}),o&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Shipment Details"}),(0,l.jsxs)("div",{className:"px-3 py-1 rounded-full text-sm font-medium flex items-center ".concat((e=>{switch(e){case"DELIVERED":return"text-green-600 bg-green-100";case"SHIPPED":case"IN_TRANSIT":case"OUT_FOR_DELIVERY":return"text-blue-600 bg-blue-100";case"PROCESSING":return"text-yellow-600 bg-yellow-100";case"CANCELLED":case"RETURNED":return"text-red-600 bg-red-100";case"FAILED_DELIVERY":return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}})(o.status)),children:[(e=>{switch(e){case"DELIVERED":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"SHIPPED":case"IN_TRANSIT":return(0,l.jsxs)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,l.jsx)("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),(0,l.jsx)("path",{d:"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707L16 7.586A1 1 0 0015.414 7H14z"})]});case"OUT_FOR_DELIVERY":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})});case"PROCESSING":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})});case"CANCELLED":case"RETURNED":return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return(0,l.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}})(o.status),(0,l.jsx)("span",{className:"ml-2",children:o.status_display})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tracking Information"}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Tracking Number:"}),(0,l.jsx)("span",{className:"font-medium",children:o.tracking_number})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Shipping Partner:"}),(0,l.jsx)("span",{className:"font-medium",children:o.shipping_partner_name})]}),o.estimated_delivery_date&&(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Estimated Delivery:"}),(0,l.jsx)("span",{className:"font-medium",children:new Date(o.estimated_delivery_date).toLocaleDateString()})]}),o.delivery_slot_display&&(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Delivery Slot:"}),(0,l.jsx)("span",{className:"font-medium",children:o.delivery_slot_display})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Delivery Address"}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:(0,l.jsx)("p",{children:(e=>"string"==typeof e?e:[e.address_line_1,e.address_line_2,e.city,e.state,e.postal_code].filter(Boolean).join(", "))(o.shipping_address)})})]})]}),o.shipping_cost>0&&(0,l.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Shipping Cost:"}),(0,l.jsxs)("span",{className:"font-medium text-lg",children:["₹",o.shipping_cost]})]})})]}),(0,l.jsx)(m,{shipment:o,className:"bg-white border border-gray-200 rounded-lg p-6"})]}),x&&!o&&(0,l.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Loading tracking information..."})]})}),!x&&!o&&!u&&i&&(0,l.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,l.jsx)("p",{className:"text-gray-600",children:"Enter a tracking number or order ID to view shipment details"})]})})]})},x=e=>{var s,a,i;let{sourcePinCode:d="",destinationPinCode:c="",weight:o=0,dimensions:m={},onRateSelect:h,className:x=""}=e,u=(0,t.wA)(),{shippingRates:p,loading:g,error:v}=(0,t.d4)(e=>e.shipping),[b,y]=(0,r.useState)({source_pin_code:d,destination_pin_code:c,weight:o,dimensions:m}),[j,f]=(0,r.useState)(null),[N,w]=(0,r.useState)(!1);(0,r.useEffect)(()=>{y(e=>({...e,source_pin_code:d,destination_pin_code:c,weight:o,dimensions:m}))},[d,c,o,m]),(0,r.useEffect)(()=>()=>{u((0,n.ET)())},[u]);let _=(e,s)=>{y(a=>({...a,[e]:s}))},S=(e,s)=>{y(a=>({...a,dimensions:{...a.dimensions,[e]:s}}))},C=async e=>{if(e.preventDefault(),b.source_pin_code&&b.destination_pin_code&&b.weight)try{await u((0,n.kn)(b)).unwrap(),f(null)}catch(e){console.error("Failed to calculate shipping rates:",e)}},E=b.source_pin_code&&b.destination_pin_code&&b.weight>0;return(0,l.jsx)("div",{className:"shipping-cost-calculator ".concat(x),children:(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Calculate Shipping Cost"}),(0,l.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"source-pin",className:"block text-sm font-medium text-gray-700 mb-1",children:"Source Pin Code"}),(0,l.jsx)("input",{id:"source-pin",type:"text",value:b.source_pin_code,onChange:e=>_("source_pin_code",e.target.value),placeholder:"e.g., 110001",maxLength:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"destination-pin",className:"block text-sm font-medium text-gray-700 mb-1",children:"Destination Pin Code"}),(0,l.jsx)("input",{id:"destination-pin",type:"text",value:b.destination_pin_code,onChange:e=>_("destination_pin_code",e.target.value),placeholder:"e.g., 400001",maxLength:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"weight",className:"block text-sm font-medium text-gray-700 mb-1",children:"Weight (kg)"}),(0,l.jsx)("input",{id:"weight",type:"number",step:"0.1",min:"0.1",value:b.weight,onChange:e=>_("weight",parseFloat(e.target.value)||0),placeholder:"e.g., 1.5",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsx)("div",{children:(0,l.jsxs)("button",{type:"button",onClick:()=>w(!N),className:"text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-1 transition-transform ".concat(N?"rotate-90":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),"Advanced Options"]})}),N&&(0,l.jsxs)("div",{className:"space-y-4 p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900",children:"Package Dimensions (cm)"}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"length",className:"block text-sm font-medium text-gray-700 mb-1",children:"Length"}),(0,l.jsx)("input",{id:"length",type:"number",step:"0.1",min:"0",value:(null==(s=b.dimensions)?void 0:s.length)||"",onChange:e=>S("length",parseFloat(e.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"width",className:"block text-sm font-medium text-gray-700 mb-1",children:"Width"}),(0,l.jsx)("input",{id:"width",type:"number",step:"0.1",min:"0",value:(null==(a=b.dimensions)?void 0:a.width)||"",onChange:e=>S("width",parseFloat(e.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"height",className:"block text-sm font-medium text-gray-700 mb-1",children:"Height"}),(0,l.jsx)("input",{id:"height",type:"number",step:"0.1",min:"0",value:(null==(i=b.dimensions)?void 0:i.height)||"",onChange:e=>S("height",parseFloat(e.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("button",{type:"submit",disabled:!E||g,className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:g?(0,l.jsxs)("div",{className:"flex items-center justify-center",children:[(0,l.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Calculating..."]}):"Calculate Rates"}),p.length>0&&(0,l.jsx)("button",{type:"button",onClick:()=>{u((0,n.Zb)()),f(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Clear"})]})]}),v&&(0,l.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsx)("p",{className:"text-red-700 text-sm",children:v})]})}),p.length>0&&(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-4",children:"Available Shipping Options"}),(0,l.jsx)("div",{className:"space-y-3",children:p.map((e,s)=>(0,l.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-all duration-200 ".concat((null==j?void 0:j.shipping_partner.id)===e.shipping_partner.id?"border-blue-500 bg-blue-50 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),onClick:()=>(e=>{f(e),null==h||h(e)})(e),children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 ".concat((null==j?void 0:j.shipping_partner.id)===e.shipping_partner.id?"border-blue-500 bg-blue-500":"border-gray-300"),children:(null==j?void 0:j.shipping_partner.id)===e.shipping_partner.id&&(0,l.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-gray-900",children:e.shipping_partner.name}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Delivery: ",(e=>e.estimated_delivery_days?"".concat(e.estimated_delivery_days," days"):e.min_delivery_days&&e.max_delivery_days?"".concat(e.min_delivery_days,"-").concat(e.max_delivery_days," days"):"Not specified")(e)]}),e.is_dynamic_rate&&(0,l.jsx)("span",{className:"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-1",children:"Live Rate"})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["₹",e.rate.toFixed(2)]}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"+ taxes"})]})]})},"".concat(e.shipping_partner.id,"-").concat(s)))}),j&&(0,l.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Selected Option:"}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[j.shipping_partner.name," - ₹",j.rate.toFixed(2)]})]}),(0,l.jsx)("button",{onClick:()=>f(null),className:"text-sm text-red-600 hover:text-red-700 font-medium",children:"Clear Selection"})]})})]}),!g&&0===p.length&&!v&&E&&(0,l.jsx)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 text-yellow-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),(0,l.jsx)("p",{className:"text-yellow-700",children:'Click "Calculate Rates" to see available shipping options'})]})})]})})},u=e=>{let{pinCode:s="",onServiceabilityCheck:a,className:i=""}=e,d=(0,t.wA)(),{serviceableAreas:c,loading:o,error:m}=(0,t.d4)(e=>e.shipping),[h,x]=(0,r.useState)(s),[u,p]=(0,r.useState)(""),[g,v]=(0,r.useState)(null);(0,r.useEffect)(()=>{x(s),s&&s!==u&&b(s)},[s]),(0,r.useEffect)(()=>()=>{d((0,n.ET)())},[d]);let b=async e=>{let s=e||h;if(s&&6===s.length){p(s);try{let e=await d((0,n.i8)({pin_code:s})).unwrap();if(e){let l={serviceable:e.serviceable,areas:e.areas||[],pinCode:s};v(l),null==a||a(e.serviceable,e.areas)}}catch(e){v({serviceable:!1,areas:[],pinCode:s}),null==a||a(!1,[])}}};return(0,l.jsx)("div",{className:"serviceability-checker ".concat(i),children:(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Check Delivery Availability"}),(0,l.jsx)("form",{onSubmit:e=>{e.preventDefault(),b()},className:"space-y-4",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"pin-code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Pin Code"}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("input",{id:"pin-code",type:"text",value:h,onChange:e=>{x(e.target.value.replace(/\D/g,"").slice(0,6))},placeholder:"e.g., 110001",maxLength:6,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,l.jsx)("button",{type:"submit",disabled:o||6!==h.length,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:o?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Checking..."]}):"Check"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pin code to check delivery availability"})]})}),g&&g.pinCode===h&&(0,l.jsx)("div",{className:"mt-6",children:g.serviceable?(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("svg",{className:"w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("h4",{className:"text-green-800 font-medium mb-2",children:["Great! We deliver to ",g.pinCode]}),g.areas.length>0&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("p",{className:"text-green-700 text-sm mb-3",children:"Available delivery options:"}),g.areas.map((e,s)=>(0,l.jsx)("div",{className:"bg-white border border-green-200 rounded-lg p-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"font-medium text-gray-900",children:[e.city,", ",e.state]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:e.country})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-green-600",children:(e=>e.min_delivery_days===e.max_delivery_days?"".concat(e.min_delivery_days," day").concat(e.min_delivery_days>1?"s":""):"".concat(e.min_delivery_days,"-").concat(e.max_delivery_days," days"))(e)}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"delivery time"})]})]})},e.id))]})]})]})}):(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("svg",{className:"w-6 h-6 text-red-500 mr-3 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("h4",{className:"text-red-800 font-medium mb-2",children:["Sorry, we don't deliver to ",g.pinCode]}),(0,l.jsx)("p",{className:"text-red-700 text-sm",children:"This pin code is currently not in our delivery network. Please try a different pin code or contact our support team for assistance."})]})]})})}),m&&(0,l.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsx)("p",{className:"text-red-700 text-sm",children:m})]})}),(0,l.jsxs)("div",{className:"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,l.jsxs)("h4",{className:"text-blue-800 font-medium mb-2 flex items-center",children:[(0,l.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Quick Tips"]}),(0,l.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,l.jsx)("li",{children:"• Pin codes are 6-digit numbers (e.g., 110001 for New Delhi)"}),(0,l.jsx)("li",{children:"• Delivery times may vary based on location and shipping partner"}),(0,l.jsx)("li",{children:"• Some areas may have additional delivery charges"}),(0,l.jsx)("li",{children:"• Contact support if your area is not serviceable"})]})]})]})})},p=[{id:"1",type:"HOME",first_name:"John",last_name:"Doe",address_line_1:"123 Main Street",address_line_2:"Apt 4B",city:"New Delhi",state:"Delhi",postal_code:"110001",country:"India",phone:"+91 9876543210",is_default:!0},{id:"2",type:"WORK",first_name:"John",last_name:"Doe",address_line_1:"456 Business Park",city:"Gurgaon",state:"Haryana",postal_code:"122001",country:"India",phone:"+91 9876543210",is_default:!1}],g=()=>{let[e,s]=(0,r.useState)("delivery"),[a,t]=(0,r.useState)("110001");return(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,l.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Shipping & Delivery"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Manage your delivery preferences, track orders, and calculate shipping costs"})]}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm mb-6",children:(0,l.jsx)("div",{className:"border-b border-gray-200",children:(0,l.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"delivery",label:"Delivery Options",icon:"\uD83D\uDE9A"},{id:"tracking",label:"Order Tracking",icon:"\uD83D\uDCE6"},{id:"calculator",label:"Shipping Calculator",icon:"\uD83D\uDCB0"},{id:"serviceability",label:"Check Serviceability",icon:"\uD83D\uDCCD"}].map(a=>(0,l.jsxs)("button",{onClick:()=>s(a.id),className:"py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ".concat(e===a.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,l.jsx)("span",{className:"mr-2",children:a.icon}),a.label]},a.id))})})}),(0,l.jsxs)("div",{className:"space-y-6",children:["delivery"===e&&(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsx)("div",{children:(0,l.jsx)(o,{addresses:p,onAddressSelect:e=>{e&&t(e.postal_code)},onAddNewAddress:()=>alert("Add new address functionality would be implemented here"),onEditAddress:e=>alert("Edit address functionality for ".concat(e.id," would be implemented here"))})}),(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,l.jsx)(c,{pincode:a,onSelect:e=>{console.log("Selected delivery slot:",e)}})})})]}),"tracking"===e&&(0,l.jsx)("div",{children:(0,l.jsx)(h,{showSearch:!0,className:"bg-white"})}),"calculator"===e&&(0,l.jsx)("div",{className:"max-w-4xl",children:(0,l.jsx)(x,{sourcePinCode:"110001",destinationPinCode:a,weight:1.5,onRateSelect:e=>{console.log("Selected shipping rate:",e)}})}),"serviceability"===e&&(0,l.jsx)("div",{className:"max-w-2xl",children:(0,l.jsx)(u,{pinCode:a,onServiceabilityCheck:(e,s)=>{console.log("Serviceability result:",{serviceable:e,areas:s})}})})]}),(0,l.jsxs)("div",{className:"mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Shipping Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Delivery Options"}),(0,l.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Standard Delivery (3-5 days)"}),(0,l.jsx)("li",{children:"• Express Delivery (1-2 days)"}),(0,l.jsx)("li",{children:"• Same Day Delivery (selected areas)"}),(0,l.jsx)("li",{children:"• Scheduled Delivery Slots"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Shipping Partners"}),(0,l.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Shiprocket"}),(0,l.jsx)("li",{children:"• Delhivery"}),(0,l.jsx)("li",{children:"• Blue Dart"}),(0,l.jsx)("li",{children:"• DTDC"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Payment Options"}),(0,l.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Cash on Delivery"}),(0,l.jsx)("li",{children:"• Prepaid Orders"}),(0,l.jsx)("li",{children:"• Digital Wallets"}),(0,l.jsx)("li",{children:"• UPI Payments"})]})]})]})]})]})})}}},e=>{e.O(0,[3464,4540,1990,3568,2125,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=4180)),_N_E=e.O()}]);