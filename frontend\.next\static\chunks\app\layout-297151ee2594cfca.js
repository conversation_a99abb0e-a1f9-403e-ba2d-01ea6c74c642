(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},2056:(e,t,r)=>{"use strict";r.d(t,{LP:()=>l,Ti:()=>c,To:()=>i,Uk:()=>o,Y9:()=>n,yH:()=>a});var s=r(7141);let o=()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN),t=localStorage.getItem(s.d5.REFRESH_TOKEN);if(e&&t)return{access:e,refresh:t}}catch(e){console.error("Error getting stored tokens:",e)}return null},a=e=>{try{localStorage.setItem(s.d5.ACCESS_TOKEN,e.access),localStorage.setItem(s.d5.REFRESH_TOKEN,e.refresh)}catch(e){console.error("Error storing tokens:",e)}},n=()=>{try{localStorage.removeItem(s.d5.ACCESS_TOKEN),localStorage.removeItem(s.d5.REFRESH_TOKEN)}catch(e){console.error("Error removing tokens:",e)}},c=()=>{try{let e=localStorage.getItem(s.d5.USER);return e?JSON.parse(e):null}catch(e){return console.error("Error getting stored user:",e),null}},i=e=>{try{localStorage.setItem(s.d5.USER,JSON.stringify(e))}catch(e){console.error("Error storing user:",e)}},l=()=>{try{localStorage.removeItem(s.d5.USER)}catch(e){console.error("Error removing user:",e)}}},2302:(e,t,r)=>{"use strict";r.d(t,{uE:()=>c});var s=r(3464),o=r(7141),a=r(2056);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=(0,a.Uk)();return(null==t?void 0:t.access)&&e.headers&&(e.headers.Authorization="Bearer ".concat(t.access)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{if(e&&"object"==typeof e&&"config"in e&&"response"in e){var t;let r=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!r._retry){r._retry=!0;try{let e=(0,a.Uk)();if(null==e?void 0:e.refresh){let t=(await this.refreshToken(e.refresh)).data;return(0,a.yH)(t),r.headers&&(r.headers.Authorization="Bearer ".concat(t.access)),this.client(r)}}catch(e){(0,a.Y9)(),window.location.href="/auth/login"}}}return Promise.reject(e)})}async refreshToken(e){return this.client.post("/auth/refresh/",{refresh:e})}async get(e,t){try{let r=await this.client.get(e,t);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}async post(e,t,r){try{let s=await this.client.post(e,t,r);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async put(e,t,r){try{let s=await this.client.put(e,t,r);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async patch(e,t,r){try{let s=await this.client.patch(e,t,r);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async delete(e,t){try{let r=await this.client.delete(e,t);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}handleError(e){if(e&&"object"==typeof e&&"response"in e){if(e.response){var t,r,s,o,a,n,c;return{success:!1,error:{message:(null==(r=e.response.data)||null==(t=r.error)?void 0:t.message)||(null==(s=e.response.data)?void 0:s.message)||"An error occurred",code:(null==(a=e.response.data)||null==(o=a.error)?void 0:o.code)||"api_error",status_code:e.response.status,details:(null==(c=e.response.data)||null==(n=c.error)?void 0:n.details)||e.response.data}}}if(e.request)return{success:!1,error:{message:"Network error. Please check your connection.",code:"network_error",status_code:0}}}return{success:!1,error:{message:e instanceof Error?e.message:"An unexpected error occurred",code:"unknown_error",status_code:0}}}constructor(){this.client=s.A.create({baseURL:o.JR,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let c=new n,{get:i,post:l,put:d,patch:u,delete:E}=c},2975:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},6039:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2975,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,9700))},6389:(e,t,r)=>{"use strict";r.d(t,{B0:()=>c,Ei:()=>o,F$:()=>n,Hp:()=>a,Ss:()=>i,l$:()=>s});let s={HOME:"/",PRODUCTS:"/products",PRODUCT_DETAIL:e=>"/products/".concat(e),CART:"/cart",CHECKOUT:"/checkout",SEARCH:"/search",ABOUT:"/about",CONTACT:"/contact",TERMS:"/terms",PRIVACY:"/privacy",FAQ:"/faq"},o={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email"},a={DASHBOARD:"/profile",ORDERS:"/profile/orders",ORDER_DETAIL:e=>"/profile/orders/".concat(e),ADDRESSES:"/profile/addresses",WISHLIST:"/profile/wishlist",SETTINGS:"/profile/settings",NOTIFICATIONS:"/profile/notifications"},n={DASHBOARD:"/admin",ANALYTICS:"/admin/analytics",ORDERS:"/admin/orders",ORDER_DETAIL:e=>"/admin/orders/".concat(e),PRODUCTS:"/admin/products",PRODUCT_EDIT:e=>"/admin/products/".concat(e,"/edit"),PRODUCT_CREATE:"/admin/products/create",CUSTOMERS:"/admin/customers",CUSTOMER_DETAIL:e=>"/admin/customers/".concat(e),CONTENT:"/admin/content",REPORTS:"/admin/reports",SYSTEM:"/admin/system",NOTIFICATIONS:"/admin/notifications",SETTINGS:"/admin/settings"},c={DASHBOARD:"/seller/dashboard",PRODUCTS:"/seller/products",PRODUCT_EDIT:e=>"/seller/products/".concat(e,"/edit"),PRODUCT_CREATE:"/seller/products/create",ORDERS:"/seller/orders",ORDER_DETAIL:e=>"/seller/orders/".concat(e),PROFILE:"/seller/profile",KYC:"/seller/kyc",BANK_ACCOUNTS:"/seller/bank-accounts",PAYOUTS:"/seller/payouts",ANALYTICS:"/seller/analytics",SETTINGS:"/seller/settings"},i={"/":"Home","/products":"Products","/cart":"Shopping Cart","/checkout":"Checkout","/search":"Search","/about":"About Us","/contact":"Contact Us","/terms":"Terms of Service","/privacy":"Privacy Policy","/faq":"FAQ","/auth/login":"Login","/auth/register":"Register","/auth/forgot-password":"Forgot Password","/auth/reset-password":"Reset Password","/auth/verify-email":"Verify Email","/profile":"My Account","/profile/orders":"My Orders","/profile/addresses":"My Addresses","/profile/wishlist":"My Wishlist","/profile/settings":"Account Settings","/profile/notifications":"Notifications","/admin":"Admin Dashboard","/admin/analytics":"Analytics","/admin/orders":"Orders Management","/admin/products":"Products Management","/admin/products/create":"Create Product","/admin/customers":"Customers Management","/admin/content":"Content Management","/admin/reports":"Reports","/admin/system":"System Health","/admin/notifications":"Notifications","/admin/settings":"Admin Settings","/seller/dashboard":"Seller Dashboard","/seller/products":"My Products","/seller/products/create":"Add New Product","/seller/orders":"My Orders","/seller/profile":"Seller Profile","/seller/kyc":"KYC Verification","/seller/bank-accounts":"Bank Accounts","/seller/payouts":"Payouts","/seller/analytics":"Sales Analytics","/seller/settings":"Seller Settings"}},7141:(e,t,r)=>{"use strict";r.d(t,{Cy:()=>c,Hp:()=>s.Hp,JR:()=>o,Sn:()=>a,bw:()=>l,d5:()=>n,oO:()=>d,w8:()=>i});var s=r(6389);let o="http://localhost:8000/api/v1",a={AUTH:{LOGIN:"/auth/login/",REGISTER:"/auth/register/",LOGOUT:"/auth/logout/",REFRESH:"/auth/refresh/",PROFILE:"/auth/profile/",FORGOT_PASSWORD:"/auth/forgot-password/",RESET_PASSWORD:"/auth/reset-password/",VALIDATE_RESET_TOKEN:e=>"/auth/validate-reset-token/".concat(e,"/")},PRODUCTS:{LIST:"/products/",DETAIL:e=>"/products/".concat(e,"/"),CATEGORIES:"/products/categories/"},CART:{LIST:"/cart/",ADD:"/cart/add/",UPDATE:e=>"/cart/".concat(e,"/"),REMOVE:e=>"/cart/".concat(e,"/")},ORDERS:{LIST:"/orders/",DETAIL:e=>"/orders/".concat(e,"/"),CREATE:"/orders/",CANCEL:e=>"/orders/".concat(e,"/cancel/"),TIMELINE:e=>"/orders/".concat(e,"/timeline/"),INVOICE:e=>"/orders/".concat(e,"/invoice/"),DOWNLOAD_INVOICE:e=>"/orders/".concat(e,"/download_invoice/")},RETURNS:{LIST:"/return-requests/",DETAIL:e=>"/return-requests/".concat(e,"/"),CREATE:"/return-requests/"},REPLACEMENTS:{LIST:"/replacements/",DETAIL:e=>"/replacements/".concat(e,"/"),CREATE:"/replacements/",UPDATE_STATUS:e=>"/replacements/".concat(e,"/update_status/")},SEARCH:{PRODUCTS:"/search/products/",SUGGESTIONS:"/search/suggestions/",FILTERS:"/search/filters/",POPULAR:"/search/popular/",RELATED:"/search/related/"},CUSTOMER:{PROFILE:"/customer/profile/",ADDRESSES:"/customer/addresses/",ADDRESS_DETAIL:e=>"/customer/addresses/".concat(e,"/"),PREFERENCES:"/customer/preferences/"},WISHLIST:{LIST:"/wishlist/",ADD:"/wishlist/add/",REMOVE:e=>"/wishlist/".concat(e,"/"),CLEAR:"/wishlist/clear/"},PAYMENTS:{METHODS:"/payments/methods/",CURRENCIES:"/payments/currencies/",CREATE:"/payments/create/",VERIFY:"/payments/verify/",STATUS:e=>"/payments/".concat(e,"/status/"),WALLET:"/payments/wallet/",GIFT_CARD:{VALIDATE:"/payments/gift-card/validate/",BALANCE:e=>"/payments/gift-card/".concat(e,"/balance/")},CONVERT_CURRENCY:"/payments/convert-currency/"},ADMIN:{DASHBOARD:"/admin/dashboard/",ANALYTICS:"/admin/analytics/",PRODUCTS:"/admin/products/",ORDERS:"/admin/orders/",CUSTOMERS:"/admin/customers/",SETTINGS:"/admin/settings/"},SELLER:{DASHBOARD:"/seller/dashboard/",PRODUCTS:"/seller/products/",ORDERS:"/seller/orders/",PROFILE:"/seller/profile/",KYC:"/seller/kyc/",PAYOUTS:"/seller/payouts/"}},n={ACCESS_TOKEN:"access_token",REFRESH_TOKEN:"refresh_token",USER:"user",CART:"cart"},c={CUSTOMER:"customer",SELLER:"seller",ADMIN:"admin"},i={PENDING:"PENDING",CONFIRMED:"CONFIRMED",PROCESSING:"PROCESSING",SHIPPED:"SHIPPED",DELIVERED:"DELIVERED",CANCELLED:"CANCELLED",RETURNED:"RETURNED"},l={...s.l$,...s.Ei,PROFILE:s.Hp.DASHBOARD,ORDERS:s.Hp.ORDERS,PROFILE_ADDRESSES:s.Hp.ADDRESSES,PROFILE_WISHLIST:s.Hp.WISHLIST,PROFILE_PREFERENCES:s.Hp.SETTINGS,ADMIN:s.F$.DASHBOARD,SELLER:s.B0.DASHBOARD},d={PASSWORD_MIN_LENGTH:8,USERNAME_MIN_LENGTH:3,PHONE_REGEX:/^[\+]?[1-9][\d]{0,15}$/,EMAIL_REGEX:/^[^\s@]+@[^\s@]+\.[^\s@]+$/}},9042:(e,t,r)=>{"use strict";r.d(t,{Q:()=>d,W:()=>u});var s=r(5155),o=r(2115);function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite",r=document.getElementById("accessibility-announce-".concat(t));r||((r=document.createElement("div")).id="accessibility-announce-".concat(t),r.setAttribute("aria-live",t),r.setAttribute("aria-relevant","additions"),r.setAttribute("aria-atomic","true"),Object.entries({position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}).forEach(e=>{let[t,s]=e;r.style[t]=s}),document.body.appendChild(r)),r.textContent=e}let n=(0,o.createContext)(void 0),c="ecommerce-font-size",i="ecommerce-high-contrast",l="ecommerce-reduce-motion";function d(e){let{children:t}=e,[r,d]=(0,o.useState)(!1),[u,E]=(0,o.useState)(16),[S,m]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{let e=localStorage.getItem(c);e&&E(parseInt(e,10));let t=localStorage.getItem(i);t?d("true"===t):d(window.matchMedia("(prefers-contrast: more)").matches);let r=localStorage.getItem(l);r?m("true"===r):m(window.matchMedia("(prefers-reduced-motion: reduce)").matches)},[]),(0,o.useEffect)(()=>{r?document.documentElement.classList.add("high-contrast"):document.documentElement.classList.remove("high-contrast"),localStorage.setItem(i,String(r))},[r]),(0,o.useEffect)(()=>{document.documentElement.style.fontSize="".concat(u,"px"),localStorage.setItem(c,String(u))},[u]),(0,o.useEffect)(()=>{S?document.documentElement.classList.add("reduce-motion"):document.documentElement.classList.remove("reduce-motion"),localStorage.setItem(l,String(S))},[S]),(0,s.jsx)(n.Provider,{value:{highContrast:r,toggleHighContrast:()=>{d(e=>!e)},fontSize:u,increaseFontSize:()=>{E(e=>Math.min(e+1,24))},decreaseFontSize:()=>{E(e=>Math.max(e-1,12))},resetFontSize:()=>{E(16)},reduceMotion:S,toggleReduceMotion:()=>{m(e=>!e)},announce:a},children:t})}function u(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAccessibility must be used within an AccessibilityProvider");return e}},9700:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>E});var s=r(5155),o=r(4540),a=r(3568),n=r(6322),c=r(2115),i=r(9519);function l(e){let{children:t}=e,r=(0,n.jL)();return(0,c.useEffect)(()=>{r((0,i.Nu)())},[r]),(0,s.jsx)(s.Fragment,{children:t})}var d=r(9042);let u=(0,c.lazy)(()=>r.e(9516).then(r.bind(r,9516)).then(e=>({default:e.AccessibilityMenu})));function E(e){let{children:t}=e;return(0,c.useEffect)(()=>(function(){if("undefined"==typeof PerformanceObserver)return()=>{};try{let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{console.warn("[Long Task] Duration: ".concat(e.duration.toFixed(2),"ms"),e)})});return e.observe({entryTypes:["longtask"]}),()=>e.disconnect()}catch(e){return console.error("Failed to observe long tasks:",e),()=>{}}})(),[]),(0,s.jsx)(o.Kq,{store:n.M_,children:(0,s.jsx)(l,{children:(0,s.jsxs)(d.Q,{children:[t,(0,s.jsx)(a.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}),(0,s.jsx)(c.Suspense,{fallback:null,children:(0,s.jsx)(u,{})})]})})})}}},e=>{e.O(0,[3444,3464,4540,1990,3568,6322,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=6039)),_N_E=e.O()}]);