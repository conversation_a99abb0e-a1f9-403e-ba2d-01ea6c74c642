(()=>{var a={};a.id=279,a.ids=[279],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4145:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(60687),e=c(43210),f=c(16189),g=c(54864),h=c(68991),i=c(53593),j=c(69351),k=c(30474);let l=({onMethodSelect:a})=>{let b=(0,h.jL)(),{paymentMethods:c,selectedPaymentMethod:f,loading:i,error:l}=(0,g.d4)(a=>a.payments);(0,e.useEffect)(()=>{b((0,j.v4)())},[b]);let m=c=>{b((0,j.BG)(c)),a&&a(c)},n=c.reduce((a,b)=>{let c=b.method_type;return a[c]||(a[c]=[]),a[c].push(b),a},{}),o={CARD:"Credit/Debit Cards",UPI:"UPI Payment",WALLET:"Digital Wallets",NETBANKING:"Net Banking",COD:"Cash on Delivery",GIFT_CARD:"Gift Cards",IMPS:"IMPS Transfer",RTGS:"RTGS Transfer",NEFT:"NEFT Transfer"};return i?(0,d.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,d.jsx)("div",{className:"animate-pulse flex space-x-4",children:(0,d.jsxs)("div",{className:"flex-1 space-y-4 py-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]})]})})}):l?(0,d.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,d.jsxs)("p",{children:["Error loading payment methods: ",l]}),(0,d.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",onClick:()=>b((0,j.v4)()),children:"Retry"})]}):0===c.length?(0,d.jsx)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:(0,d.jsx)("p",{children:"No payment methods available at the moment. Please try again later."})}):(0,d.jsxs)("div",{className:"payment-methods-container",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Select Payment Method"}),Object.entries(n).map(([a,b])=>(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h4",{className:"text-md font-medium mb-2",children:o[a]||a}),(0,d.jsx)("div",{className:"space-y-2",children:b.map(a=>(0,d.jsxs)("div",{className:`
                  p-4 border rounded-lg cursor-pointer transition-all
                  ${f===a.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-blue-300 hover:bg-gray-50"}
                `,onClick:()=>m(a.id),children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-8 h-8 mr-3",children:a.icon?(0,d.jsx)(k.default,{src:a.icon,alt:a.name,width:32,height:32,className:"object-contain"}):(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-xs",children:a.name.substring(0,2)})})}),(0,d.jsxs)("div",{className:"flex-grow",children:[(0,d.jsx)("h5",{className:"font-medium",children:a.name}),a.description&&(0,d.jsx)("p",{className:"text-sm text-gray-500",children:a.description})]}),(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("input",{type:"radio",checked:f===a.id,onChange:()=>m(a.id),className:"h-5 w-5 text-blue-600"})})]}),a.processing_fee_percentage>0||a.processing_fee_fixed>0?(0,d.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["Processing fee:",a.processing_fee_percentage>0&&(0,d.jsxs)("span",{children:[" ",a.processing_fee_percentage,"%"]}),a.processing_fee_percentage>0&&a.processing_fee_fixed>0&&(0,d.jsx)("span",{children:" + "}),a.processing_fee_fixed>0&&(0,d.jsxs)("span",{children:["$",a.processing_fee_fixed.toFixed(2)]})]}):null]},a.id))})]},a))]})},m=({amount:a,onCurrencyChange:b})=>{let c=(0,h.jL)(),{currencies:f,selectedCurrency:i,loading:k,error:l,currencyConversion:m}=(0,g.d4)(a=>a.payments);if((0,e.useEffect)(()=>{c((0,j.CW)())},[c]),(0,e.useEffect)(()=>{i&&a>0&&c((0,j.hZ)({from_currency:"USD",to_currency:i,amount:a}))},[c,i,a]),(0,e.useEffect)(()=>{m&&b&&b(m.to_currency,m.converted_amount)},[m,b]),k&&0===f.length)return(0,d.jsx)("div",{className:"animate-pulse",children:(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32"})});if(l&&0===f.length)return(0,d.jsx)("div",{className:"text-red-500 text-sm",children:"Error loading currencies"});let n=f.find(a=>a.code===i);return(0,d.jsxs)("div",{className:"currency-selector",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("select",{value:i,onChange:a=>{let b=a.target.value;c((0,j.$f)(b))},className:"form-select rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",disabled:k,children:f.map(a=>(0,d.jsxs)("option",{value:a.code,children:[a.code," (",a.symbol,")"]},a.code))}),m?(0,d.jsx)("div",{className:"text-sm text-gray-500",children:(0,d.jsxs)("span",{children:["Exchange rate: 1 USD = ",n?.exchange_rate?.toFixed(4)," ",i]})}):null]}),m?(0,d.jsx)("div",{className:"mt-2 text-sm",children:(0,d.jsxs)("span",{className:"font-medium",children:[m.amount?.toFixed(2)," USD ="," ",n?.symbol,m.converted_amount?.toFixed(2)," ",i]})}):null]})},n=({amount:a,onProceed:b})=>{let{selectedCurrency:c,currencies:f}=(0,g.d4)(a=>a.payments),[h,i]=(0,e.useState)({cardNumber:"",cardholderName:"",expiryMonth:"",expiryYear:"",cvv:"",saveCard:!1}),[j,k]=(0,e.useState)({}),l=a=>{let{name:b,value:c,type:d}=a.target,e="checkbox"===d?a.target.checked:void 0;i(a=>({...a,[b]:"checkbox"===d?e:c})),j[b]&&k(a=>({...a,[b]:void 0}))},m=Array.from({length:12},(a,b)=>{let c=(b+1).toString().padStart(2,"0");return(0,d.jsx)("option",{value:c,children:c},c)}),n=new Date().getFullYear(),o=Array.from({length:11},(a,b)=>{let c=n+b;return(0,d.jsx)("option",{value:c,children:c},c)}),p=f.find(a=>a.code===c)||{code:"USD",symbol:"$"};return(0,d.jsxs)("div",{className:"credit-card-payment p-4 border rounded-lg",children:[(0,d.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Credit/Debit Card Payment"}),(0,d.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Payment Amount:"}),(0,d.jsxs)("span",{className:"font-medium",children:[p.symbol,a.toFixed(2)," ",p.code]})]})}),(0,d.jsxs)("form",{onSubmit:a=>{a.preventDefault(),(()=>{let a={},b=h.cardNumber.replace(/\D/g,"");return(!b||b.length<13||b.length>19)&&(a.cardNumber="Please enter a valid card number"),h.cardholderName.trim()||(a.cardholderName="Cardholder name is required"),h.expiryMonth||(a.expiryMonth="Required"),h.expiryYear||(a.expiryYear="Required"),(!h.cvv||h.cvv.length<3||h.cvv.length>4)&&(a.cvv="Invalid CVV"),k(a),0===Object.keys(a).length})()&&b(h)},children:[(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("label",{htmlFor:"cardNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Card Number"}),(0,d.jsx)("input",{id:"cardNumber",name:"cardNumber",type:"text",value:h.cardNumber,onChange:a=>{let b=(a=>{let b=a.replace(/\D/g,""),c="";for(let a=0;a<b.length;a+=4)c+=b.slice(a,a+4)+" ";return c.trim()})(a.target.value);i(a=>({...a,cardNumber:b})),j.cardNumber&&k(a=>({...a,cardNumber:void 0}))},placeholder:"1234 5678 9012 3456",maxLength:19,className:`w-full form-input rounded-md ${j.cardNumber?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"} focus:ring focus:ring-opacity-50`}),j.cardNumber&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.cardNumber})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("label",{htmlFor:"cardholderName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cardholder Name"}),(0,d.jsx)("input",{id:"cardholderName",name:"cardholderName",type:"text",value:h.cardholderName,onChange:l,placeholder:"John Doe",className:`w-full form-input rounded-md ${j.cardholderName?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"} focus:ring focus:ring-opacity-50`}),j.cardholderName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.cardholderName})]}),(0,d.jsxs)("div",{className:"flex mb-4 space-x-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{htmlFor:"expiryMonth",className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("select",{id:"expiryMonth",name:"expiryMonth",value:h.expiryMonth,onChange:l,className:`w-full form-select rounded-md ${j.expiryMonth?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"} focus:ring focus:ring-opacity-50`,children:[(0,d.jsx)("option",{value:"",children:"Month"}),m]}),(0,d.jsxs)("select",{id:"expiryYear",name:"expiryYear",value:h.expiryYear,onChange:l,className:`w-full form-select rounded-md ${j.expiryYear?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"} focus:ring focus:ring-opacity-50`,children:[(0,d.jsx)("option",{value:"",children:"Year"}),o]})]}),(j.expiryMonth||j.expiryYear)&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Please select a valid expiry date"})]}),(0,d.jsxs)("div",{className:"w-1/3",children:[(0,d.jsx)("label",{htmlFor:"cvv",className:"block text-sm font-medium text-gray-700 mb-1",children:"CVV"}),(0,d.jsx)("input",{id:"cvv",name:"cvv",type:"password",value:h.cvv,onChange:l,placeholder:"123",maxLength:4,className:`w-full form-input rounded-md ${j.cvv?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"} focus:ring focus:ring-opacity-50`}),j.cvv&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.cvv})]})]}),(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{id:"saveCard",name:"saveCard",type:"checkbox",checked:h.saveCard,onChange:l,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"saveCard",className:"ml-2 block text-sm text-gray-700",children:"Save card for future payments"})]})}),(0,d.jsxs)("button",{type:"submit",className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:["Pay ",p.symbol,a.toFixed(2)]})]})]})},o=({amount:a,onProceed:b})=>{let c=(0,h.jL)(),{wallet:f,loading:i,error:k}=(0,g.d4)(a=>a.payments);(0,e.useEffect)(()=>{c((0,j.OS)())},[c]);let l=()=>{b()},m=f&&f.balance>=a;return i?(0,d.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,d.jsx)("div",{className:"animate-pulse flex space-x-4",children:(0,d.jsxs)("div",{className:"flex-1 space-y-4 py-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]})})}):k?(0,d.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,d.jsxs)("p",{children:["Error loading wallet: ",k]}),(0,d.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",onClick:()=>c((0,j.OS)()),children:"Retry"})]}):f?(0,d.jsxs)("div",{className:"wallet-payment p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h4",{className:"text-lg font-medium",children:"Your Wallet"}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Available Balance"}),(0,d.jsxs)("div",{className:"text-xl font-bold",children:[f.currency.symbol,f.balance.toFixed(2)," ",f.currency.code]})]})]}),(0,d.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Payment Amount:"}),(0,d.jsxs)("span",{className:"font-medium",children:[f.currency.symbol,a.toFixed(2)]})]}),(0,d.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,d.jsx)("span",{children:"Balance After Payment:"}),(0,d.jsxs)("span",{className:`font-medium ${m?"text-green-600":"text-red-600"}`,children:[f.currency.symbol,Math.max(0,f.balance-a).toFixed(2)]})]})]}),!m&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 rounded-md",children:(0,d.jsx)("p",{children:"Insufficient wallet balance. Please add funds or choose another payment method."})}),(0,d.jsx)("button",{className:`w-full py-2 rounded-md transition-colors ${m?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,onClick:l,disabled:!m,children:m?"Pay from Wallet":"Insufficient Balance"})]}):(0,d.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,d.jsx)("p",{children:"You don't have a wallet yet. A wallet will be created for you when you proceed."}),(0,d.jsx)("button",{className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",onClick:l,children:"Proceed with Payment"})]})},p=({amount:a,onProceed:b})=>{let c=(0,h.jL)(),{giftCard:f,loading:i,error:k}=(0,g.d4)(a=>a.payments),[l,m]=(0,e.useState)(""),n=f&&f.current_balance>=a;return(0,d.jsxs)("div",{className:"gift-card-payment p-4 border rounded-lg",children:[(0,d.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Gift Card Payment"}),f?(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"mb-4 p-3 bg-green-50 rounded-md",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-green-700",children:"Valid Gift Card"}),(0,d.jsx)("button",{onClick:()=>{c((0,j.ON)()),m("")},className:"text-sm text-blue-600 hover:text-blue-800",children:"Change"})]}),(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Card Number"}),(0,d.jsx)("div",{className:"font-medium",children:f.code})]}),(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Available Balance"}),(0,d.jsxs)("div",{className:"font-bold",children:[f.currency.symbol,f.current_balance.toFixed(2)," ",f.currency.code]})]}),(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Expires On"}),(0,d.jsx)("div",{children:new Date(f.expiry_date).toLocaleDateString()})]})]}),(0,d.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Payment Amount:"}),(0,d.jsxs)("span",{className:"font-medium",children:[f.currency.symbol,a.toFixed(2)]})]}),(0,d.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,d.jsx)("span",{children:"Balance After Payment:"}),(0,d.jsxs)("span",{className:`font-medium ${n?"text-green-600":"text-red-600"}`,children:[f.currency.symbol,Math.max(0,f.current_balance-a).toFixed(2)]})]})]}),!n&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 rounded-md",children:(0,d.jsx)("p",{children:"Insufficient gift card balance. Please use another gift card or choose another payment method."})}),(0,d.jsx)("button",{className:`w-full py-2 rounded-md transition-colors ${n?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,onClick:()=>{f&&f.current_balance>=a&&b(f.code)},disabled:!n,children:n?"Pay with Gift Card":"Insufficient Balance"})]}):(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("label",{htmlFor:"gift-card-code",className:"block text-sm font-medium text-gray-700 mb-1",children:"Enter Gift Card Code"}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("input",{id:"gift-card-code",type:"text",value:l,onChange:a=>m(a.target.value),placeholder:"XXXX-XXXX-XXXX-XXXX",className:"flex-grow form-input rounded-l-md border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",disabled:i}),(0,d.jsx)("button",{onClick:()=>{l.trim()&&c((0,j.Pw)(l.trim()))},disabled:!l.trim()||i,className:`px-4 py-2 rounded-r-md ${!l.trim()||i?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:i?"Validating...":"Validate"})]}),k&&(0,d.jsx)("p",{className:"mt-2 text-sm text-red-600",children:k})]})})]})},q=({orderId:a,amount:b,onSuccess:c,onFailure:f,onCancel:i})=>{let k=(0,h.jL)(),{selectedCurrency:l,selectedPaymentMethod:m,currentPayment:n,paymentProcessing:o,paymentSuccess:p,paymentError:q,giftCard:r}=(0,g.d4)(a=>a.payments),[s,t]=(0,e.useState)(!1),[u,v]=(0,e.useState)(null),[w,x]=(0,e.useState)(null);return((0,e.useEffect)(()=>()=>{w&&clearInterval(w),k((0,j.vg)())},[k,w]),(0,e.useEffect)(()=>{p&&n&&(w&&(clearInterval(w),x(null)),c(n.id))},[p,n,w,c]),(0,e.useEffect)(()=>{q&&!o&&f(q)},[q,o,f]),s&&o)?(0,d.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Processing Payment"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Please wait while we process your payment..."})]})}):u?(0,d.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Verifying Payment"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Please wait while we verify your payment..."})]})}):p&&n?(0,d.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-12 w-12 rounded-full bg-green-100",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-green-800 mb-2",children:"Payment Successful"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Your payment has been processed successfully."}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["Payment ID: ",n.id]})]})}):q?(0,d.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-12 w-12 rounded-full bg-red-100",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-red-800 mb-2",children:"Payment Failed"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:q}),(0,d.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>k((0,j.vg)()),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Try Again"}),(0,d.jsx)("button",{onClick:i,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300",children:"Cancel"})]})]})}):null},r=({orderId:a,amount:b,onPaymentSuccess:c,onPaymentFailure:f,onCancel:i})=>{let k=(0,h.jL)(),{paymentMethods:r,selectedPaymentMethod:s,selectedCurrency:t,loading:u}=(0,g.d4)(a=>a.payments),[v,w]=(0,e.useState)(b),[x,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)(null);(0,e.useEffect)(()=>{k((0,j.v4)()),k((0,j.CW)())},[k]);let B=a=>{A({type:"card",data:a}),y(!0)},C=()=>{A({type:"wallet"}),y(!0)},D=a=>{A({type:"gift_card",data:{code:a}}),y(!0)},E=r.find(a=>a.id===s);return u&&0===r.length?(0,d.jsx)("div",{className:"checkout-payment p-6",children:(0,d.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-40 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-40 bg-gray-200 rounded"})]})}):(0,d.jsx)("div",{className:"checkout-payment",children:x?(0,d.jsx)(q,{orderId:a,amount:v,onSuccess:c,onFailure:f,onCancel:()=>{y(!1),A(null)}}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Payment"}),(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(m,{amount:b,onCurrencyChange:(a,b)=>{w(b)}})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"md:col-span-1",children:(0,d.jsx)(l,{onMethodSelect:a=>{A(null),y(!1)}})}),(0,d.jsx)("div",{className:"md:col-span-2",children:(()=>{if(!E)return null;switch(E.method_type){case"CARD":return(0,d.jsx)(n,{amount:v,onProceed:B});case"WALLET":return(0,d.jsx)(o,{amount:v,onProceed:C});case"GIFT_CARD":return(0,d.jsx)(p,{amount:v,onProceed:D});case"COD":return(0,d.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,d.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Cash on Delivery"}),(0,d.jsxs)("p",{className:"mb-4",children:["You will pay ",t," ",v.toFixed(2)," when your order is delivered."]}),(0,d.jsx)("button",{onClick:()=>{A({type:"cod"}),y(!0)},className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Place Order with COD"})]});default:return(0,d.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,d.jsx)("h4",{className:"text-lg font-medium mb-4",children:E.name}),(0,d.jsx)("p",{className:"mb-4",children:"Please proceed to complete your payment."}),(0,d.jsx)("button",{onClick:()=>{A({type:E.method_type.toLowerCase()}),y(!0)},className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Proceed to Payment"})]})}})()})]}),(0,d.jsx)("div",{className:"mt-6 flex justify-between",children:(0,d.jsx)("button",{onClick:i,className:"px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors",children:"Back"})})]})})};var s=c(7791);let t=a=>({id:a.id||"",type:a.type||"HOME",is_default:a.is_default||!1,first_name:a.first_name||"",last_name:a.last_name||"",address_line_1:a.address_line_1||"",address_line_2:a.address_line_2,city:a.city||"",state:a.state||"",postal_code:a.postal_code||"",country:a.country||""});var u=function(a){return a.SHIPPING="shipping",a.PAYMENT="payment",a.CONFIRMATION="confirmation",a}(u||{});let v=()=>{let a=(0,f.useRouter)(),b=(0,h.jL)(),{items:c}=(0,g.d4)(a=>a.cart),{currentOrder:j,loading:k,error:l}=(0,g.d4)(a=>a.orders),[m,n]=(0,e.useState)("shipping"),[o,p]=(0,e.useState)(null),[q,u]=(0,e.useState)(null),[v,w]=(0,e.useState)(""),[x,y]=(0,e.useState)("");(0,e.useEffect)(()=>{c&&0!==c.length||a.push(s.bw.CART)},[c,a]);let z=a=>{y(a),n("confirmation")},A=a=>{console.error("Payment failed:",a)};return k?(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-60 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-40 bg-gray-200 rounded"})]})}):l?(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,d.jsxs)("p",{children:["Error: ",l]}),(0,d.jsx)("button",{onClick:()=>a.push(s.bw.CART),className:"mt-4 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Return to Cart"})]})}):(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-center",children:[(0,d.jsxs)("div",{className:`step-item ${"shipping"===m?"active":"completed"}`,children:[(0,d.jsx)("div",{className:"step-circle",children:"1"}),(0,d.jsx)("div",{className:"step-text",children:"Shipping"})]}),(0,d.jsx)("div",{className:"step-line"}),(0,d.jsxs)("div",{className:`step-item ${"payment"===m?"active":"confirmation"===m?"completed":""}`,children:[(0,d.jsx)("div",{className:"step-circle",children:"2"}),(0,d.jsx)("div",{className:"step-text",children:"Payment"})]}),(0,d.jsx)("div",{className:"step-line"}),(0,d.jsxs)("div",{className:`step-item ${"confirmation"===m?"active":""}`,children:[(0,d.jsx)("div",{className:"step-circle",children:"3"}),(0,d.jsx)("div",{className:"step-text",children:"Confirmation"})]})]})}),(0,d.jsx)("div",{className:"max-w-4xl mx-auto",children:(()=>{switch(m){case"shipping":default:return(0,d.jsxs)("div",{className:"shipping-step",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Shipping Information"}),(0,d.jsxs)("div",{className:"mb-6 p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,d.jsx)("p",{children:"This is a placeholder for the shipping form."}),(0,d.jsx)("p",{children:"In a real implementation, this would include address fields, shipping method selection, etc."})]}),(0,d.jsx)("button",{onClick:()=>{p(t({first_name:"John",last_name:"Doe",address_line_1:"123 Main St",city:"Anytown",state:"CA",postal_code:"12345",country:"US"})),u(t({first_name:"John",last_name:"Doe",address_line_1:"123 Main St",city:"Anytown",state:"CA",postal_code:"12345",country:"US"})),w("standard"),(()=>{if(!o||!v)return;let a={shipping_address:o,billing_address:q||o,shipping_method:v,items:c?.map(a=>({product_id:a.product.id,quantity:a.quantity}))||[]};b((0,i.fS)(a))})(),n("payment")},className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Continue to Payment"})]});case"payment":return j?(0,d.jsx)(r,{orderId:j.id,amount:j.total_amount,onPaymentSuccess:z,onPaymentFailure:A,onCancel:()=>n("shipping")}):(0,d.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,d.jsx)("p",{children:"Order information is not available. Please go back to the shipping step."}),(0,d.jsx)("button",{onClick:()=>n("shipping"),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Back to Shipping"})]});case"confirmation":return j?(0,d.jsxs)("div",{className:"confirmation-step",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-16 w-16 rounded-full bg-green-100",children:(0,d.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-green-800 mb-2",children:"Order Confirmed!"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Thank you for your purchase."})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Order Summary"}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("p",{className:"text-gray-600",children:["Order Number: ",(0,d.jsx)("span",{className:"font-medium",children:j.order_number})]}),(0,d.jsxs)("p",{className:"text-gray-600",children:["Payment ID: ",(0,d.jsx)("span",{className:"font-medium",children:x})]})]}),(0,d.jsxs)("div",{className:"border-t pt-4 mb-4",children:[(0,d.jsx)("h4",{className:"font-medium mb-2",children:"Items"}),(0,d.jsx)("ul",{className:"space-y-2",children:j?.items?.map(a=>(0,d.jsxs)("li",{className:"flex justify-between",children:[(0,d.jsxs)("span",{children:[a.product?.name," \xd7 ",a.quantity]}),(0,d.jsxs)("span",{className:"font-medium",children:["$",a.total_price?.toFixed(2)]})]},a.id))})]}),(0,d.jsxs)("div",{className:"border-t pt-4",children:[(0,d.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,d.jsx)("span",{children:"Subtotal"}),(0,d.jsxs)("span",{children:["$",((j?.total_amount||0)-(j?.shipping_amount||0)-(j?.tax_amount||0)+(j?.discount_amount||0)).toFixed(2)]})]}),(0,d.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,d.jsx)("span",{children:"Shipping"}),(0,d.jsxs)("span",{children:["$",(j?.shipping_amount||0).toFixed(2)]})]}),(0,d.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,d.jsx)("span",{children:"Tax"}),(0,d.jsxs)("span",{children:["$",(j?.tax_amount||0).toFixed(2)]})]}),(j?.discount_amount||0)>0&&(0,d.jsxs)("div",{className:"flex justify-between mb-2 text-green-600",children:[(0,d.jsx)("span",{children:"Discount"}),(0,d.jsxs)("span",{children:["-$",(j?.discount_amount||0).toFixed(2)]})]}),(0,d.jsxs)("div",{className:"flex justify-between font-bold text-lg mt-2 pt-2 border-t",children:[(0,d.jsx)("span",{children:"Total"}),(0,d.jsxs)("span",{children:["$",(j?.total_amount||0).toFixed(2)]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("button",{onClick:()=>a.push(s.Hp.ORDERS),className:"px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors",children:"View Orders"}),(0,d.jsx)("button",{onClick:()=>a.push(s.bw.HOME),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Continue Shopping"})]})]}):(0,d.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,d.jsx)("p",{children:"Order information is not available."}),(0,d.jsx)("button",{onClick:()=>a.push(s.bw.HOME),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Return to Home"})]})}})()})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38791:(a,b,c)=>{Promise.resolve().then(c.bind(c,54787))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45507:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,54787)),"C:\\Local_ecom\\frontend\\src\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Local_ecom\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Local_ecom\\frontend\\src\\app\\checkout\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/checkout/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},53038:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(43210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},54787:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Local_ecom\\\\frontend\\\\src\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\app\\checkout\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78111:(a,b,c)=>{Promise.resolve().then(c.bind(c,4145))},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,503,474,998],()=>b(b.s=45507));module.exports=c})();