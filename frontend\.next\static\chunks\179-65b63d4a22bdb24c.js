"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{179:(e,s,t)=>{t.d(s,{hR:()=>x,zh:()=>u,U5:()=>h,PL:()=>b,tl:()=>p,CP:()=>m,F7:()=>g,TJ:()=>i});var a=t(5155),r=t(2115),l=t(4540),n=t(5695),d=t(9404);let i=()=>{let e=(0,l.wA)(),s=(0,n.useRouter)(),{loading:t,error:i}=(0,l.d4)(e=>e.seller),[c,o]=(0,r.useState)({business_name:"",business_type:"INDIVIDUAL",tax_id:"",gstin:"",pan_number:"",description:"",address:"",city:"",state:"",country:"",postal_code:"",phone_number:"",email:"",website:""}),[m,u]=(0,r.useState)(null),[x,p]=(0,r.useState)(null),h=e=>{let{name:s,value:t}=e.target;o(e=>({...e,[s]:t}))},g=e=>{let{name:s,files:t}=e.target;t&&t.length>0&&("logo"===s?u(t[0]):"banner"===s&&p(t[0]))},b=async t=>{t.preventDefault();let a={...c,logo:m||void 0,banner:x||void 0},r=await e((0,d.xF)(a));d.xF.fulfilled.match(r)&&s.push("/seller/dashboard")};return(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Seller Registration"}),i&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:i}),(0,a.jsxs)("form",{onSubmit:b,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Business Information"})}),(0,a.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Name*"}),(0,a.jsx)("input",{type:"text",name:"business_name",value:c.business_name,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Type*"}),(0,a.jsxs)("select",{name:"business_type",value:c.business_type,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"INDIVIDUAL",children:"Individual"}),(0,a.jsx)("option",{value:"PARTNERSHIP",children:"Partnership"}),(0,a.jsx)("option",{value:"LLC",children:"Limited Liability Company"}),(0,a.jsx)("option",{value:"CORPORATION",children:"Corporation"}),(0,a.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tax ID"}),(0,a.jsx)("input",{type:"text",name:"tax_id",value:c.tax_id,onChange:h,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"GSTIN"}),(0,a.jsx)("input",{type:"text",name:"gstin",value:c.gstin,onChange:h,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PAN Number"}),(0,a.jsx)("input",{type:"text",name:"pan_number",value:c.pan_number,onChange:h,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Description"}),(0,a.jsx)("textarea",{name:"description",value:c.description,onChange:h,rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo"}),(0,a.jsx)("input",{type:"file",name:"logo",onChange:g,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner"}),(0,a.jsx)("input",{type:"file",name:"banner",onChange:g,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("h3",{className:"text-lg font-medium mb-4 mt-4",children:"Contact Information"})}),(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address*"}),(0,a.jsx)("textarea",{name:"address",value:c.address,onChange:h,required:!0,rows:2,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City*"}),(0,a.jsx)("input",{type:"text",name:"city",value:c.city,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State*"}),(0,a.jsx)("input",{type:"text",name:"state",value:c.state,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country*"}),(0,a.jsx)("input",{type:"text",name:"country",value:c.country,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code*"}),(0,a.jsx)("input",{type:"text",name:"postal_code",value:c.postal_code,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number*"}),(0,a.jsx)("input",{type:"tel",name:"phone_number",value:c.phone_number,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email*"}),(0,a.jsx)("input",{type:"email",name:"email",value:c.email,onChange:h,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website"}),(0,a.jsx)("input",{type:"url",name:"website",value:c.website,onChange:h,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)("button",{type:"submit",disabled:t,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:t?"Submitting...":"Register as Seller"})})]})]})};var c=t(6874),o=t.n(c);let m=()=>{let e=(0,l.wA)(),{profile:s,analytics:t,loading:n}=(0,l.d4)(e=>e.seller);return((0,r.useEffect)(()=>{e((0,d.b7)()),e((0,d.IZ)())},[e]),n)?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):s?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h2",{className:"text-2xl font-semibold mb-2",children:["Welcome, ",s.business_name,"!"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"VERIFIED"===s.verification_status?(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Your seller account is verified."}):"PENDING"===s.verification_status?(0,a.jsx)("span",{className:"text-yellow-600 font-medium",children:"Your seller account is pending verification."}):(0,a.jsx)("span",{className:"text-red-600 font-medium",children:"Your seller account verification has issues."})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Sales"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsxs)("span",{className:"text-3xl font-semibold",children:["₹",(null==t?void 0:t.total_sales.toLocaleString())||"0"]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Orders"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsx)("span",{className:"text-3xl font-semibold",children:(null==t?void 0:t.total_orders)||"0"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Products"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsx)("span",{className:"text-3xl font-semibold",children:(null==t?void 0:t.total_products)||"0"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Sales Overview"}),(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:(null==t?void 0:t.sales_by_period)?(0,a.jsx)("div",{className:"w-full h-full",children:(0,a.jsx)("div",{className:"flex h-full items-end justify-between",children:t.sales_by_period.map((e,s)=>(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"bg-blue-500 w-12",style:{height:"".concat(e.amount/Math.max(...t.sales_by_period.map(e=>e.amount))*100,"%"),minHeight:"10px"}}),(0,a.jsx)("span",{className:"text-xs mt-2",children:e.period})]},s))})}):(0,a.jsx)("p",{className:"text-gray-500",children:"No sales data available"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Recent Orders"})}),(0,a.jsx)("div",{className:"divide-y",children:(null==t?void 0:t.recent_orders)&&t.recent_orders.length>0?t.recent_orders.map(e=>(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:e.order_number}),(0,a.jsxs)("span",{className:"text-gray-600",children:["₹",e.total_amount.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("span",{className:"text-sm ".concat("DELIVERED"===e.status?"text-green-600":"CANCELLED"===e.status?"text-red-600":"text-yellow-600"),children:e.status})]})]},e.id)):(0,a.jsx)("div",{className:"px-6 py-4 text-center text-gray-500",children:"No recent orders"})}),(0,a.jsx)("div",{className:"px-6 py-3 bg-gray-50",children:(0,a.jsx)(o(),{href:"/seller/orders",children:(0,a.jsx)("a",{className:"text-sm text-blue-600 hover:text-blue-800",children:"View all orders"})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Top Products"})}),(0,a.jsx)("div",{className:"divide-y",children:(null==t?void 0:t.top_products)&&t.top_products.length>0?t.top_products.map(e=>(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-gray-600",children:["₹",e.sales.toLocaleString()]})]}),(0,a.jsx)("div",{className:"flex justify-between mt-1",children:(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.quantity," units sold"]})})]},e.id)):(0,a.jsx)("div",{className:"px-6 py-4 text-center text-gray-500",children:"No product data available"})}),(0,a.jsx)("div",{className:"px-6 py-3 bg-gray-50",children:(0,a.jsx)(o(),{href:"/seller/products",children:(0,a.jsx)("a",{className:"text-sm text-blue-600 hover:text-blue-800",children:"View all products"})})})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(o(),{href:"/seller/products/add",children:(0,a.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Add Product"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"List a new product for sale"})]})}),(0,a.jsx)(o(),{href:"/seller/kyc",children:(0,a.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,a.jsx)("h4",{className:"font-medium",children:"KYC Verification"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Complete your verification"})]})}),(0,a.jsx)(o(),{href:"/seller/bank-accounts",children:(0,a.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Bank Accounts"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Manage your payment methods"})]})}),(0,a.jsx)(o(),{href:"/seller/profile",children:(0,a.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Edit Profile"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Update your business details"})]})})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Seller Profile Not Found"}),(0,a.jsx)("p",{className:"mb-6",children:"You need to register as a seller to access the dashboard."}),(0,a.jsx)(o(),{href:"/seller/register",children:(0,a.jsx)("a",{className:"bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700",children:"Register as Seller"})})]})},u=()=>{let e=(0,l.wA)(),{kycDocuments:s,loading:t,error:n}=(0,l.d4)(e=>e.seller),[i,c]=(0,r.useState)({document_type:"ID_PROOF",document_number:"",document_file:{},document_name:"",issue_date:"",expiry_date:""}),[o,m]=(0,r.useState)(null),[u,x]=(0,r.useState)("");(0,r.useEffect)(()=>{e((0,d.Y0)())},[e]);let p=e=>{let{name:s,value:t}=e.target;c(e=>({...e,[s]:t}))},h=async s=>{if(s.preventDefault(),!o)return;let t={...i,document_file:o},a=await e((0,d.CO)(t));d.CO.fulfilled.match(a)&&(x("Document uploaded successfully!"),c({document_type:"ID_PROOF",document_number:"",document_file:{},document_name:"",issue_date:"",expiry_date:""}),m(null),setTimeout(()=>{x("")},3e3))};return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"KYC Verification"}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Upload KYC Document"}),n&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),u&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:u}),(0,a.jsxs)("form",{onSubmit:h,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Type*"}),(0,a.jsxs)("select",{name:"document_type",value:i.document_type,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ID_PROOF",children:"ID Proof"}),(0,a.jsx)("option",{value:"ADDRESS_PROOF",children:"Address Proof"}),(0,a.jsx)("option",{value:"BUSINESS_PROOF",children:"Business Proof"}),(0,a.jsx)("option",{value:"TAX_DOCUMENT",children:"Tax Document"}),(0,a.jsx)("option",{value:"BANK_STATEMENT",children:"Bank Statement"}),(0,a.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Name*"}),(0,a.jsx)("input",{type:"text",name:"document_name",value:i.document_name,onChange:p,required:!0,placeholder:"e.g., Aadhar Card, PAN Card",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Number*"}),(0,a.jsx)("input",{type:"text",name:"document_number",value:i.document_number,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document File*"}),(0,a.jsx)("input",{type:"file",name:"document_file",onChange:e=>{let{files:s}=e.target;s&&s.length>0&&m(s[0])},required:!0,accept:".pdf,.jpg,.jpeg,.png",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Accepted formats: PDF, JPG, JPEG, PNG (Max size: 5MB)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Issue Date"}),(0,a.jsx)("input",{type:"date",name:"issue_date",value:i.issue_date,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date"}),(0,a.jsx)("input",{type:"date",name:"expiry_date",value:i.expiry_date,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{type:"submit",disabled:t||!o,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:t?"Uploading...":"Upload Document"})})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Uploaded Documents"}),0===s.length?(0,a.jsx)("p",{className:"text-gray-500",children:"No documents uploaded yet."}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Type"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Number"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded On"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.document_type_display}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.document_name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.document_number}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat((e=>{switch(e){case"VERIFIED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"REJECTED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.verification_status)),children:e.verification_status_display}),e.verification_notes&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.verification_notes})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()})]},e.id))})]})})]})]})},x=()=>{let e=(0,l.wA)(),{bankAccounts:s,loading:t,error:n}=(0,l.d4)(e=>e.seller),[i,c]=(0,r.useState)({account_holder_name:"",bank_name:"",account_number:"",ifsc_code:"",branch_name:"",account_type:"SAVINGS",is_primary:!1}),[o,m]=(0,r.useState)(null),[u,x]=(0,r.useState)(""),[p,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e((0,d.Dz)())},[e]);let g=e=>{let{name:s,value:t,type:a}=e.target,r="checked"in e.target&&e.target.checked;c(e=>({...e,[s]:"checkbox"===a?r:t}))},b=async s=>{s.preventDefault();let t={...i,verification_document:o||void 0},a=await e((0,d.rf)(t));d.rf.fulfilled.match(a)&&(x("Bank account added successfully!"),c({account_holder_name:"",bank_name:"",account_number:"",ifsc_code:"",branch_name:"",account_type:"SAVINGS",is_primary:!1}),m(null),h(!1),setTimeout(()=>{x("")},3e3))},y=async s=>{await e((0,d.gZ)(s))};return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Bank Accounts"}),n&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),u&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:u}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Your Bank Accounts"}),(0,a.jsx)("button",{onClick:()=>h(!p),className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:p?"Cancel":"Add New Account"})]}),p&&(0,a.jsxs)("form",{onSubmit:b,className:"border-t pt-4 mt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Holder Name*"}),(0,a.jsx)("input",{type:"text",name:"account_holder_name",value:i.account_holder_name,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bank Name*"}),(0,a.jsx)("input",{type:"text",name:"bank_name",value:i.bank_name,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Number*"}),(0,a.jsx)("input",{type:"text",name:"account_number",value:i.account_number,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"IFSC Code*"}),(0,a.jsx)("input",{type:"text",name:"ifsc_code",value:i.ifsc_code,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Branch Name*"}),(0,a.jsx)("input",{type:"text",name:"branch_name",value:i.branch_name,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Type*"}),(0,a.jsxs)("select",{name:"account_type",value:i.account_type,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"SAVINGS",children:"Savings"}),(0,a.jsx)("option",{value:"CURRENT",children:"Current/Checking"}),(0,a.jsx)("option",{value:"BUSINESS",children:"Business"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Verification Document"}),(0,a.jsx)("input",{type:"file",name:"verification_document",onChange:e=>{let{files:s}=e.target;s&&s.length>0&&m(s[0])},accept:".pdf,.jpg,.jpeg,.png",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a cancelled cheque or bank statement (PDF, JPG, JPEG, PNG)"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",name:"is_primary",id:"is_primary",checked:i.is_primary,onChange:g,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"is_primary",className:"ml-2 block text-sm text-gray-700",children:"Set as primary account"})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{type:"submit",disabled:t,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:t?"Adding...":"Add Bank Account"})})]}),!p&&(0,a.jsx)("div",{className:"mt-4",children:0===s.length?(0,a.jsx)("p",{className:"text-gray-500",children:"No bank accounts added yet."}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Number"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Type"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.is_primary&&(0,a.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2 py-0.5 rounded",children:"Primary"}),e.bank_name]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"•••• "+e.account_number.slice(-4)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.account_type_display}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat((e=>{switch(e){case"VERIFIED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"REJECTED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.verification_status)),children:e.verification_status_display})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:!e.is_primary&&(0,a.jsx)("button",{onClick:()=>y(e.id),disabled:t||"VERIFIED"!==e.verification_status,className:"text-blue-600 hover:text-blue-900 disabled:text-gray-400",children:"Set as Primary"})})]},e.id))})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Important Information"}),(0,a.jsxs)("ul",{className:"list-disc pl-5 space-y-2 text-gray-700",children:[(0,a.jsx)("li",{children:"Bank accounts need to be verified before they can be used for payouts."}),(0,a.jsx)("li",{children:"Verification typically takes 1-3 business days."}),(0,a.jsx)("li",{children:"For faster verification, upload a cancelled cheque or bank statement."}),(0,a.jsx)("li",{children:"Only verified bank accounts can be set as primary."}),(0,a.jsx)("li",{children:"All payouts will be sent to your primary bank account."})]})]})]})},p=()=>{let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!1),[n,d]=(0,r.useState)("all"),[i,c]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=[{id:"1",name:"Wireless Bluetooth Headphones",price:1299,stock:45,status:"active",category:"Electronics",created_at:"2023-06-15T10:30:00Z",image:"https://via.placeholder.com/50"},{id:"2",name:"Premium Cotton T-Shirt",price:499,stock:120,status:"active",category:"Clothing",created_at:"2023-06-10T14:20:00Z",image:"https://via.placeholder.com/50"},{id:"3",name:"Stainless Steel Water Bottle",price:799,stock:0,status:"out_of_stock",category:"Home & Kitchen",created_at:"2023-05-25T09:15:00Z",image:"https://via.placeholder.com/50"},{id:"4",name:"Organic Face Wash",price:349,stock:78,status:"active",category:"Beauty",created_at:"2023-06-05T11:45:00Z",image:"https://via.placeholder.com/50"},{id:"5",name:"Fitness Tracker Watch",price:2499,stock:15,status:"active",category:"Electronics",created_at:"2023-06-12T16:30:00Z",image:"https://via.placeholder.com/50"}];l(!0),setTimeout(()=>{s(e),l(!1)},500)},[]);let m=e.filter(e=>{let s="all"===n||e.status===n,t=e.name.toLowerCase().includes(i.toLowerCase())||e.category.toLowerCase().includes(i.toLowerCase());return s&&t});return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Product Management"}),(0,a.jsx)(o(),{href:"/seller/products/add",children:(0,a.jsx)("a",{className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Add New Product"})})]}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>d("all"),className:"px-3 py-1 rounded-md ".concat("all"===n?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:"All Products"}),(0,a.jsx)("button",{onClick:()=>d("active"),className:"px-3 py-1 rounded-md ".concat("active"===n?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:"Active"}),(0,a.jsx)("button",{onClick:()=>d("out_of_stock"),className:"px-3 py-1 rounded-md ".concat("out_of_stock"===n?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:"Out of Stock"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search products...",value:i,onChange:e=>c(e.target.value),className:"w-full md:w-64 border border-gray-300 rounded-md pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),t?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===m.length?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No products found."})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("img",{className:"h-10 w-10 rounded-full",src:e.image,alt:e.name})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",e.price.toLocaleString()]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.stock})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.category})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"active"===e.status?"Active":"Out of Stock"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)(o(),{href:e.id?"/seller/products/edit/".concat(e.id):"/seller/products",children:(0,a.jsx)("a",{className:"text-blue-600 hover:text-blue-900 mr-4",children:"Edit"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})]})},h=()=>{let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!1),[n,d]=(0,r.useState)("all"),[i,c]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=[{id:"1",order_number:"ORD-12345",customer_name:"Rahul Sharma",total_amount:2499,items_count:2,status:"DELIVERED",payment_status:"COMPLETED",created_at:"2023-06-15T10:30:00Z"},{id:"2",order_number:"ORD-12346",customer_name:"Priya Patel",total_amount:1299,items_count:1,status:"PROCESSING",payment_status:"COMPLETED",created_at:"2023-06-16T14:20:00Z"},{id:"3",order_number:"ORD-12347",customer_name:"Amit Kumar",total_amount:3499,items_count:3,status:"SHIPPED",payment_status:"COMPLETED",created_at:"2023-06-14T09:15:00Z"},{id:"4",order_number:"ORD-12348",customer_name:"Sneha Gupta",total_amount:799,items_count:1,status:"PENDING",payment_status:"PENDING",created_at:"2023-06-17T11:45:00Z"},{id:"5",order_number:"ORD-12349",customer_name:"Vikram Singh",total_amount:4999,items_count:2,status:"CANCELLED",payment_status:"REFUNDED",created_at:"2023-06-13T16:30:00Z"}];l(!0),setTimeout(()=>{s(e),l(!1)},500)},[]);let m=e.filter(e=>{let s="all"===n||e.status===n,t=e.order_number.toLowerCase().includes(i.toLowerCase())||e.customer_name.toLowerCase().includes(i.toLowerCase());return s&&t});return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Order Management"}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2 md:pb-0",children:[(0,a.jsx)("button",{onClick:()=>d("all"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("all"===n?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:"All Orders"}),(0,a.jsx)("button",{onClick:()=>d("PENDING"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("PENDING"===n?"bg-gray-100 text-gray-800 border-2 border-gray-300":"bg-gray-100 text-gray-800"),children:"Pending"}),(0,a.jsx)("button",{onClick:()=>d("PROCESSING"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("PROCESSING"===n?"bg-yellow-100 text-yellow-800 border-2 border-yellow-300":"bg-gray-100 text-gray-800"),children:"Processing"}),(0,a.jsx)("button",{onClick:()=>d("SHIPPED"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("SHIPPED"===n?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-800"),children:"Shipped"}),(0,a.jsx)("button",{onClick:()=>d("DELIVERED"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("DELIVERED"===n?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-800"),children:"Delivered"}),(0,a.jsx)("button",{onClick:()=>d("CANCELLED"),className:"px-3 py-1 rounded-md whitespace-nowrap ".concat("CANCELLED"===n?"bg-red-100 text-red-800 border-2 border-red-300":"bg-gray-100 text-gray-800"),children:"Cancelled"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search orders...",value:i,onChange:e=>c(e.target.value),className:"w-full md:w-64 border border-gray-300 rounded-md pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),t?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===m.length?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No orders found."})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.order_number}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.items_count," items"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.customer_name})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",e.total_amount.toLocaleString()]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.payment_status})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat((e=>{switch(e){case"DELIVERED":return"bg-green-100 text-green-800";case"PROCESSING":return"bg-yellow-100 text-yellow-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"PENDING":default:return"bg-gray-100 text-gray-800";case"CANCELLED":return"bg-red-100 text-red-800"}})(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsx)(o(),{href:e.id?"/seller/orders/".concat(e.id):"/seller/orders",children:(0,a.jsx)("a",{className:"text-blue-600 hover:text-blue-900",children:"View Details"})})})]},e.id))})]})})]})]})},g=()=>{let e=(0,l.wA)(),{profile:s,loading:t,error:n}=(0,l.d4)(e=>e.seller),[i,c]=(0,r.useState)({business_name:"",business_type:"",tax_id:"",gstin:"",pan_number:"",description:"",address:"",city:"",state:"",country:"",postal_code:"",phone_number:"",email:"",website:""}),[o,m]=(0,r.useState)(null),[u,x]=(0,r.useState)(null),[p,h]=(0,r.useState)("");(0,r.useEffect)(()=>{e((0,d.b7)())},[e]),(0,r.useEffect)(()=>{s&&c({business_name:s.business_name,business_type:s.business_type,tax_id:s.tax_id,gstin:s.gstin,pan_number:s.pan_number,description:s.description,address:s.address,city:s.city,state:s.state,country:s.country,postal_code:s.postal_code,phone_number:s.phone_number,email:s.email,website:s.website})},[s]);let g=e=>{let{name:s,value:t}=e.target;c(e=>({...e,[s]:t}))},b=e=>{let{name:s,files:t}=e.target;t&&t.length>0&&("logo"===s?m(t[0]):"banner"===s&&x(t[0]))},y=async s=>{s.preventDefault();let t={...i,logo:o?URL.createObjectURL(o):void 0,banner:u?URL.createObjectURL(u):void 0},a=await e((0,d.a4)(t));d.a4.fulfilled.match(a)&&(h("Profile updated successfully!"),setTimeout(()=>{h("")},3e3))};return t&&!s?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):s?(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Seller Profile"}),n&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),p&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:p}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center mb-8 space-y-4 md:space-y-0 md:space-x-6",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:s.logo?(0,a.jsx)("img",{src:s.logo,alt:s.business_name,className:"h-24 w-24 rounded-full object-cover border-2 border-gray-200"}):(0,a.jsx)("div",{className:"h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-2xl font-semibold",children:s.business_name.charAt(0)})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:s.business_name}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)("span",{className:"inline-block h-3 w-3 rounded-full mr-2 ".concat("VERIFIED"===s.verification_status?"bg-green-500":"PENDING"===s.verification_status?"bg-yellow-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:s.verification_status_display})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Joined on ",new Date(s.created_at).toLocaleDateString()]})]})]}),(0,a.jsxs)("form",{onSubmit:y,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Business Information"})}),(0,a.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Name*"}),(0,a.jsx)("input",{type:"text",name:"business_name",value:i.business_name,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Type*"}),(0,a.jsxs)("select",{name:"business_type",value:i.business_type,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"INDIVIDUAL",children:"Individual"}),(0,a.jsx)("option",{value:"PARTNERSHIP",children:"Partnership"}),(0,a.jsx)("option",{value:"LLC",children:"Limited Liability Company"}),(0,a.jsx)("option",{value:"CORPORATION",children:"Corporation"}),(0,a.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tax ID"}),(0,a.jsx)("input",{type:"text",name:"tax_id",value:i.tax_id,onChange:g,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"GSTIN"}),(0,a.jsx)("input",{type:"text",name:"gstin",value:i.gstin,onChange:g,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PAN Number"}),(0,a.jsx)("input",{type:"text",name:"pan_number",value:i.pan_number,onChange:g,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Description"}),(0,a.jsx)("textarea",{name:"description",value:i.description,onChange:g,rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo"}),s.logo&&(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)("img",{src:s.logo,alt:"Current Logo",className:"h-12 w-12 object-cover rounded-md"})}),(0,a.jsx)("input",{type:"file",name:"logo",onChange:b,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner"}),s.banner&&(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)("img",{src:s.banner,alt:"Current Banner",className:"h-12 w-32 object-cover rounded-md"})}),(0,a.jsx)("input",{type:"file",name:"banner",onChange:b,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("h3",{className:"text-lg font-medium mb-4 mt-4",children:"Contact Information"})}),(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address*"}),(0,a.jsx)("textarea",{name:"address",value:i.address,onChange:g,required:!0,rows:2,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City*"}),(0,a.jsx)("input",{type:"text",name:"city",value:i.city,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State*"}),(0,a.jsx)("input",{type:"text",name:"state",value:i.state,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country*"}),(0,a.jsx)("input",{type:"text",name:"country",value:i.country,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code*"}),(0,a.jsx)("input",{type:"text",name:"postal_code",value:i.postal_code,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number*"}),(0,a.jsx)("input",{type:"tel",name:"phone_number",value:i.phone_number,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email*"}),(0,a.jsx)("input",{type:"email",name:"email",value:i.email,onChange:g,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website"}),(0,a.jsx)("input",{type:"url",name:"website",value:i.website,onChange:g,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)("button",{type:"submit",disabled:t,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:t?"Saving...":"Save Changes"})})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Seller Profile Not Found"}),(0,a.jsx)("p",{children:"You need to register as a seller first."})]})},b=()=>{let e=(0,l.wA)(),{payouts:s,loading:t,error:n}=(0,l.d4)(e=>e.seller);return(0,r.useEffect)(()=>{e((0,d.Ty)())},[e]),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Payout History"}),n&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Your Payouts"})}),t?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===s.length?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No payout history available."})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction ID"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fee"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.transaction_id})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",e.amount.toLocaleString()]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",e.transaction_fee.toLocaleString()]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat((e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800";case"PROCESSING":return"bg-yellow-100 text-yellow-800";case"PENDING":default:return"bg-gray-100 text-gray-800";case"FAILED":return"bg-red-100 text-red-800"}})(e.status)),children:e.status_display})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.payout_date).toLocaleDateString()})]},e.id))})]})})]}),(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mt-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Payout Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Payout Schedule"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Payouts are processed every 7 days"})]}),(0,a.jsx)("div",{className:"mt-2 md:mt-0",children:(0,a.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"Weekly"})})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Transaction Fee"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Fee charged on each payout"})]}),(0,a.jsx)("div",{className:"mt-2 md:mt-0",children:(0,a.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"2%"})})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Minimum Payout Amount"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Minimum balance required for payout"})]}),(0,a.jsx)("div",{className:"mt-2 md:mt-0",children:(0,a.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"₹1,000"})})]})]})]})]})}},9404:(e,s,t)=>{t.d(s,{Ay:()=>b,CO:()=>c,Dz:()=>o,IZ:()=>p,Ty:()=>x,Y0:()=>i,a4:()=>d,b7:()=>l,gZ:()=>u,rf:()=>m,xF:()=>n});var a=t(1990),r=t(3464);let l=(0,a.zD)("seller/fetchProfile",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.get("/api/v1/seller/profile/")).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to fetch seller profile")}}),n=(0,a.zD)("seller/register",async(e,s)=>{let{rejectWithValue:t}=s;try{let s=new FormData;return Object.entries(e).forEach(e=>{let[t,a]=e;a instanceof File?s.append(t,a):null!=a&&s.append(t,a.toString())}),(await r.A.post("/api/v1/seller/register/",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to register as seller")}}),d=(0,a.zD)("seller/updateProfile",async(e,s)=>{let{rejectWithValue:t}=s;try{let s=new FormData;return Object.entries(e).forEach(e=>{let[t,a]=e;a instanceof File?s.append(t,a):null!=a&&s.append(t,String(a))}),(await r.A.put("/api/v1/seller/profile/",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to update seller profile")}}),i=(0,a.zD)("seller/fetchKYCDocuments",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.get("/api/v1/seller/kyc/")).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to fetch KYC documents")}}),c=(0,a.zD)("seller/uploadKYCDocument",async(e,s)=>{let{rejectWithValue:t}=s;try{let s=new FormData;return Object.entries(e).forEach(e=>{let[t,a]=e;a instanceof File?s.append(t,a):null!=a&&s.append(t,a.toString())}),(await r.A.post("/api/v1/seller/kyc/",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to upload KYC document")}}),o=(0,a.zD)("seller/fetchBankAccounts",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.get("/api/v1/seller/bank-accounts/")).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to fetch bank accounts")}}),m=(0,a.zD)("seller/addBankAccount",async(e,s)=>{let{rejectWithValue:t}=s;try{let s=new FormData;return Object.entries(e).forEach(e=>{let[t,a]=e;a instanceof File?s.append(t,a):null!=a&&s.append(t,a.toString())}),(await r.A.post("/api/v1/seller/bank-accounts/",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to add bank account")}}),u=(0,a.zD)("seller/setPrimaryBankAccount",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.post("/api/v1/seller/bank-accounts/".concat(e,"/set_primary/"))).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to set primary bank account")}}),x=(0,a.zD)("seller/fetchPayoutHistory",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.get("/api/v1/seller/payouts/")).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to fetch payout history")}}),p=(0,a.zD)("seller/fetchAnalytics",async(e,s)=>{let{rejectWithValue:t}=s;try{return(await r.A.get("/api/v1/seller/analytics/")).data.data}catch(e){var a,l,n;return t((null==(n=e.response)||null==(l=n.data)||null==(a=l.error)?void 0:a.message)||"Failed to fetch seller analytics")}}),h=(0,a.Z0)({name:"seller",initialState:{profile:null,kycDocuments:[],bankAccounts:[],payouts:[],analytics:null,loading:!1,error:null},reducers:{clearSellerError:e=>{e.error=null}},extraReducers:e=>{e.addCase(l.pending,e=>{e.loading=!0,e.error=null}).addCase(l.fulfilled,(e,s)=>{e.loading=!1,e.profile=s.payload}).addCase(l.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,s)=>{e.loading=!1,e.profile=s.payload}).addCase(n.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(d.pending,e=>{e.loading=!0,e.error=null}).addCase(d.fulfilled,(e,s)=>{e.loading=!1,e.profile=s.payload}).addCase(d.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,s)=>{e.loading=!1,e.kycDocuments=s.payload}).addCase(i.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,s)=>{e.loading=!1,e.kycDocuments=[...e.kycDocuments,s.payload]}).addCase(c.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,s)=>{e.loading=!1,e.bankAccounts=s.payload}).addCase(o.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(m.pending,e=>{e.loading=!0,e.error=null}).addCase(m.fulfilled,(e,s)=>{e.loading=!1,e.bankAccounts=[...e.bankAccounts,s.payload]}).addCase(m.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,s)=>{e.loading=!1,e.bankAccounts=e.bankAccounts.map(e=>({...e,is_primary:e.id===s.payload.id}))}).addCase(u.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(x.pending,e=>{e.loading=!0,e.error=null}).addCase(x.fulfilled,(e,s)=>{e.loading=!1,e.payouts=s.payload}).addCase(x.rejected,(e,s)=>{e.loading=!1,e.error=s.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,s)=>{e.loading=!1,e.analytics=s.payload}).addCase(p.rejected,(e,s)=>{e.loading=!1,e.error=s.payload})}}),{clearSellerError:g}=h.actions,b=h.reducer}}]);