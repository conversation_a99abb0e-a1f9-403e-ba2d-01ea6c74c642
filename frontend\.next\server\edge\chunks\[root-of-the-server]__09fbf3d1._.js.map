{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/routes.ts"], "sourcesContent": ["/**\r\n * Centralized route definitions for the application\r\n */\r\n\r\n// Main routes\r\nexport const MAIN_ROUTES = {\r\n  HOME: '/',\r\n  PRODUCTS: '/products',\r\n  PRODUCT_DETAIL: (slug: string) => `/products/${slug}`,\r\n  CART: '/cart',\r\n  CHECKOUT: '/checkout',\r\n  SEARCH: '/search',\r\n  ABOUT: '/about',\r\n  CONTACT: '/contact',\r\n  TERMS: '/terms',\r\n  PRIVACY: '/privacy',\r\n  FAQ: '/faq',\r\n} as const;\r\n\r\n// Authentication routes\r\nexport const AUTH_ROUTES = {\r\n  LOGIN: '/auth/login',\r\n  REGISTER: '/auth/register',\r\n  FORGOT_PASSWORD: '/auth/forgot-password',\r\n  RESET_PASSWORD: '/auth/reset-password',\r\n  VERIFY_EMAIL: '/auth/verify-email',\r\n} as const;\r\n\r\n// User profile routes\r\nexport const PROFILE_ROUTES = {\r\n  DASHBOARD: '/profile',\r\n  ORDERS: '/profile/orders',\r\n  ORDER_DETAIL: (id: string) => `/profile/orders/${id}`,\r\n  ADDRESSES: '/profile/addresses',\r\n  WISHLIST: '/profile/wishlist',\r\n  SETTINGS: '/profile/settings',\r\n  NOTIFICATIONS: '/profile/notifications',\r\n} as const;\r\n\r\n// Admin routes\r\nexport const ADMIN_ROUTES = {\r\n  DASHBOARD: '/admin',\r\n  ANALYTICS: '/admin/analytics',\r\n  ORDERS: '/admin/orders',\r\n  ORDER_DETAIL: (id: string) => `/admin/orders/${id}`,\r\n  PRODUCTS: '/admin/products',\r\n  PRODUCT_EDIT: (id: string) => `/admin/products/${id}/edit`,\r\n  PRODUCT_CREATE: '/admin/products/create',\r\n  CUSTOMERS: '/admin/customers',\r\n  CUSTOMER_DETAIL: (id: string) => `/admin/customers/${id}`,\r\n  CONTENT: '/admin/content',\r\n  REPORTS: '/admin/reports',\r\n  SYSTEM: '/admin/system',\r\n  NOTIFICATIONS: '/admin/notifications',\r\n  SETTINGS: '/admin/settings',\r\n} as const;\r\n\r\n// Seller routes\r\nexport const SELLER_ROUTES = {\r\n  DASHBOARD: '/seller/dashboard',\r\n  PRODUCTS: '/seller/products',\r\n  PRODUCT_EDIT: (id: string) => `/seller/products/${id}/edit`,\r\n  PRODUCT_CREATE: '/seller/products/create',\r\n  ORDERS: '/seller/orders',\r\n  ORDER_DETAIL: (id: string) => `/seller/orders/${id}`,\r\n  PROFILE: '/seller/profile',\r\n  KYC: '/seller/kyc',\r\n  BANK_ACCOUNTS: '/seller/bank-accounts',\r\n  PAYOUTS: '/seller/payouts',\r\n  ANALYTICS: '/seller/analytics',\r\n  SETTINGS: '/seller/settings',\r\n} as const;\r\n\r\n// Human-readable labels for routes\r\nexport const ROUTE_LABELS: Record<string, string> = {\r\n  '/': 'Home',\r\n  '/products': 'Products',\r\n  '/cart': 'Shopping Cart',\r\n  '/checkout': 'Checkout',\r\n  '/search': 'Search',\r\n  '/about': 'About Us',\r\n  '/contact': 'Contact Us',\r\n  '/terms': 'Terms of Service',\r\n  '/privacy': 'Privacy Policy',\r\n  '/faq': 'FAQ',\r\n  \r\n  '/auth/login': 'Login',\r\n  '/auth/register': 'Register',\r\n  '/auth/forgot-password': 'Forgot Password',\r\n  '/auth/reset-password': 'Reset Password',\r\n  '/auth/verify-email': 'Verify Email',\r\n  \r\n  '/profile': 'My Account',\r\n  '/profile/orders': 'My Orders',\r\n  '/profile/addresses': 'My Addresses',\r\n  '/profile/wishlist': 'My Wishlist',\r\n  '/profile/settings': 'Account Settings',\r\n  '/profile/notifications': 'Notifications',\r\n  \r\n  '/admin': 'Admin Dashboard',\r\n  '/admin/analytics': 'Analytics',\r\n  '/admin/orders': 'Orders Management',\r\n  '/admin/products': 'Products Management',\r\n  '/admin/products/create': 'Create Product',\r\n  '/admin/customers': 'Customers Management',\r\n  '/admin/content': 'Content Management',\r\n  '/admin/reports': 'Reports',\r\n  '/admin/system': 'System Health',\r\n  '/admin/notifications': 'Notifications',\r\n  '/admin/settings': 'Admin Settings',\r\n  \r\n  '/seller/dashboard': 'Seller Dashboard',\r\n  '/seller/products': 'My Products',\r\n  '/seller/products/create': 'Add New Product',\r\n  '/seller/orders': 'My Orders',\r\n  '/seller/profile': 'Seller Profile',\r\n  '/seller/kyc': 'KYC Verification',\r\n  '/seller/bank-accounts': 'Bank Accounts',\r\n  '/seller/payouts': 'Payouts',\r\n  '/seller/analytics': 'Sales Analytics',\r\n  '/seller/settings': 'Seller Settings',\r\n} as const;"], "names": [], "mappings": "AAAA;;CAEC,GAED,cAAc;;;;;;;;;AACP,MAAM,cAAc;IACzB,MAAM;IACN,UAAU;IACV,gBAAgB,CAAC,OAAiB,CAAC,UAAU,EAAE,MAAM;IACrD,MAAM;IACN,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,KAAK;AACP;AAGO,MAAM,cAAc;IACzB,OAAO;IACP,UAAU;IACV,iBAAiB;IACjB,gBAAgB;IAChB,cAAc;AAChB;AAGO,MAAM,iBAAiB;IAC5B,WAAW;IACX,QAAQ;IACR,cAAc,CAAC,KAAe,CAAC,gBAAgB,EAAE,IAAI;IACrD,WAAW;IACX,UAAU;IACV,UAAU;IACV,eAAe;AACjB;AAGO,MAAM,eAAe;IAC1B,WAAW;IACX,WAAW;IACX,QAAQ;IACR,cAAc,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;IACnD,UAAU;IACV,cAAc,CAAC,KAAe,CAAC,gBAAgB,EAAE,GAAG,KAAK,CAAC;IAC1D,gBAAgB;IAChB,WAAW;IACX,iBAAiB,CAAC,KAAe,CAAC,iBAAiB,EAAE,IAAI;IACzD,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,UAAU;AACZ;AAGO,MAAM,gBAAgB;IAC3B,WAAW;IACX,UAAU;IACV,cAAc,CAAC,KAAe,CAAC,iBAAiB,EAAE,GAAG,KAAK,CAAC;IAC3D,gBAAgB;IAChB,QAAQ;IACR,cAAc,CAAC,KAAe,CAAC,eAAe,EAAE,IAAI;IACpD,SAAS;IACT,KAAK;IACL,eAAe;IACf,SAAS;IACT,WAAW;IACX,UAAU;AACZ;AAGO,MAAM,eAAuC;IAClD,KAAK;IACL,aAAa;IACb,SAAS;IACT,aAAa;IACb,WAAW;IACX,UAAU;IACV,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,QAAQ;IAER,eAAe;IACf,kBAAkB;IAClB,yBAAyB;IACzB,wBAAwB;IACxB,sBAAsB;IAEtB,YAAY;IACZ,mBAAmB;IACnB,sBAAsB;IACtB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAE1B,UAAU;IACV,oBAAoB;IACpB,iBAAiB;IACjB,mBAAmB;IACnB,0BAA0B;IAC1B,oBAAoB;IACpB,kBAAkB;IAClB,kBAAkB;IAClB,iBAAiB;IACjB,wBAAwB;IACxB,mBAAmB;IAEnB,qBAAqB;IACrB,oBAAoB;IACpB,2BAA2B;IAC3B,kBAAkB;IAClB,mBAAmB;IACnB,eAAe;IACf,yBAAyB;IACzB,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;AACtB"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware/auth.ts"], "sourcesContent": ["// Authentication middleware utilities\r\n\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { MAIN_ROUTES, AUTH_ROUTES, PROFILE_ROUTES, ADMIN_ROUTES, SELLER_ROUTES } from '@/constants/routes';\r\n\r\n// Protected routes that require authentication\r\nconst PROTECTED_ROUTES = [\r\n  PROFILE_ROUTES.DASHBOARD,\r\n  PROFILE_ROUTES.ORDERS,\r\n  MAIN_ROUTES.CART,\r\n  MAIN_ROUTES.CHECKOUT,\r\n  ADMIN_ROUTES.DASHBOARD,\r\n  SELLER_ROUTES.DASHBOARD,\r\n];\r\n\r\n// Guest-only routes (redirect if authenticated)\r\nconst GUEST_ROUTES = [\r\n  AUTH_ROUTES.LOGIN,\r\n  AUTH_ROUTES.REGISTER,\r\n  AUTH_ROUTES.FORGOT_PASSWORD,\r\n  AUTH_ROUTES.RESET_PASSWORD,\r\n];\r\n\r\n/**\r\n * Authentication middleware for Next.js\r\n * Handles route protection and redirects based on authentication status\r\n */\r\nexport function authMiddleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  \r\n  // Get tokens from cookies or headers\r\n  const accessToken = request.cookies.get('access_token')?.value;\r\n  const refreshToken = request.cookies.get('refresh_token')?.value;\r\n  \r\n  const isAuthenticated = !!(accessToken || refreshToken);\r\n  \r\n  // Check if route requires authentication\r\n  const isProtectedRoute = PROTECTED_ROUTES.some(route => \r\n    pathname.startsWith(route)\r\n  );\r\n  \r\n  // Check if route is guest-only\r\n  const isGuestRoute = GUEST_ROUTES.some(route => \r\n    pathname.startsWith(route)\r\n  );\r\n  \r\n  // Check if route is admin-only or seller-only\r\n  const isAdminRoute = pathname.startsWith('/admin');\r\n  const isSellerRoute = pathname.startsWith('/seller');\r\n  \r\n  // Redirect unauthenticated users from protected routes\r\n  if (isProtectedRoute && !isAuthenticated) {\r\n    const loginUrl = new URL(AUTH_ROUTES.LOGIN, request.url);\r\n    loginUrl.searchParams.set('redirect', pathname);\r\n    return NextResponse.redirect(loginUrl);\r\n  }\r\n  \r\n  // Redirect authenticated users from guest-only routes\r\n  if (isGuestRoute && isAuthenticated) {\r\n    return NextResponse.redirect(new URL(MAIN_ROUTES.HOME, request.url));\r\n  }\r\n  \r\n  // For admin/seller routes, we'll do basic authentication check here\r\n  // but the full authorization will be handled in the components\r\n  // since we need user data from the store\r\n  if ((isAdminRoute || isSellerRoute) && !isAuthenticated) {\r\n    const loginUrl = new URL(AUTH_ROUTES.LOGIN, request.url);\r\n    loginUrl.searchParams.set('redirect', pathname);\r\n    return NextResponse.redirect(loginUrl);\r\n  }\r\n  \r\n  return NextResponse.next();\r\n}\r\n\r\n/**\r\n * Helper function to check if a path should be processed by auth middleware\r\n * @param pathname - The current pathname\r\n * @returns Boolean indicating if the path should be processed\r\n */\r\nexport function shouldProcessAuth(pathname: string): boolean {\r\n  // Skip API routes, static files, and Next.js internals\r\n  if (\r\n    pathname.startsWith('/api/') ||\r\n    pathname.startsWith('/_next/') ||\r\n    pathname.startsWith('/favicon.ico') ||\r\n    pathname.includes('.')\r\n  ) {\r\n    return false;\r\n  }\r\n  \r\n  return true;\r\n}"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AAEtC;AAAA;AACA;;;AAEA,+CAA+C;AAC/C,MAAM,mBAAmB;IACvB,kIAAA,CAAA,iBAAc,CAAC,SAAS;IACxB,kIAAA,CAAA,iBAAc,CAAC,MAAM;IACrB,kIAAA,CAAA,cAAW,CAAC,IAAI;IAChB,kIAAA,CAAA,cAAW,CAAC,QAAQ;IACpB,kIAAA,CAAA,eAAY,CAAC,SAAS;IACtB,kIAAA,CAAA,gBAAa,CAAC,SAAS;CACxB;AAED,gDAAgD;AAChD,MAAM,eAAe;IACnB,kIAAA,CAAA,cAAW,CAAC,KAAK;IACjB,kIAAA,CAAA,cAAW,CAAC,QAAQ;IACpB,kIAAA,CAAA,cAAW,CAAC,eAAe;IAC3B,kIAAA,CAAA,cAAW,CAAC,cAAc;CAC3B;AAMM,SAAS,eAAe,OAAoB;IACjD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,qCAAqC;IACrC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACzD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB;IAE3D,MAAM,kBAAkB,CAAC,CAAC,CAAC,eAAe,YAAY;IAEtD,yCAAyC;IACzC,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,CAAA,QAC7C,SAAS,UAAU,CAAC;IAGtB,+BAA+B;IAC/B,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,QACrC,SAAS,UAAU,CAAC;IAGtB,8CAA8C;IAC9C,MAAM,eAAe,SAAS,UAAU,CAAC;IACzC,MAAM,gBAAgB,SAAS,UAAU,CAAC;IAE1C,uDAAuD;IACvD,IAAI,oBAAoB,CAAC,iBAAiB;QACxC,MAAM,WAAW,IAAI,IAAI,kIAAA,CAAA,cAAW,CAAC,KAAK,EAAE,QAAQ,GAAG;QACvD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,sDAAsD;IACtD,IAAI,gBAAgB,iBAAiB;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kIAAA,CAAA,cAAW,CAAC,IAAI,EAAE,QAAQ,GAAG;IACpE;IAEA,oEAAoE;IACpE,+DAA+D;IAC/D,yCAAyC;IACzC,IAAI,CAAC,gBAAgB,aAAa,KAAK,CAAC,iBAAiB;QACvD,MAAM,WAAW,IAAI,IAAI,kIAAA,CAAA,cAAW,CAAC,KAAK,EAAE,QAAQ,GAAG;QACvD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAOO,SAAS,kBAAkB,QAAgB;IAChD,uDAAuD;IACvD,IACE,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,mBACpB,SAAS,QAAQ,CAAC,MAClB;QACA,OAAO;IACT;IAEA,OAAO;AACT"}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\r\nimport { authMiddleware, shouldProcessAuth } from './middleware/auth';\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  \r\n  // Skip processing for certain paths\r\n  if (!shouldProcessAuth(pathname)) {\r\n    return;\r\n  }\r\n  \r\n  // Apply authentication middleware\r\n  return authMiddleware(request);\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AACA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,oCAAoC;IACpC,IAAI,CAAC,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAChC;IACF;IAEA,kCAAkC;IAClC,OAAO,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE;AACxB;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}