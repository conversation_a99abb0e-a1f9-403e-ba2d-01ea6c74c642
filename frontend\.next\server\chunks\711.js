"use strict";exports.id=711,exports.ids=[711],exports.modules={11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},32711:(a,b,c)=>{c.r(b),c.d(b,{AccessibilityMenu:()=>p});var d=c(60687),e=c(43210),f=c(31464),g=c(62688);let h=(0,g.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var i=c(11860);let j=(0,g.A)("contrast",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 18a6 6 0 0 0 0-12v12z",key:"j4l70d"}]]),k=(0,g.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),l=(0,g.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),m=(0,g.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]),n=(0,g.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),o=(0,g.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function p(){let[a,b]=(0,e.useState)(!1),{highContrast:c,toggleHighContrast:g,increaseFontSize:p,decreaseFontSize:q,resetFontSize:r,reduceMotion:s,toggleReduceMotion:t,announce:u}=(0,f.W)(),v=function(a=!0,b){return(0,e.useRef)(null)}(a),w=()=>{let c=!a;b(c),c?u("Accessibility menu opened","polite"):u("Accessibility menu closed","polite")};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{className:"fixed bottom-4 right-4 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:w,"aria-label":"Accessibility options","aria-expanded":a,"aria-controls":"accessibility-menu",children:(0,d.jsx)(h,{className:"h-6 w-6"})}),a&&(0,d.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center",role:"dialog","aria-modal":"true","aria-labelledby":"accessibility-title",children:(0,d.jsxs)("div",{ref:v,id:"accessibility-menu",className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h2",{id:"accessibility-title",className:"text-xl font-bold",children:"Accessibility Options"}),(0,d.jsx)("button",{onClick:w,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200","aria-label":"Close accessibility menu",children:(0,d.jsx)(i.A,{className:"h-6 w-6"})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Display"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("button",{onClick:()=>{g(),u(`High contrast mode ${!c?"enabled":"disabled"}`,"polite")},className:`flex items-center justify-between w-full px-4 py-2 rounded-md ${c?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`,"aria-pressed":c,children:[(0,d.jsxs)("span",{className:"flex items-center",children:[(0,d.jsx)(j,{className:"mr-2 h-5 w-5"}),"High Contrast"]}),(0,d.jsx)("span",{className:"text-sm",children:c?"On":"Off"})]}),(0,d.jsxs)("button",{onClick:()=>{t(),u(`Reduced motion ${!s?"enabled":"disabled"}`,"polite")},className:`flex items-center justify-between w-full px-4 py-2 rounded-md ${s?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`,"aria-pressed":s,children:[(0,d.jsxs)("span",{className:"flex items-center",children:[(0,d.jsx)(k,{className:"mr-2 h-5 w-5"}),"Reduce Motion"]}),(0,d.jsx)("span",{className:"text-sm",children:s?"On":"Off"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Text Size"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{onClick:()=>{q(),u("Font size decreased","polite")},className:"p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600","aria-label":"Decrease font size",children:(0,d.jsx)(l,{className:"h-5 w-5"})}),(0,d.jsxs)("button",{onClick:()=>{r(),u("Font size reset to default","polite")},className:"flex-1 p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center","aria-label":"Reset font size",children:[(0,d.jsx)(m,{className:"mr-2 h-5 w-5"}),(0,d.jsx)("span",{children:"Reset"}),(0,d.jsx)(n,{className:"ml-2 h-4 w-4"})]}),(0,d.jsx)("button",{onClick:()=>{p(),u("Font size increased","polite")},className:"p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600","aria-label":"Increase font size",children:(0,d.jsx)(o,{className:"h-5 w-5"})})]})]})]})]})})]})}},62688:(a,b,c)=>{c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}}};