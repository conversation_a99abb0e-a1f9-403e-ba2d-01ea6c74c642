"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6322],{638:(e,a,r)=>{r.d(a,{kn:()=>i,i8:()=>o,ET:()=>g,Zb:()=>f,Ay:()=>S,Fn:()=>s,VZ:()=>c,MI:()=>C,it:()=>y,e6:()=>m,Ox:()=>u});var t=r(1990),l=r(2302);let d={getShippingPartners:()=>l.uE.get("/shipping/partners/"),checkServiceability:e=>l.uE.get("/shipping/serviceable-areas/check_serviceability/?pin_code=".concat(e)),getAvailableDeliverySlots:e=>l.uE.post("/shipping/delivery-slots/available_slots/",e),calculateShippingRates:e=>l.uE.post("/shipping/shipping-rates/calculate/",e),getUserShipments:e=>{let a=new URLSearchParams;return e&&Object.entries(e).forEach(e=>{let[r,t]=e;void 0!==t&&a.append(r,t.toString())}),l.uE.get("/shipping/shipments/?".concat(a.toString()))},trackShipment:e=>l.uE.get("/shipping/shipments/".concat(e,"/track/"))},n=(0,t.zD)("shipping/fetchPartners",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.getShippingPartners()).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to fetch shipping partners")}}),o=(0,t.zD)("shipping/checkServiceability",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.checkServiceability(e.pin_code)).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to check serviceability")}}),s=(0,t.zD)("shipping/fetchAvailableSlots",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.getAvailableDeliverySlots(e)).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to fetch delivery slots")}}),i=(0,t.zD)("shipping/calculateRates",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.calculateShippingRates(e)).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to calculate shipping rates")}}),u=(0,t.zD)("shipping/trackShipment",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.trackShipment(e)).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to track shipment")}}),c=(0,t.zD)("shipping/fetchUserShipments",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await d.getUserShipments()).data}catch(e){var t,l;return r((null==(l=e.response)||null==(t=l.data)?void 0:t.message)||"Failed to fetch shipments")}}),p=(0,t.Z0)({name:"shipping",initialState:{partners:[],serviceableAreas:[],deliverySlots:[],shipments:[],currentShipment:null,shippingRates:[],selectedDeliverySlot:null,selectedShippingAddress:null,loading:!1,error:null},reducers:{clearError:e=>{e.error=null},setSelectedDeliverySlot:(e,a)=>{e.selectedDeliverySlot=a.payload},setSelectedShippingAddress:(e,a)=>{e.selectedShippingAddress=a.payload},clearShippingRates:e=>{e.shippingRates=[]},clearDeliverySlots:e=>{e.deliverySlots=[]},setCurrentShipment:(e,a)=>{e.currentShipment=a.payload},resetShippingState:e=>{e.selectedDeliverySlot=null,e.selectedShippingAddress=null,e.shippingRates=[],e.deliverySlots=[],e.error=null}},extraReducers:e=>{e.addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.partners=a.payload||[]}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,a.payload&&a.payload.serviceable&&a.payload.areas&&(e.serviceableAreas=a.payload.areas)}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.serviceableAreas=[]}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1,e.deliverySlots=a.payload||[]}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.deliverySlots=[]}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,a.payload?e.shippingRates=Array.isArray(a.payload)?a.payload:[a.payload]:e.shippingRates=[]}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.shippingRates=[]}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{var r;e.loading=!1,e.currentShipment=(null==(r=a.payload)?void 0:r.shipment)||null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.currentShipment=null}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,a.payload?Array.isArray(a.payload)?e.shipments=a.payload:a.payload.results?e.shipments=a.payload.results:e.shipments=[]:e.shipments=[]}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:g,setSelectedDeliverySlot:y,setSelectedShippingAddress:m,clearShippingRates:f,clearDeliverySlots:h,setCurrentShipment:C,resetShippingState:v}=p.actions,S=p.reducer},1573:(e,a,r)=>{r.d(a,{Ay:()=>m,Hc:()=>u,YF:()=>c,_t:()=>p,hw:()=>i,ii:()=>n,j$:()=>o,wc:()=>s});var t=r(1990),l=r(2302),d=r(7141);let n=(0,t.zD)("customer/fetchProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.CUSTOMER.PROFILE);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch profile")}catch(e){return r(e.message||"Failed to fetch profile")}}),o=(0,t.zD)("customer/updateProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.put(d.Sn.CUSTOMER.PROFILE,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to update profile")}catch(e){return r(e.message||"Failed to update profile")}}),s=(0,t.zD)("customer/updatePreferences",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.put(d.Sn.CUSTOMER.PREFERENCES,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to update preferences")}catch(e){return r(e.message||"Failed to update preferences")}}),i=(0,t.zD)("customer/fetchAddresses",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.CUSTOMER.ADDRESSES);if(e.success&&e.data)return e.data.results||e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch addresses")}catch(e){return r(e.message||"Failed to fetch addresses")}}),u=(0,t.zD)("customer/createAddress",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.CUSTOMER.ADDRESSES,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to create address")}catch(e){return r(e.message||"Failed to create address")}}),c=(0,t.zD)("customer/updateAddress",async(e,a)=>{let{id:r,addressData:t}=e,{rejectWithValue:n}=a;try{var o;let e=await l.uE.put(d.Sn.CUSTOMER.ADDRESS_DETAIL(r),t);if(e.success&&e.data)return e.data;return n((null==(o=e.error)?void 0:o.message)||"Failed to update address")}catch(e){return n(e.message||"Failed to update address")}}),p=(0,t.zD)("customer/deleteAddress",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.delete(d.Sn.CUSTOMER.ADDRESS_DETAIL(e));if(a.success)return e;return r((null==(t=a.error)?void 0:t.message)||"Failed to delete address")}catch(e){return r(e.message||"Failed to delete address")}}),g=(0,t.Z0)({name:"customer",initialState:{profile:null,addresses:[],loading:!1,error:null},reducers:{clearError:e=>{e.error=null}},extraReducers:e=>{e.addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.profile=a.payload,e.error=null}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.profile=a.payload,e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1,e.profile&&(e.profile.preferences=a.payload),e.error=null}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,e.addresses=a.payload,e.error=null}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.addresses.push(a.payload),e.error=null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,a)=>{e.loading=!1;let r=e.addresses.findIndex(e=>e.id===a.payload.id);-1!==r&&(e.addresses[r]=a.payload),e.error=null}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,a)=>{e.loading=!1,e.addresses=e.addresses.filter(e=>e.id!==a.payload),e.error=null}).addCase(p.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:y}=g.actions,m=g.reducer},3165:(e,a,r)=>{r.d(a,{$f:()=>h,Ay:()=>S,BG:()=>C,CW:()=>o,DB:()=>u,KF:()=>i,Lb:()=>s,ON:()=>v,OS:()=>c,Pw:()=>p,hZ:()=>g,v4:()=>n,vg:()=>f});var t=r(1990),l=r(2302),d=r(7141);let n=(0,t.zD)("payments/fetchPaymentMethods",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.PAYMENTS.METHODS);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch payment methods")}catch(e){return r(e.message||"Failed to fetch payment methods")}}),o=(0,t.zD)("payments/fetchCurrencies",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.PAYMENTS.CURRENCIES);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch currencies")}catch(e){return r(e.message||"Failed to fetch currencies")}}),s=(0,t.zD)("payments/createPayment",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.PAYMENTS.CREATE,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to create payment")}catch(e){return r(e.message||"Failed to create payment")}}),i=(0,t.zD)("payments/verifyPayment",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.PAYMENTS.VERIFY,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to verify payment")}catch(e){return r(e.message||"Failed to verify payment")}}),u=(0,t.zD)("payments/getPaymentStatus",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.get(d.Sn.PAYMENTS.STATUS(e));if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to get payment status")}catch(e){return r(e.message||"Failed to get payment status")}}),c=(0,t.zD)("payments/getWalletDetails",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.PAYMENTS.WALLET);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to get wallet details")}catch(e){return r(e.message||"Failed to get wallet details")}}),p=(0,t.zD)("payments/validateGiftCard",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.PAYMENTS.GIFT_CARD.VALIDATE,{code:e});if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Invalid gift card")}catch(e){return r(e.message||"Failed to validate gift card")}}),g=(0,t.zD)("payments/convertCurrency",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.PAYMENTS.CONVERT_CURRENCY,e);if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to convert currency")}catch(e){return r(e.message||"Failed to convert currency")}}),y=(0,t.Z0)({name:"payments",initialState:{paymentMethods:[],currencies:[],selectedCurrency:"USD",selectedPaymentMethod:null,currentPayment:null,wallet:null,giftCard:null,loading:!1,error:null,paymentProcessing:!1,paymentSuccess:!1,paymentError:null,currencyConversion:null},reducers:{clearError:e=>{e.error=null,e.paymentError=null},resetPaymentState:e=>{e.paymentProcessing=!1,e.paymentSuccess=!1,e.paymentError=null,e.currentPayment=null},setSelectedCurrency:(e,a)=>{e.selectedCurrency=a.payload},setSelectedPaymentMethod:(e,a)=>{e.selectedPaymentMethod=a.payload},clearGiftCard:e=>{e.giftCard=null}},extraReducers:e=>{e.addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.paymentMethods=a.payload,!e.selectedPaymentMethod&&a.payload.length>0&&(e.selectedPaymentMethod=a.payload[0].id),e.error=null}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.currencies=a.payload,e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(s.pending,e=>{e.paymentProcessing=!0,e.paymentSuccess=!1,e.paymentError=null}).addCase(s.fulfilled,(e,a)=>{e.paymentProcessing=!1,e.paymentSuccess=!0,e.currentPayment={id:a.payload.payment_id,status:a.payload.status,amount:a.payload.amount,currency:a.payload.currency,payment_method:e.paymentMethods.find(e=>e.method_type===a.payload.payment_method)||{id:"",name:a.payload.payment_method,method_type:a.payload.payment_method,gateway:"INTERNAL",processing_fee_percentage:0,processing_fee_fixed:0,is_active:!0},order_id:"",processing_fee:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},e.paymentError=null}).addCase(s.rejected,(e,a)=>{e.paymentProcessing=!1,e.paymentSuccess=!1,e.paymentError=a.payload}).addCase(i.pending,e=>{e.paymentProcessing=!0,e.paymentError=null}).addCase(i.fulfilled,(e,a)=>{e.paymentProcessing=!1,e.paymentSuccess="COMPLETED"===a.payload.status,e.currentPayment&&(e.currentPayment.status=a.payload.status),e.paymentError=null}).addCase(i.rejected,(e,a)=>{e.paymentProcessing=!1,e.paymentSuccess=!1,e.paymentError=a.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.currentPayment&&(e.currentPayment.status=a.payload.status),e.paymentSuccess="COMPLETED"===a.payload.status,e.error=null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,e.wallet=a.payload,e.error=null}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,a)=>{e.loading=!1,e.giftCard=a.payload,e.error=null}).addCase(p.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.giftCard=null}).addCase(g.pending,e=>{e.loading=!0,e.error=null}).addCase(g.fulfilled,(e,a)=>{e.loading=!1,e.currencyConversion=a.payload,e.error=null}).addCase(g.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:m,resetPaymentState:f,setSelectedCurrency:h,setSelectedPaymentMethod:C,clearGiftCard:v}=y.actions,S=y.reducer},3224:(e,a,r)=>{r.d(a,{Ay:()=>p,Ej:()=>n,Jw:()=>i,Qg:()=>s});var t=r(1990),l=r(2302),d=r(7141);let n=(0,t.zD)("wishlist/fetch",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.WISHLIST.LIST);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch wishlist")}catch(e){return r(e.message||"Failed to fetch wishlist")}}),o=(0,t.zD)("wishlist/add",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.WISHLIST.ADD,{product_id:e});if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to add to wishlist")}catch(e){return r(e.message||"Failed to add to wishlist")}}),s=(0,t.zD)("wishlist/remove",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.delete(d.Sn.WISHLIST.REMOVE(e));if(a.success)return e;return r((null==(t=a.error)?void 0:t.message)||"Failed to remove from wishlist")}catch(e){return r(e.message||"Failed to remove from wishlist")}}),i=(0,t.zD)("wishlist/clear",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.delete(d.Sn.WISHLIST.CLEAR);if(e.success)return null;return r((null==(t=e.error)?void 0:t.message)||"Failed to clear wishlist")}catch(e){return r(e.message||"Failed to clear wishlist")}}),u=(0,t.Z0)({name:"wishlist",initialState:{wishlist:null,loading:!1,error:null},reducers:{clearError:e=>{e.error=null}},extraReducers:e=>{e.addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.wishlist=a.payload,e.error=null}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.wishlist?e.wishlist.items.push(a.payload):e.wishlist={id:"temp-id",items:[a.payload],created_at:new Date().toISOString()},e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1,e.wishlist&&(e.wishlist.items=e.wishlist.items.filter(e=>e.id!==a.payload)),e.error=null}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,e=>{e.loading=!1,e.wishlist&&(e.wishlist.items=[]),e.error=null}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:c}=u.actions,p=u.reducer},5111:(e,a,r)=>{r.d(a,{Ay:()=>g,fS:()=>p});var t=r(1990),l=r(7141);let d=(0,t.Z0)({name:"orders",initialState:{orders:[],currentOrder:null,loading:!1,error:null,pagination:{count:0,next:null,previous:null,page_size:10,total_pages:0,current_page:1},returnRequestLoading:!1,returnRequestError:null},reducers:{setOrders:(e,a)=>{e.orders=a.payload,e.loading=!1,e.error=null},setCurrentOrder:(e,a)=>{e.currentOrder=a.payload,e.loading=!1,e.error=null},setLoading:(e,a)=>{e.loading=a.payload},setError:(e,a)=>{e.error=a.payload,e.loading=!1},updateOrderStatus:(e,a)=>{let{orderId:r,status:t,message:l,trackingData:d,timestamp:n}=a.payload,o=e.orders.findIndex(e=>e.id===r);if(-1!==o){e.orders[o].status=t;let a={id:Date.now().toString(),status:t,description:l,location:d.location,created_at:d.timestamp||n};e.orders[o].timeline||(e.orders[o].timeline=[]),e.orders[o].timeline.unshift(a)}if(e.currentOrder&&e.currentOrder.id===r){e.currentOrder.status=t;let a={id:Date.now().toString(),status:t,description:l,location:d.location,created_at:d.timestamp||n};e.currentOrder.timeline||(e.currentOrder.timeline=[]),e.currentOrder.timeline.unshift(a)}},clearOrders:e=>{e.orders=[],e.currentOrder=null,e.loading=!1,e.error=null}}}),{setOrders:n,setCurrentOrder:o,setLoading:s,setError:i,updateOrderStatus:u,clearOrders:c}=d.actions;(0,t.zD)("orders/fetchOrders",async(e,a)=>{let{dispatch:r}=a;try{r(s(!0));let e=[];return r(n(e)),e}catch(e){throw r(i(e instanceof Error?e.message:"Failed to fetch orders")),e}}),(0,t.zD)("orders/fetchOrderById",async(e,a)=>{let{dispatch:r}=a;try{r(s(!0));let e={};return r(o(e)),e}catch(e){throw r(i(e instanceof Error?e.message:"Failed to fetch order")),e}}),(0,t.zD)("orders/cancelOrder",async(e,a)=>{let{dispatch:r}=a;try{return r(s(!0)),{success:!0}}catch(e){throw r(i(e instanceof Error?e.message:"Failed to cancel order")),e}});let p=(0,t.zD)("orders/createOrder",async(e,a)=>{let{dispatch:r}=a;try{r(s(!0));let a={id:"new-order-id",order_number:"ORD-".concat(Date.now()),status:l.w8.PENDING,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),items:e.items.map(e=>({id:"item-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,9)),product:{id:e.product_id,name:"Product Name",slug:"product-name",description:"Product description",short_description:"Short description",category:{id:"cat-1",name:"Category",slug:"category",is_active:!0,created_at:new Date().toISOString()},brand:"Brand",sku:"SKU001",price:100,is_active:!0,is_featured:!1,dimensions:{},images:[],created_at:new Date().toISOString(),updated_at:new Date().toISOString()},quantity:e.quantity,unit_price:100,total_price:100*e.quantity,status:l.w8.PENDING,can_return:!1})),shipping_address:e.shipping_address,billing_address:e.billing_address,payment_method:"credit_card",payment_status:"pending",shipping_amount:10,tax_amount:20,discount_amount:0,total_amount:130,timeline:[]};return r(o(a)),a}catch(e){throw r(i(e instanceof Error?e.message:"Failed to create order")),e}});(0,t.zD)("orders/createReturnRequest",async(e,a)=>{let{dispatch:r}=a;try{return r(s(!0)),{success:!0}}catch(e){throw r(i(e instanceof Error?e.message:"Failed to create return request")),e}});let g=d.reducer},6322:(e,a,r)=>{r.d(a,{M_:()=>K,jL:()=>V,GV:()=>$});var t=r(1990),l=r(4540),d=r(9519),n=r(9159),o=r(2302),s=r(7141);let i=(0,t.zD)("products/fetchProducts",async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{rejectWithValue:a}=arguments.length>1?arguments[1]:void 0;try{var r;let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.filters&&Object.entries(e.filters).forEach(e=>{let[a,r]=e;null!=r&&""!==r&&t.append(a,r.toString())});let l="".concat(s.Sn.PRODUCTS.LIST,"?").concat(t.toString()),d=await o.uE.get(l);if(d.success&&d.data)return d.data;return a((null==(r=d.error)?void 0:r.message)||"Failed to fetch products")}catch(e){return a(e.message||"Failed to fetch products")}}),u=(0,t.zD)("products/fetchProductById",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await o.uE.get(s.Sn.PRODUCTS.DETAIL(e));if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to fetch product")}catch(e){return r(e.message||"Failed to fetch product")}}),c=(0,t.zD)("products/fetchCategories",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await o.uE.get(s.Sn.PRODUCTS.CATEGORIES);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch categories")}catch(e){return r(e.message||"Failed to fetch categories")}}),p=(0,t.zD)("products/searchProducts",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await o.uE.get("".concat(s.Sn.PRODUCTS.LIST,"?search=").concat(encodeURIComponent(e)));if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to search products")}catch(e){return r(e.message||"Failed to search products")}}),g=(0,t.Z0)({name:"products",initialState:{products:[],categories:[],currentProduct:null,loading:!1,error:null,pagination:null,filters:{}},reducers:{clearError:e=>{e.error=null},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters={}},setCurrentProduct:(e,a)=>{e.currentProduct=a.payload}},extraReducers:e=>{e.addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,e.products=a.payload.results,e.pagination=a.payload.pagination,e.error=null}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.currentProduct=a.payload,e.error=null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,e.categories=a.payload}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,a)=>{e.loading=!1,e.products=a.payload.results,e.pagination=a.payload.pagination,e.error=null}).addCase(p.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:y,setFilters:m,clearFilters:f,setCurrentProduct:h}=g.actions,C=g.reducer;var v=r(5111);let S=(0,t.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,isNotificationCenterOpen:!1},reducers:{addNotification:(e,a)=>{!e.notifications.some(e=>e.id===a.payload.id)&&(e.notifications.unshift(a.payload),a.payload.isRead||(e.unreadCount+=1))},markNotificationRead:(e,a)=>{let r=e.notifications.find(e=>e.id===a.payload);r&&!r.isRead&&(r.isRead=!0,e.unreadCount=Math.max(0,e.unreadCount-1))},markAllNotificationsRead:e=>{e.notifications.forEach(e=>{e.isRead=!0}),e.unreadCount=0},clearNotifications:e=>{e.notifications=[],e.unreadCount=0},toggleNotificationCenter:e=>{e.isNotificationCenterOpen=!e.isNotificationCenterOpen}}}),{addNotification:E,markNotificationRead:D,markAllNotificationsRead:A,clearNotifications:w,toggleNotificationCenter:F}=S.actions,R=S.reducer,P=(0,t.Z0)({name:"inventory",initialState:{items:{},loading:!1,error:null},reducers:{setInventoryItems:(e,a)=>{e.items=a.payload,e.loading=!1,e.error=null},setLoading:(e,a)=>{e.loading=a.payload},setError:(e,a)=>{e.error=a.payload,e.loading=!1},updateInventoryLevel:(e,a)=>{let{productId:r,quantity:t,timestamp:l}=a.payload;e.items[r]?(e.items[r].quantity=t,e.items[r].lastUpdated=l):e.items[r]={productId:r,quantity:t,lastUpdated:l}},clearInventory:e=>{e.items={},e.loading=!1,e.error=null}}}),{setInventoryItems:I,setLoading:_,setError:T,updateInventoryLevel:O,clearInventory:z}=P.actions,j=P.reducer,k=(0,t.Z0)({name:"chat",initialState:{rooms:{},activeRoomId:null,loading:!1,error:null},reducers:{setChatRooms:(e,a)=>{let r={};a.payload.forEach(e=>{r[e.id]=e}),e.rooms=r,e.loading=!1,e.error=null},setActiveRoom:(e,a)=>{e.activeRoomId=a.payload,e.rooms[a.payload]&&(e.rooms[a.payload].messages.forEach(e=>{e.isRead=!0}),e.rooms[a.payload].unreadCount=0)},setLoading:(e,a)=>{e.loading=a.payload},setError:(e,a)=>{e.error=a.payload,e.loading=!1},updateChatMessages:(e,a)=>{let{roomId:r,message:t}=a.payload;e.rooms[r]||(e.rooms[r]={id:r,name:"Room ".concat(r),roomType:"UNKNOWN",participants:[],messages:[],unreadCount:0,isActive:!1}),e.rooms[r].messages.push(t),e.activeRoomId===r||t.isRead||(e.rooms[r].unreadCount+=1)},markRoomMessagesRead:(e,a)=>{let r=a.payload;e.rooms[r]&&(e.rooms[r].messages.forEach(e=>{e.isRead=!0}),e.rooms[r].unreadCount=0)},clearChat:e=>{e.rooms={},e.activeRoomId=null,e.loading=!1,e.error=null}}}),{setChatRooms:b,setActiveRoom:L,setLoading:U,setError:M,updateChatMessages:N,markRoomMessagesRead:Y,clearChat:x}=k.actions,Z=k.reducer;var H=r(3165),q=r(638),G=r(9404),B=r(3224),W=r(1573);let K=(0,t.U1)({reducer:{auth:d.Ay,cart:n.Ay,products:C,orders:v.Ay,notifications:R,inventory:j,chat:Z,payments:H.Ay,shipping:q.Ay,seller:G.Ay,wishlist:B.Ay,customer:W.Ay},middleware:e=>e({serializableCheck:{ignoredActions:["auth/setUser","auth/setToken"],ignoredActionPaths:["payload.data"],ignoredPaths:["auth.user","auth.token"]}})}),V=()=>(0,l.wA)(),$=l.d4},9159:(e,a,r)=>{r.d(a,{Ay:()=>v,bE:()=>o});var t=r(1990),l=r(2302),d=r(7141);let n=(0,t.zD)("cart/fetchCart",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.CART.LIST);if(e.success&&e.data)return e.data;return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch cart")}catch(e){return r(e.message||"Failed to fetch cart")}}),o=(0,t.zD)("cart/addToCart",async(e,a)=>{let{productId:r,quantity:t}=e,{rejectWithValue:n}=a;try{var o;let e=await l.uE.post(d.Sn.CART.ADD,{product_id:r,quantity:t});if(e.success&&e.data)return e.data;return n((null==(o=e.error)?void 0:o.message)||"Failed to add item to cart")}catch(e){return n(e.message||"Failed to add item to cart")}}),s=(0,t.zD)("cart/updateCartItem",async(e,a)=>{let{itemId:r,quantity:t}=e,{rejectWithValue:n}=a;try{var o;let e=await l.uE.patch(d.Sn.CART.UPDATE(r),{quantity:t});if(e.success&&e.data)return e.data;return n((null==(o=e.error)?void 0:o.message)||"Failed to update cart item")}catch(e){return n(e.message||"Failed to update cart item")}}),i=(0,t.zD)("cart/removeCartItem",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.delete(d.Sn.CART.REMOVE(e));if(a.success)return e;return r((null==(t=a.error)?void 0:t.message)||"Failed to remove item from cart")}catch(e){return r(e.message||"Failed to remove item from cart")}}),u=(0,t.zD)("cart/saveForLater",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post("/cart/save-for-later/".concat(e,"/"));if(a.success&&a.data)return{savedItem:a.data,itemId:e};return r((null==(t=a.error)?void 0:t.message)||"Failed to save item for later")}catch(e){return r(e.message||"Failed to save item for later")}}),c=(0,t.zD)("cart/moveToCart",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post("/cart/move-to-cart/".concat(e,"/"));if(a.success&&a.data)return{cartItem:a.data,savedItemId:e};return r((null==(t=a.error)?void 0:t.message)||"Failed to move item to cart")}catch(e){return r(e.message||"Failed to move item to cart")}}),p=(0,t.zD)("cart/removeSavedItem",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.delete("/cart/saved-items/".concat(e,"/"));if(a.success)return e;return r((null==(t=a.error)?void 0:t.message)||"Failed to remove saved item")}catch(e){return r(e.message||"Failed to remove saved item")}}),g=(0,t.zD)("cart/applyCoupon",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post("/cart/apply-coupon/",{code:e});if(a.success&&a.data)return a.data;return r((null==(t=a.error)?void 0:t.message)||"Failed to apply coupon")}catch(e){return r(e.message||"Failed to apply coupon")}}),y=(0,t.zD)("cart/removeCoupon",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.delete("/cart/remove-coupon/".concat(e,"/"));if(a.success)return e;return r((null==(t=a.error)?void 0:t.message)||"Failed to remove coupon")}catch(e){return r(e.message||"Failed to remove coupon")}}),m=e=>({itemCount:e.reduce((e,a)=>e+a.quantity,0),totalAmount:e.reduce((e,a)=>e+(a.product.discount_price||a.product.price)*a.quantity,0)}),f=(0,t.Z0)({name:"cart",initialState:{items:[],savedItems:[],appliedCoupons:[],itemCount:0,subtotal:0,discountAmount:0,totalAmount:0,loading:!1,error:null},reducers:{clearError:e=>{e.error=null},clearCart:e=>{e.items=[],e.itemCount=0,e.totalAmount=0}},extraReducers:e=>{e.addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.items=a.payload.items,e.savedItems=a.payload.saved_items||[],e.appliedCoupons=a.payload.applied_coupons||[],e.subtotal=a.payload.subtotal,e.discountAmount=a.payload.discount_amount,e.totalAmount=a.payload.total_amount;let{itemCount:r}=m(a.payload.items);e.itemCount=r,e.error=null}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1;let r=e.items.findIndex(e=>e.product.id===a.payload.product.id);r>=0?e.items[r].quantity+=a.payload.quantity:e.items.push(a.payload);let{itemCount:t,totalAmount:l}=m(e.items);e.itemCount=t,e.totalAmount=l,e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1;let r=e.items.findIndex(e=>e.id===a.payload.id);r>=0&&(e.items[r].quantity=a.payload.quantity);let{itemCount:t,totalAmount:l}=m(e.items);e.itemCount=t,e.totalAmount=l,e.error=null}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,e.items=e.items.filter(e=>e.id!==a.payload);let{itemCount:r,totalAmount:t}=m(e.items);e.itemCount=r,e.totalAmount=t,e.error=null}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.items=e.items.filter(e=>e.id!==a.payload.itemId),e.savedItems.push(a.payload.savedItem);let{itemCount:r,totalAmount:t}=m(e.items);e.itemCount=r,e.totalAmount=t,e.error=null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,e.savedItems=e.savedItems.filter(e=>e.id!==a.payload.savedItemId),e.items.push(a.payload.cartItem);let{itemCount:r,totalAmount:t}=m(e.items);e.itemCount=r,e.totalAmount=t,e.error=null}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,a)=>{e.loading=!1,e.savedItems=e.savedItems.filter(e=>e.id!==a.payload),e.error=null}).addCase(p.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(g.pending,e=>{e.loading=!0,e.error=null}).addCase(g.fulfilled,(e,a)=>{e.loading=!1,e.appliedCoupons.push(a.payload),e.discountAmount+=a.payload.discount_amount,e.totalAmount=e.subtotal-e.discountAmount,e.error=null}).addCase(g.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(y.pending,e=>{e.loading=!0,e.error=null}).addCase(y.fulfilled,(e,a)=>{e.loading=!1;let r=e.appliedCoupons.find(e=>e.coupon.id===a.payload);r&&(e.discountAmount-=r.discount_amount,e.appliedCoupons=e.appliedCoupons.filter(e=>e.coupon.id!==a.payload),e.totalAmount=e.subtotal-e.discountAmount),e.error=null}).addCase(y.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearError:h,clearCart:C}=f.actions,v=f.reducer},9404:(e,a,r)=>{r.d(a,{Ay:()=>h,CO:()=>i,Dz:()=>u,IZ:()=>y,Ty:()=>g,Y0:()=>s,a4:()=>o,b7:()=>d,gZ:()=>p,rf:()=>c,xF:()=>n});var t=r(1990),l=r(3464);let d=(0,t.zD)("seller/fetchProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.get("/api/v1/seller/profile/")).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to fetch seller profile")}}),n=(0,t.zD)("seller/register",async(e,a)=>{let{rejectWithValue:r}=a;try{let a=new FormData;return Object.entries(e).forEach(e=>{let[r,t]=e;t instanceof File?a.append(r,t):null!=t&&a.append(r,t.toString())}),(await l.A.post("/api/v1/seller/register/",a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to register as seller")}}),o=(0,t.zD)("seller/updateProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{let a=new FormData;return Object.entries(e).forEach(e=>{let[r,t]=e;t instanceof File?a.append(r,t):null!=t&&a.append(r,String(t))}),(await l.A.put("/api/v1/seller/profile/",a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to update seller profile")}}),s=(0,t.zD)("seller/fetchKYCDocuments",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.get("/api/v1/seller/kyc/")).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to fetch KYC documents")}}),i=(0,t.zD)("seller/uploadKYCDocument",async(e,a)=>{let{rejectWithValue:r}=a;try{let a=new FormData;return Object.entries(e).forEach(e=>{let[r,t]=e;t instanceof File?a.append(r,t):null!=t&&a.append(r,t.toString())}),(await l.A.post("/api/v1/seller/kyc/",a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to upload KYC document")}}),u=(0,t.zD)("seller/fetchBankAccounts",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.get("/api/v1/seller/bank-accounts/")).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to fetch bank accounts")}}),c=(0,t.zD)("seller/addBankAccount",async(e,a)=>{let{rejectWithValue:r}=a;try{let a=new FormData;return Object.entries(e).forEach(e=>{let[r,t]=e;t instanceof File?a.append(r,t):null!=t&&a.append(r,t.toString())}),(await l.A.post("/api/v1/seller/bank-accounts/",a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to add bank account")}}),p=(0,t.zD)("seller/setPrimaryBankAccount",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.post("/api/v1/seller/bank-accounts/".concat(e,"/set_primary/"))).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to set primary bank account")}}),g=(0,t.zD)("seller/fetchPayoutHistory",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.get("/api/v1/seller/payouts/")).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to fetch payout history")}}),y=(0,t.zD)("seller/fetchAnalytics",async(e,a)=>{let{rejectWithValue:r}=a;try{return(await l.A.get("/api/v1/seller/analytics/")).data.data}catch(e){var t,d,n;return r((null==(n=e.response)||null==(d=n.data)||null==(t=d.error)?void 0:t.message)||"Failed to fetch seller analytics")}}),m=(0,t.Z0)({name:"seller",initialState:{profile:null,kycDocuments:[],bankAccounts:[],payouts:[],analytics:null,loading:!1,error:null},reducers:{clearSellerError:e=>{e.error=null}},extraReducers:e=>{e.addCase(d.pending,e=>{e.loading=!0,e.error=null}).addCase(d.fulfilled,(e,a)=>{e.loading=!1,e.profile=a.payload}).addCase(d.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,a)=>{e.loading=!1,e.profile=a.payload}).addCase(n.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.profile=a.payload}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1,e.kycDocuments=a.payload}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(i.pending,e=>{e.loading=!0,e.error=null}).addCase(i.fulfilled,(e,a)=>{e.loading=!1,e.kycDocuments=[...e.kycDocuments,a.payload]}).addCase(i.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(u.pending,e=>{e.loading=!0,e.error=null}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.bankAccounts=a.payload}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0,e.error=null}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,e.bankAccounts=[...e.bankAccounts,a.payload]}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.pending,e=>{e.loading=!0,e.error=null}).addCase(p.fulfilled,(e,a)=>{e.loading=!1,e.bankAccounts=e.bankAccounts.map(e=>({...e,is_primary:e.id===a.payload.id}))}).addCase(p.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(g.pending,e=>{e.loading=!0,e.error=null}).addCase(g.fulfilled,(e,a)=>{e.loading=!1,e.payouts=a.payload}).addCase(g.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(y.pending,e=>{e.loading=!0,e.error=null}).addCase(y.fulfilled,(e,a)=>{e.loading=!1,e.analytics=a.payload}).addCase(y.rejected,(e,a)=>{e.loading=!1,e.error=a.payload})}}),{clearSellerError:f}=m.actions,h=m.reducer},9519:(e,a,r)=>{r.d(a,{Ay:()=>f,DY:()=>s,Lx:()=>o,Nu:()=>p,y4:()=>i});var t=r(1990),l=r(2302),d=r(7141),n=r(2056);let o=(0,t.zD)("auth/login",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.AUTH.LOGIN,e);if(a.success&&a.data){let{user:e,tokens:r}=a.data;return(0,n.yH)(r),(0,n.To)(e),{user:e,tokens:r}}return r((null==(t=a.error)?void 0:t.message)||"Login failed")}catch(e){return r(e.message||"Login failed")}}),s=(0,t.zD)("auth/register",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.post(d.Sn.AUTH.REGISTER,e);if(a.success&&a.data){let{user:e,tokens:r}=a.data;return(0,n.yH)(r),(0,n.To)(e),{user:e,tokens:r}}return r((null==(t=a.error)?void 0:t.message)||"Registration failed")}catch(e){return r(e.message||"Registration failed")}}),i=(0,t.zD)("auth/logout",async()=>{try{let e=(0,n.Uk)();return(null==e?void 0:e.refresh)&&await l.uE.post(d.Sn.AUTH.LOGOUT,{refresh:e.refresh}),(0,n.Y9)(),(0,n.LP)(),null}catch(e){return(0,n.Y9)(),(0,n.LP)(),null}}),u=(0,t.zD)("auth/fetchProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let e=await l.uE.get(d.Sn.AUTH.PROFILE);if(e.success&&e.data){let a=e.data.user;return(0,n.To)(a),a}return r((null==(t=e.error)?void 0:t.message)||"Failed to fetch profile")}catch(e){return r(e.message||"Failed to fetch profile")}}),c=(0,t.zD)("auth/updateProfile",async(e,a)=>{let{rejectWithValue:r}=a;try{var t;let a=await l.uE.put(d.Sn.AUTH.PROFILE,e);if(a.success&&a.data){let e=a.data.user;return(0,n.To)(e),e}return r((null==(t=a.error)?void 0:t.message)||"Failed to update profile")}catch(e){return r(e.message||"Failed to update profile")}}),p=(0,t.zD)("auth/initialize",async(e,a)=>{let{dispatch:r}=a,t=(0,n.Uk)(),l=(0,n.Ti)();if(t&&l)try{return await r(u()).unwrap(),{user:l,tokens:t}}catch(e){(0,n.Y9)(),(0,n.LP)()}return null}),g=(0,t.Z0)({name:"auth",initialState:{user:null,tokens:null,isAuthenticated:!1,loading:!1,error:null},reducers:{clearError:e=>{e.error=null},setTokens:(e,a)=>{e.tokens=a.payload,(0,n.yH)(a.payload)}},extraReducers:e=>{e.addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload.user,e.tokens=a.payload.tokens,e.isAuthenticated=!0,e.error=null}).addCase(o.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.isAuthenticated=!1}).addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload.user,e.tokens=a.payload.tokens,e.isAuthenticated=!0,e.error=null}).addCase(s.rejected,(e,a)=>{e.loading=!1,e.error=a.payload,e.isAuthenticated=!1}).addCase(i.fulfilled,e=>{e.user=null,e.tokens=null,e.isAuthenticated=!1,e.loading=!1,e.error=null}).addCase(u.pending,e=>{e.loading=!0}).addCase(u.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload,e.error=null}).addCase(u.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(c.pending,e=>{e.loading=!0}).addCase(c.fulfilled,(e,a)=>{e.loading=!1,e.user=a.payload,e.error=null}).addCase(c.rejected,(e,a)=>{e.loading=!1,e.error=a.payload}).addCase(p.fulfilled,(e,a)=>{a.payload&&(e.user=a.payload.user,e.tokens=a.payload.tokens,e.isAuthenticated=!0),e.loading=!1})}}),{clearError:y,setTokens:m}=g.actions,f=g.reducer}}]);