"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8254],{8254:(e,s,t)=>{t.d(s,{a8:()=>h,Xh:()=>w,Qg:()=>u,c$:()=>b,ar:()=>A});var a=t(5155),r=t(2115),l=t(4540);let i=()=>(0,l.wA)(),n=l.d4;var d=t(1573),c=t(3741),o=t(3915),m=t(3568);let x={type:"HOME",first_name:"",last_name:"",company:"",address_line_1:"",address_line_2:"",city:"",state:"",postal_code:"",country:"US",phone:"",is_default:!1};function h(){let e=i(),{addresses:s,loading:t}=n(e=>e.customer),[l,h]=(0,r.useState)(!1),[u,p]=(0,r.useState)(null),[g,y]=(0,r.useState)(x),[f,j]=(0,r.useState)({});(0,r.useEffect)(()=>{e((0,d.hw)())},[e]),(0,r.useEffect)(()=>{u?y({type:u.type,first_name:u.first_name,last_name:u.last_name,company:u.company||"",address_line_1:u.address_line_1,address_line_2:u.address_line_2||"",city:u.city,state:u.state,postal_code:u.postal_code,country:u.country,phone:u.phone||"",is_default:u.is_default}):y(x)},[u]);let b=e=>{let{name:s,value:t,type:a}=e.target,r=e.target.checked;y(e=>({...e,[s]:"checkbox"===a?r:t})),f[s]&&j(e=>({...e,[s]:""}))},v=async s=>{if(s.preventDefault(),(()=>{let e={};return g.first_name.trim()||(e.first_name="First name is required"),g.last_name.trim()||(e.last_name="Last name is required"),g.address_line_1.trim()||(e.address_line_1="Address is required"),g.city.trim()||(e.city="City is required"),g.state.trim()||(e.state="State is required"),g.postal_code.trim()||(e.postal_code="Postal code is required"),g.country.trim()||(e.country="Country is required"),g.phone&&!/^[\+]?[1-9][\d]{0,15}$/.test(g.phone)&&(e.phone="Please enter a valid phone number"),j(e),0===Object.keys(e).length})())try{u?(await e((0,d.YF)({id:u.id,addressData:g})).unwrap(),m.Ay.success("Address updated successfully!")):(await e((0,d.Hc)(g)).unwrap(),m.Ay.success("Address added successfully!")),w()}catch(e){m.Ay.error(e||"Failed to save address")}},N=async s=>{if(window.confirm("Are you sure you want to delete this address?"))try{await e((0,d._t)(s.id)).unwrap(),m.Ay.success("Address deleted successfully!")}catch(e){m.Ay.error(e||"Failed to delete address")}},w=()=>{h(!1),p(null),y(x),j({})},_=async()=>{try{await e((0,d.hw)()).unwrap(),m.Ay.success("Addresses refreshed")}catch(e){m.Ay.error("Failed to refresh addresses")}};return(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["My Addresses",s.length>0&&(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",s.length," ",1===s.length?"address":"addresses",")"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:_,loading:t,children:"Refresh"}),(0,a.jsx)(c.$,{variant:"primary",size:"sm",onClick:()=>h(!0),children:"Add Address"})]})]})}),(0,a.jsx)("div",{className:"px-6 py-4",children:0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No addresses found"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Add your first address to get started with faster checkout."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(c.$,{variant:"primary",onClick:()=>h(!0),children:"Add Address"})})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:[(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("HOME"===e.type?"bg-blue-100 text-blue-800":"WORK"===e.type?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.type}),e.is_default&&(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800",children:"Default"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("p",{className:"font-medium",children:[e.first_name," ",e.last_name]}),e.company&&(0,a.jsx)("p",{className:"text-gray-600",children:e.company}),(0,a.jsx)("p",{children:e.address_line_1}),e.address_line_2&&(0,a.jsx)("p",{children:e.address_line_2}),(0,a.jsxs)("p",{children:[e.city,", ",e.state," ",e.postal_code]}),(0,a.jsx)("p",{children:e.country}),e.phone&&(0,a.jsxs)("p",{className:"text-gray-600",children:["Phone: ",e.phone]})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{p(e),h(!0)},children:"Edit"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>N(e),className:"text-red-600 hover:text-red-700",children:"Delete"})]})]},e.id))})}),l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:u?"Edit Address":"Add New Address"}),(0,a.jsx)("button",{onClick:w,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Type"}),(0,a.jsxs)("select",{name:"type",value:g.type,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"HOME",children:"Home"}),(0,a.jsx)("option",{value:"WORK",children:"Work"}),(0,a.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"is_default",name:"is_default",type:"checkbox",checked:g.is_default,onChange:b,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"is_default",className:"ml-2 block text-sm text-gray-900",children:"Set as default address"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(o.p,{label:"First Name",name:"first_name",type:"text",value:g.first_name,onChange:b,error:f.first_name,required:!0}),(0,a.jsx)(o.p,{label:"Last Name",name:"last_name",type:"text",value:g.last_name,onChange:b,error:f.last_name,required:!0})]}),(0,a.jsx)(o.p,{label:"Company (Optional)",name:"company",type:"text",value:g.company,onChange:b}),(0,a.jsx)(o.p,{label:"Address Line 1",name:"address_line_1",type:"text",value:g.address_line_1,onChange:b,error:f.address_line_1,required:!0}),(0,a.jsx)(o.p,{label:"Address Line 2 (Optional)",name:"address_line_2",type:"text",value:g.address_line_2,onChange:b}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(o.p,{label:"City",name:"city",type:"text",value:g.city,onChange:b,error:f.city,required:!0}),(0,a.jsx)(o.p,{label:"State",name:"state",type:"text",value:g.state,onChange:b,error:f.state,required:!0}),(0,a.jsx)(o.p,{label:"Postal Code",name:"postal_code",type:"text",value:g.postal_code,onChange:b,error:f.postal_code,required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(o.p,{label:"Country",name:"country",type:"text",value:g.country,onChange:b,error:f.country,required:!0}),(0,a.jsx)(o.p,{label:"Phone (Optional)",name:"phone",type:"tel",value:g.phone,onChange:b,error:f.phone,placeholder:"+1234567890"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(c.$,{type:"button",variant:"outline",onClick:w,children:"Cancel"}),(0,a.jsx)(c.$,{type:"submit",loading:t,children:u?"Update Address":"Add Address"})]})]})]})})})]})}function u(){let e=i(),{user:s}=n(e=>e.auth),{profile:t,loading:l}=n(e=>e.customer),[x,h]=(0,r.useState)({date_of_birth:"",gender:""}),[u,p]=(0,r.useState)({}),[g,y]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e((0,d.ii)())},[e]),(0,r.useEffect)(()=>{t&&h({date_of_birth:t.date_of_birth||"",gender:t.gender||""})},[t]);let f=e=>{let{name:s,value:t}=e.target;h(e=>({...e,[s]:t})),u[s]&&p(e=>({...e,[s]:""}))},j=async s=>{if(s.preventDefault(),(()=>{let e={};if(x.date_of_birth){let s=new Date(x.date_of_birth),t=new Date,a=t.getFullYear()-s.getFullYear();s>t?e.date_of_birth="Date of birth cannot be in the future":a>120&&(e.date_of_birth="Please enter a valid date of birth")}return p(e),0===Object.keys(e).length})())try{await e((0,d.j$)({...x,gender:x.gender||void 0})).unwrap(),m.Ay.success("Profile updated successfully!"),y(!1)}catch(e){m.Ay.error(e||"Failed to update profile")}},b=async()=>{try{await e((0,d.ii)()).unwrap(),m.Ay.success("Profile refreshed")}catch(e){m.Ay.error("Failed to refresh profile")}};return s?(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Customer Profile"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:b,loading:l,children:"Refresh"}),!g&&(0,a.jsx)(c.$,{variant:"primary",size:"sm",onClick:()=>y(!0),children:"Edit Profile"})]})]})}),(0,a.jsx)("div",{className:"px-6 py-4",children:g?(0,a.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(o.p,{label:"Date of Birth",name:"date_of_birth",type:"date",value:x.date_of_birth,onChange:f,error:u.date_of_birth}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gender"}),(0,a.jsxs)("select",{name:"gender",value:x.gender,onChange:f,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"Select Gender"}),(0,a.jsx)("option",{value:"M",children:"Male"}),(0,a.jsx)("option",{value:"F",children:"Female"}),(0,a.jsx)("option",{value:"O",children:"Other"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>{t&&h({date_of_birth:t.date_of_birth||"",gender:t.gender||""}),p({}),y(!1)},children:"Cancel"}),(0,a.jsx)(c.$,{type:"submit",loading:l,children:"Save Changes"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:s.username})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:s.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:s.phone_number||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 capitalize",children:s.user_type})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Additional Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Date of Birth"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==t?void 0:t.date_of_birth)?new Date(t.date_of_birth).toLocaleDateString():"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Gender"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==t?void 0:t.gender)?"M"===t.gender?"Male":"F"===t.gender?"Female":"Other":"Not specified"})]})]})]}),(0,a.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Verification Status"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(s.is_verified?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:s.is_verified?"Verified":"Unverified"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Member Since"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(s.created_at).toLocaleDateString()})]})]})})]})})]}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No user data available"})})}var p=t(6874),g=t.n(p),y=t(5695),f=t(7141);let j=[{name:"Profile",href:f.bw.PROFILE,icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})},{name:"Addresses",href:f.bw.PROFILE_ADDRESSES,icon:(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})},{name:"Wishlist",href:f.bw.PROFILE_WISHLIST,icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})},{name:"Preferences",href:f.bw.PROFILE_PREFERENCES,icon:(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}];function b(e){let{children:s}=e,t=(0,y.usePathname)(),[l,i]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-5",children:[(0,a.jsx)("aside",{className:"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3",children:(0,a.jsxs)("nav",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsxs)("button",{type:"button",className:"bg-white p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>i(!l),children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})}),(0,a.jsx)("div",{className:"".concat(l?"block":"hidden"," lg:block"),children:j.map(e=>{let s=t===e.href;return(0,a.jsxs)(g(),{href:e.href||"/profile",className:"".concat(s?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900"," group border-l-4 px-3 py-2 flex items-center text-sm font-medium"),onClick:()=>i(!1),children:[(0,a.jsx)("span",{className:"".concat(s?"text-blue-500":"text-gray-400 group-hover:text-gray-500"," flex-shrink-0 -ml-1 mr-3"),children:e.icon}),(0,a.jsx)("span",{className:"truncate",children:e.name})]},e.name)})})]})}),(0,a.jsx)("div",{className:"space-y-6 sm:px-6 lg:px-0 lg:col-span-9",children:s})]})})})}let v=[{value:"en",label:"English"},{value:"es",label:"Spanish"},{value:"fr",label:"French"},{value:"de",label:"German"}],N=[{value:"USD",label:"US Dollar ($)"},{value:"EUR",label:"Euro (€)"},{value:"GBP",label:"British Pound (\xa3)"},{value:"CAD",label:"Canadian Dollar (C$)"}];function w(){var e,s;let t=i(),{profile:l,loading:o}=n(e=>e.customer),[x,h]=(0,r.useState)({newsletter_subscription:!1,sms_notifications:!1,email_notifications:!0,language:"en",currency:"USD"}),[u,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{l||t((0,d.ii)())},[t,l]),(0,r.useEffect)(()=>{(null==l?void 0:l.preferences)&&h(l.preferences)},[l]);let g=e=>{let{name:s,value:t,type:a}=e.target,r=e.target.checked;h(e=>({...e,[s]:"checkbox"===a?r:t}))},y=async e=>{e.preventDefault();try{await t((0,d.wc)(x)).unwrap(),m.Ay.success("Preferences updated successfully!"),p(!1)}catch(e){m.Ay.error(e||"Failed to update preferences")}},f=async()=>{try{await t((0,d.ii)()).unwrap(),m.Ay.success("Preferences refreshed")}catch(e){m.Ay.error("Failed to refresh preferences")}};return(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Preferences"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:f,loading:o,children:"Refresh"}),!u&&(0,a.jsx)(c.$,{variant:"primary",size:"sm",onClick:()=>p(!0),children:"Edit Preferences"})]})]})}),(0,a.jsx)("div",{className:"px-6 py-4",children:u?(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notifications"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"email_notifications",name:"email_notifications",type:"checkbox",checked:x.email_notifications,onChange:g,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[(0,a.jsx)("label",{htmlFor:"email_notifications",className:"font-medium text-gray-700",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Receive order updates and promotions via email"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"sms_notifications",name:"sms_notifications",type:"checkbox",checked:x.sms_notifications,onChange:g,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[(0,a.jsx)("label",{htmlFor:"sms_notifications",className:"font-medium text-gray-700",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Receive order updates via SMS"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"newsletter_subscription",name:"newsletter_subscription",type:"checkbox",checked:x.newsletter_subscription,onChange:g,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[(0,a.jsx)("label",{htmlFor:"newsletter_subscription",className:"font-medium text-gray-700",children:"Newsletter Subscription"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Receive our newsletter with deals and updates"})]})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Localization"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Language"}),(0,a.jsx)("select",{name:"language",value:x.language,onChange:g,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:v.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Currency"}),(0,a.jsx)("select",{name:"currency",value:x.currency,onChange:g,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:N.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>{(null==l?void 0:l.preferences)&&h(l.preferences),p(!1)},children:"Cancel"}),(0,a.jsx)(c.$,{type:"submit",loading:o,children:"Save Preferences"})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notifications"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive order updates and promotions via email"})]}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(x.email_notifications?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:x.email_notifications?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive order updates via SMS"})]}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(x.sms_notifications?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:x.sms_notifications?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Newsletter Subscription"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive our newsletter with deals and updates"})]}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(x.newsletter_subscription?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:x.newsletter_subscription?"Subscribed":"Unsubscribed"})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Localization"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Language"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==(e=v.find(e=>e.value===x.language))?void 0:e.label)||x.language})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Currency"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:(null==(s=N.find(e=>e.value===x.currency))?void 0:s.label)||x.currency})]})]})]})]})})]})}var _=t(6766),k=t(3224),C=t(9159);function A(){let e=i(),{wishlist:s,loading:t}=n(e=>e.wishlist),[l,d]=(0,r.useState)(new Set);(0,r.useEffect)(()=>{e((0,k.Ej)())},[e]);let o=async s=>{d(e=>new Set(e).add(s));try{await e((0,k.Qg)(s)).unwrap(),m.Ay.success("Item removed from wishlist")}catch(e){m.Ay.error(e||"Failed to remove item")}finally{d(e=>{let t=new Set(e);return t.delete(s),t})}},x=async(s,t)=>{try{await e((0,C.bE)({productId:s,quantity:1})).unwrap(),m.Ay.success("".concat(t," added to cart"))}catch(e){m.Ay.error(e||"Failed to add to cart")}},h=async()=>{if((null==s?void 0:s.items.length)&&window.confirm("Are you sure you want to clear your entire wishlist?"))try{await e((0,k.Jw)()).unwrap(),m.Ay.success("Wishlist cleared")}catch(e){m.Ay.error(e||"Failed to clear wishlist")}},u=async()=>{try{await e((0,k.Ej)()).unwrap(),m.Ay.success("Wishlist refreshed")}catch(e){m.Ay.error("Failed to refresh wishlist")}};return t&&!s?(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"h-20 w-20 bg-gray-200 rounded"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]},s))})]})})}):(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["My Wishlist",(null==s?void 0:s.items)&&(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",s.items.length," ",1===s.items.length?"item":"items",")"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:u,loading:t,children:"Refresh"}),(null==s?void 0:s.items)&&s.items.length>0&&(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:h,className:"text-red-600 hover:text-red-700",children:"Clear All"})]})]})}),(0,a.jsx)("div",{className:"px-6 py-4",children:(null==s?void 0:s.items)&&0!==s.items.length?(0,a.jsx)("div",{className:"space-y-4",children:s.items.map(e=>{var s;return(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-20 w-20 relative",children:e.product.images&&e.product.images.length>0?(0,a.jsx)(_.default,{src:(null==(s=e.product.images.find(e=>e.is_primary))?void 0:s.image)||e.product.images[0].image,alt:e.product.name,fill:!0,className:"object-cover rounded-md"}):(0,a.jsx)("div",{className:"h-full w-full bg-gray-200 rounded-md flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(g(),{href:e.product.slug?f.bw.PRODUCT_DETAIL(e.product.slug):f.bw.PRODUCTS,className:"text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2",children:e.product.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1 line-clamp-1",children:e.product.short_description}),(0,a.jsxs)("div",{className:"flex items-center mt-2 space-x-2",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-gray-900",children:["$",e.product.discount_price||e.product.price]}),e.product.discount_price&&(0,a.jsxs)("span",{className:"text-sm text-gray-500 line-through",children:["$",e.product.price]})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Added ",new Date(e.added_at).toLocaleDateString()]})]})})}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(c.$,{size:"sm",onClick:()=>x(e.product.id,e.product.name),disabled:!e.product.is_active,children:"Add to Cart"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>o(e.id),loading:l.has(e.id),className:"text-red-600 hover:text-red-700",children:"Remove"})]})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Your wishlist is empty"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Start adding products you love to your wishlist."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(g(),{href:f.bw.PRODUCTS,children:(0,a.jsx)(c.$,{variant:"primary",children:"Browse Products"})})})]})})]})}}}]);