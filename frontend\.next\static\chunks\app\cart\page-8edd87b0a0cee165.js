(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{2843:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(5155),a=t(2115),l=t(4775),i=t(3741),n=t(6322),c=t(6874),d=t.n(c),o=t(7141),x=t(3568);let m=[{id:"1",product:{id:"1",name:"iPhone 15 Pro Max (Natural Titanium, 256GB)",brand:"Apple",price:134900,discount_price:134900,image:"/api/placeholder/150/150",rating:4.5,reviewCount:12543},quantity:1,subtotal:134900},{id:"2",product:{id:"2",name:"Samsung Galaxy S24 Ultra (Titanium Black, 512GB)",brand:"Samsung",price:149999,discount_price:129999,image:"/api/placeholder/150/150",rating:4.3,reviewCount:8765},quantity:2,subtotal:259998}];function h(){(0,n.jL)();let{items:e,itemCount:s,totalAmount:t,loading:c}=(0,n.GV)(e=>e.cart),[h,u]=(0,a.useState)(m),[p,g]=(0,a.useState)([]),[j,b]=(0,a.useState)(!1);(0,a.useEffect)(()=>{u(m)},[]);let y=e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",maximumFractionDigits:0}).format(e),N=async(e,s)=>{if(!(s<1))try{u(t=>t.map(t=>t.id===e?{...t,quantity:s,subtotal:t.product.discount_price*s}:t))}catch(e){x.Ay.error("Failed to update quantity")}},v=async e=>{try{u(s=>s.filter(s=>s.id!==e)),x.Ay.success("Item removed from cart")}catch(e){x.Ay.error("Failed to remove item")}},f=h.filter(e=>p.includes(e.id)).reduce((e,s)=>e+s.subtotal,0),w=p.length;return 0===h.length?(0,r.jsx)(l.P,{children:(0,r.jsx)("div",{className:"bg-gray-50 min-h-screen",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)("svg",{className:"w-24 h-24 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"})})}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Add items to your cart to see them here"}),(0,r.jsx)(d(),{href:o.bw.PRODUCTS,children:(0,r.jsx)(i.$,{size:"lg",className:"bg-orange-500 hover:bg-orange-600",children:"Continue Shopping"})})]})})})}):(0,r.jsx)(l.P,{children:(0,r.jsx)("div",{className:"bg-gray-50 min-h-screen",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["My Cart (",h.length,")"]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4",children:(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:j,onChange:()=>{j?g([]):g(h.map(e=>e.id)),b(!j)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsxs)("span",{className:"ml-3 text-sm font-medium text-gray-900",children:["Select All (",h.length," items)"]})]})}),h.map(e=>(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("input",{type:"checkbox",checked:p.includes(e.id),onChange:()=>{var s;return s=e.id,void g(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])},className:"mt-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0",children:(0,r.jsx)("img",{src:e.product.image,alt:e.product.name,className:"w-full h-full object-contain"})}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:e.product.brand}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:e.product.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:y(e.product.discount_price)}),e.product.price!==e.product.discount_price&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-sm text-gray-500 line-through",children:y(e.product.price)}),(0,r.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[Math.round((e.product.price-e.product.discount_price)/e.product.price*100),"% off"]})]})]}),(0,r.jsxs)("div",{className:"space-y-1 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center text-xs text-green-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Free Delivery"]}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-blue-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})}),"7 days replacement"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center border border-gray-300 rounded",children:[(0,r.jsx)("button",{onClick:()=>N(e.id,e.quantity-1),className:"px-3 py-1 hover:bg-gray-100 text-gray-600",disabled:e.quantity<=1,children:"-"}),(0,r.jsx)("span",{className:"px-4 py-1 border-x border-gray-300 font-medium",children:e.quantity}),(0,r.jsx)("button",{onClick:()=>N(e.id,e.quantity+1),className:"px-3 py-1 hover:bg-gray-100 text-gray-600",children:"+"})]}),(0,r.jsx)("button",{onClick:()=>v(e.id),className:"text-sm text-gray-500 hover:text-red-600 font-medium",children:"Remove"}),(0,r.jsx)("button",{className:"text-sm text-gray-500 hover:text-blue-600 font-medium",children:"Save for later"})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:y(e.subtotal)})})]})})]})},e.id))]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Price Details (",w," item",1!==w?"s":"",")"]}),(0,r.jsxs)("div",{className:"space-y-3 border-b border-gray-200 pb-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Price"}),(0,r.jsx)("span",{className:"text-gray-900",children:y(f)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Discount"}),(0,r.jsxs)("span",{className:"text-green-600",children:["-",y(0)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Delivery Charges"}),(0,r.jsx)("span",{className:"text-green-600",children:"FREE"})]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-semibold text-gray-900 mb-6",children:[(0,r.jsx)("span",{children:"Total Amount"}),(0,r.jsx)("span",{children:y(f)})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.$,{size:"lg",className:"w-full bg-orange-500 hover:bg-orange-600",disabled:0===w,children:"Place Order"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(d(),{href:o.bw.PRODUCTS,children:(0,r.jsx)(i.$,{variant:"outline",size:"lg",className:"w-full",children:"Continue Shopping"})})})]}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-center text-sm text-gray-600",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"Safe and Secure Payments"]})})]})})]})]})})})}},5251:(e,s,t)=>{Promise.resolve().then(t.bind(t,2843))}},e=>{e.O(0,[3464,4540,1990,7244,3568,2125,6322,4775,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=5251)),_N_E=e.O()}]);