(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1093],{387:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var r=s(5155),a=s(2115),c=s(3109),l=s(7580),o=s(7108),i=s(1788),n=s(7809),d=s(4615),m=s(1015),h=s(2083),u=s(1675),x=s(4338),p=s(3568);function E(){let[e,t]=(0,a.useState)("sales"),[s,E]=(0,a.useState)(null),[S,y]=(0,a.useState)(null),[g,N]=(0,a.useState)(!0),[R,f]=(0,a.useState)({from:new Date(Date.now()-2592e6).toISOString().split("T")[0],to:new Date().toISOString().split("T")[0]}),A=(0,a.useCallback)(async()=>{try{if(N(!0),"sales"===e){let e=await d.i.getSalesReport({date_from:R.from,date_to:R.to});E(e)}else if("customers"===e)await d.i.getCustomerAnalyticsSummary();else if("inventory"===e){let e=await d.i.getStockMaintenanceReport();y(e)}}catch(e){console.error("Failed to fetch analytics data:",e),p.Ay.error("Failed to load analytics data")}finally{N(!1)}},[e,R]);(0,a.useEffect)(()=>{A()},[e,R]);let T=async()=>{try{let t="sales";"customers"===e&&(t="customer"),"inventory"===e&&(t="inventory"),await d.i.exportReport({report_type:t,export_format:"csv",date_from:R.from,date_to:R.to}),p.Ay.success("Report export started. You will be notified when ready.")}catch(e){console.error("Failed to export report:",e),p.Ay.error("Failed to export report")}},v=[{id:"sales",name:"Sales Analytics",icon:c.A},{id:"customers",name:"Customer Analytics",icon:l.A},{id:"inventory",name:"Inventory Analytics",icon:o.A}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Advanced Analytics"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Detailed insights and performance metrics"})]}),(0,r.jsxs)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-3",children:[(0,r.jsx)(m.A,{from:R.from,to:R.to,onChange:(e,t)=>{f({from:e,to:t})}}),(0,r.jsxs)("button",{onClick:T,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:v.map(s=>{let a=e===s.id;return(0,r.jsxs)("button",{onClick:()=>t(s.id),className:"flex items-center py-2 px-1 border-b-2 font-medium text-sm ".concat(a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,r.jsx)(s.icon,{className:"h-5 w-5 mr-2"}),s.name]},s.id)})})}),g?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(x.A,{size:"lg"})}):(0,r.jsxs)("div",{className:"space-y-6",children:["sales"===e&&s&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n.A,{className:"h-8 w-8 text-blue-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Orders"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:s.summary.total_orders.toLocaleString()})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-green-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Revenue"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:["$",s.summary.total_revenue.toLocaleString()]})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-purple-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Avg Order Value"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:["$",s.summary.average_order_value.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-yellow-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Discounts"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:["$",s.summary.total_discount.toLocaleString()]})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Sales Trend"}),(0,r.jsx)(h.A,{dateRange:R})]}),s.payment_methods.length>0&&(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Payment Methods"}),(0,r.jsx)("div",{className:"space-y-3",children:s.payment_methods.map((e,t)=>{let a=e.revenue/s.summary.total_revenue*100;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.payment_method||"Unknown"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.count," orders"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["$",e.revenue.toLocaleString()]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[a.toFixed(1),"%"]})]})]},"".concat(e.payment_method,"-").concat(t))})})]})]}),"customers"===e&&(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Customer Lifecycle Analysis"}),(0,r.jsx)(u.A,{})]}),"inventory"===e&&S&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:S.summary.low_stock_count}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Low Stock Items"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:S.summary.out_of_stock_count}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Out of Stock"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:S.summary.overstock_count}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Overstock Items"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:S.summary.dead_stock_count}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Dead Stock Items"})]})})]}),S.low_stock.length>0&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Low Stock Items"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SKU"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Stock"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Minimum Level"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reorder Point"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.low_stock.slice(0,10).map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.product_name}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.sku}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-yellow-600 font-medium",children:e.current_quantity}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.minimum_level}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.reorder_point})]},e.product_id))})]})})]})]})]})]})}},2056:(e,t,s)=>{"use strict";s.d(t,{LP:()=>n,Ti:()=>o,To:()=>i,Uk:()=>a,Y9:()=>l,yH:()=>c});var r=s(7141);let a=()=>{try{let e=localStorage.getItem(r.d5.ACCESS_TOKEN),t=localStorage.getItem(r.d5.REFRESH_TOKEN);if(e&&t)return{access:e,refresh:t}}catch(e){console.error("Error getting stored tokens:",e)}return null},c=e=>{try{localStorage.setItem(r.d5.ACCESS_TOKEN,e.access),localStorage.setItem(r.d5.REFRESH_TOKEN,e.refresh)}catch(e){console.error("Error storing tokens:",e)}},l=()=>{try{localStorage.removeItem(r.d5.ACCESS_TOKEN),localStorage.removeItem(r.d5.REFRESH_TOKEN)}catch(e){console.error("Error removing tokens:",e)}},o=()=>{try{let e=localStorage.getItem(r.d5.USER);return e?JSON.parse(e):null}catch(e){return console.error("Error getting stored user:",e),null}},i=e=>{try{localStorage.setItem(r.d5.USER,JSON.stringify(e))}catch(e){console.error("Error storing user:",e)}},n=()=>{try{localStorage.removeItem(r.d5.USER)}catch(e){console.error("Error removing user:",e)}}},2302:(e,t,s)=>{"use strict";s.d(t,{uE:()=>o});var r=s(3464),a=s(7141),c=s(2056);class l{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=(0,c.Uk)();return(null==t?void 0:t.access)&&e.headers&&(e.headers.Authorization="Bearer ".concat(t.access)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{if(e&&"object"==typeof e&&"config"in e&&"response"in e){var t;let s=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!s._retry){s._retry=!0;try{let e=(0,c.Uk)();if(null==e?void 0:e.refresh){let t=(await this.refreshToken(e.refresh)).data;return(0,c.yH)(t),s.headers&&(s.headers.Authorization="Bearer ".concat(t.access)),this.client(s)}}catch(e){(0,c.Y9)(),window.location.href="/auth/login"}}}return Promise.reject(e)})}async refreshToken(e){return this.client.post("/auth/refresh/",{refresh:e})}async get(e,t){try{let s=await this.client.get(e,t);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async post(e,t,s){try{let r=await this.client.post(e,t,s);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}async put(e,t,s){try{let r=await this.client.put(e,t,s);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}async patch(e,t,s){try{let r=await this.client.patch(e,t,s);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}async delete(e,t){try{let s=await this.client.delete(e,t);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}handleError(e){if(e&&"object"==typeof e&&"response"in e){if(e.response){var t,s,r,a,c,l,o;return{success:!1,error:{message:(null==(s=e.response.data)||null==(t=s.error)?void 0:t.message)||(null==(r=e.response.data)?void 0:r.message)||"An error occurred",code:(null==(c=e.response.data)||null==(a=c.error)?void 0:a.code)||"api_error",status_code:e.response.status,details:(null==(o=e.response.data)||null==(l=o.error)?void 0:l.details)||e.response.data}}}if(e.request)return{success:!1,error:{message:"Network error. Please check your connection.",code:"network_error",status_code:0}}}return{success:!1,error:{message:e instanceof Error?e.message:"An unexpected error occurred",code:"unknown_error",status_code:0}}}constructor(){this.client=r.A.create({baseURL:a.JR,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let o=new l,{get:i,post:n,put:d,patch:m,delete:h}=o},6389:(e,t,s)=>{"use strict";s.d(t,{B0:()=>o,Ei:()=>a,F$:()=>l,Hp:()=>c,Ss:()=>i,l$:()=>r});let r={HOME:"/",PRODUCTS:"/products",PRODUCT_DETAIL:e=>"/products/".concat(e),CART:"/cart",CHECKOUT:"/checkout",SEARCH:"/search",ABOUT:"/about",CONTACT:"/contact",TERMS:"/terms",PRIVACY:"/privacy",FAQ:"/faq"},a={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email"},c={DASHBOARD:"/profile",ORDERS:"/profile/orders",ORDER_DETAIL:e=>"/profile/orders/".concat(e),ADDRESSES:"/profile/addresses",WISHLIST:"/profile/wishlist",SETTINGS:"/profile/settings",NOTIFICATIONS:"/profile/notifications"},l={DASHBOARD:"/admin",ANALYTICS:"/admin/analytics",ORDERS:"/admin/orders",ORDER_DETAIL:e=>"/admin/orders/".concat(e),PRODUCTS:"/admin/products",PRODUCT_EDIT:e=>"/admin/products/".concat(e,"/edit"),PRODUCT_CREATE:"/admin/products/create",CUSTOMERS:"/admin/customers",CUSTOMER_DETAIL:e=>"/admin/customers/".concat(e),CONTENT:"/admin/content",REPORTS:"/admin/reports",SYSTEM:"/admin/system",NOTIFICATIONS:"/admin/notifications",SETTINGS:"/admin/settings"},o={DASHBOARD:"/seller/dashboard",PRODUCTS:"/seller/products",PRODUCT_EDIT:e=>"/seller/products/".concat(e,"/edit"),PRODUCT_CREATE:"/seller/products/create",ORDERS:"/seller/orders",ORDER_DETAIL:e=>"/seller/orders/".concat(e),PROFILE:"/seller/profile",KYC:"/seller/kyc",BANK_ACCOUNTS:"/seller/bank-accounts",PAYOUTS:"/seller/payouts",ANALYTICS:"/seller/analytics",SETTINGS:"/seller/settings"},i={"/":"Home","/products":"Products","/cart":"Shopping Cart","/checkout":"Checkout","/search":"Search","/about":"About Us","/contact":"Contact Us","/terms":"Terms of Service","/privacy":"Privacy Policy","/faq":"FAQ","/auth/login":"Login","/auth/register":"Register","/auth/forgot-password":"Forgot Password","/auth/reset-password":"Reset Password","/auth/verify-email":"Verify Email","/profile":"My Account","/profile/orders":"My Orders","/profile/addresses":"My Addresses","/profile/wishlist":"My Wishlist","/profile/settings":"Account Settings","/profile/notifications":"Notifications","/admin":"Admin Dashboard","/admin/analytics":"Analytics","/admin/orders":"Orders Management","/admin/products":"Products Management","/admin/products/create":"Create Product","/admin/customers":"Customers Management","/admin/content":"Content Management","/admin/reports":"Reports","/admin/system":"System Health","/admin/notifications":"Notifications","/admin/settings":"Admin Settings","/seller/dashboard":"Seller Dashboard","/seller/products":"My Products","/seller/products/create":"Add New Product","/seller/orders":"My Orders","/seller/profile":"Seller Profile","/seller/kyc":"KYC Verification","/seller/bank-accounts":"Bank Accounts","/seller/payouts":"Payouts","/seller/analytics":"Sales Analytics","/seller/settings":"Seller Settings"}},7141:(e,t,s)=>{"use strict";s.d(t,{Cy:()=>o,Hp:()=>r.Hp,JR:()=>a,Sn:()=>c,bw:()=>n,d5:()=>l,oO:()=>d,w8:()=>i});var r=s(6389);let a="http://localhost:8000/api/v1",c={AUTH:{LOGIN:"/auth/login/",REGISTER:"/auth/register/",LOGOUT:"/auth/logout/",REFRESH:"/auth/refresh/",PROFILE:"/auth/profile/",FORGOT_PASSWORD:"/auth/forgot-password/",RESET_PASSWORD:"/auth/reset-password/",VALIDATE_RESET_TOKEN:e=>"/auth/validate-reset-token/".concat(e,"/")},PRODUCTS:{LIST:"/products/",DETAIL:e=>"/products/".concat(e,"/"),CATEGORIES:"/products/categories/"},CART:{LIST:"/cart/",ADD:"/cart/add/",UPDATE:e=>"/cart/".concat(e,"/"),REMOVE:e=>"/cart/".concat(e,"/")},ORDERS:{LIST:"/orders/",DETAIL:e=>"/orders/".concat(e,"/"),CREATE:"/orders/",CANCEL:e=>"/orders/".concat(e,"/cancel/"),TIMELINE:e=>"/orders/".concat(e,"/timeline/"),INVOICE:e=>"/orders/".concat(e,"/invoice/"),DOWNLOAD_INVOICE:e=>"/orders/".concat(e,"/download_invoice/")},RETURNS:{LIST:"/return-requests/",DETAIL:e=>"/return-requests/".concat(e,"/"),CREATE:"/return-requests/"},REPLACEMENTS:{LIST:"/replacements/",DETAIL:e=>"/replacements/".concat(e,"/"),CREATE:"/replacements/",UPDATE_STATUS:e=>"/replacements/".concat(e,"/update_status/")},SEARCH:{PRODUCTS:"/search/products/",SUGGESTIONS:"/search/suggestions/",FILTERS:"/search/filters/",POPULAR:"/search/popular/",RELATED:"/search/related/"},CUSTOMER:{PROFILE:"/customer/profile/",ADDRESSES:"/customer/addresses/",ADDRESS_DETAIL:e=>"/customer/addresses/".concat(e,"/"),PREFERENCES:"/customer/preferences/"},WISHLIST:{LIST:"/wishlist/",ADD:"/wishlist/add/",REMOVE:e=>"/wishlist/".concat(e,"/"),CLEAR:"/wishlist/clear/"},PAYMENTS:{METHODS:"/payments/methods/",CURRENCIES:"/payments/currencies/",CREATE:"/payments/create/",VERIFY:"/payments/verify/",STATUS:e=>"/payments/".concat(e,"/status/"),WALLET:"/payments/wallet/",GIFT_CARD:{VALIDATE:"/payments/gift-card/validate/",BALANCE:e=>"/payments/gift-card/".concat(e,"/balance/")},CONVERT_CURRENCY:"/payments/convert-currency/"},ADMIN:{DASHBOARD:"/admin/dashboard/",ANALYTICS:"/admin/analytics/",PRODUCTS:"/admin/products/",ORDERS:"/admin/orders/",CUSTOMERS:"/admin/customers/",SETTINGS:"/admin/settings/"},SELLER:{DASHBOARD:"/seller/dashboard/",PRODUCTS:"/seller/products/",ORDERS:"/seller/orders/",PROFILE:"/seller/profile/",KYC:"/seller/kyc/",PAYOUTS:"/seller/payouts/"}},l={ACCESS_TOKEN:"access_token",REFRESH_TOKEN:"refresh_token",USER:"user",CART:"cart"},o={CUSTOMER:"customer",SELLER:"seller",ADMIN:"admin"},i={PENDING:"PENDING",CONFIRMED:"CONFIRMED",PROCESSING:"PROCESSING",SHIPPED:"SHIPPED",DELIVERED:"DELIVERED",CANCELLED:"CANCELLED",RETURNED:"RETURNED"},n={...r.l$,...r.Ei,PROFILE:r.Hp.DASHBOARD,ORDERS:r.Hp.ORDERS,PROFILE_ADDRESSES:r.Hp.ADDRESSES,PROFILE_WISHLIST:r.Hp.WISHLIST,PROFILE_PREFERENCES:r.Hp.SETTINGS,ADMIN:r.F$.DASHBOARD,SELLER:r.B0.DASHBOARD},d={PASSWORD_MIN_LENGTH:8,USERNAME_MIN_LENGTH:3,PHONE_REGEX:/^[\+]?[1-9][\d]{0,15}$/,EMAIL_REGEX:/^[^\s@]+@[^\s@]+\.[^\s@]+$/}},8114:(e,t,s)=>{Promise.resolve().then(s.bind(s,387))}},e=>{e.O(0,[3464,3568,6664,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=8114)),_N_E=e.O()}]);