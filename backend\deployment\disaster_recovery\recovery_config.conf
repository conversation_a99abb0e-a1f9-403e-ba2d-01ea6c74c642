# MySQL Disaster Recovery Configuration
# This file contains configuration parameters for disaster recovery scripts

# Database Configuration
DB_NAME=ecommerce_prod
DB_USER=ecommerce_prod_user
DB_PASSWORD=
DB_ROOT_PASSWORD=

# Primary Database Server
PRIMARY_HOST=mysql-primary.internal
PRIMARY_PORT=3306

# Read Replica Configuration
REPLICA_HOST=mysql-replica.internal
REPLICA_PORT=3306

# Disaster Recovery Site
DR_HOST=mysql-dr.internal
DR_PORT=3306

# Replication User
REPLICATION_USER=replication
REPLICATION_PASSWORD=

# Backup Configuration
BACKUP_DIR=/var/backups/mysql
LATEST_BACKUP=/var/backups/mysql/latest/mysql_full_backup.sql.gz
BACKUP_RETENTION_DAYS=30

# Application Configuration
DJANGO_SETTINGS_FILE=/opt/ecommerce/backend/ecommerce_project/settings/production.py
ENV_FILE=/opt/ecommerce/backend/.env
MAINTENANCE_MODE_FILE=/opt/ecommerce/maintenance.flag
APP_HEALTH_URL=http://localhost:8000/health/

# Load Balancer Configuration
LOAD_BALANCER_SCRIPT=/opt/scripts/load_balancer_control.sh

# DNS Configuration
DNS_UPDATE_SCRIPT=/opt/scripts/update_dns.sh

# Notification Configuration
NOTIFICATION_EMAIL=<EMAIL>,<EMAIL>
SLACK_WEBHOOK=

# Recovery Objectives
RTO_MINUTES=30
RPO_MINUTES=15

# Monitoring Configuration
MONITORING_INTERVAL=60
HEALTH_CHECK_TIMEOUT=30

# SSL Configuration
SSL_CA_CERT=/etc/mysql/ssl/ca-cert.pem
SSL_CLIENT_CERT=/etc/mysql/ssl/client-cert.pem
SSL_CLIENT_KEY=/etc/mysql/ssl/client-key.pem

# Log Configuration
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=90

# Recovery Testing
TEST_DATABASE=ecommerce_test
TEST_SCHEDULE="0 2 * * 0"  # Weekly on Sunday at 2 AM

# Emergency Contacts
ONCALL_PRIMARY=+1-555-0101
ONCALL_SECONDARY=+1-555-0102
ESCALATION_MANAGER=+1-555-0103

# Service Dependencies
DEPENDENT_SERVICES="gunicorn celery nginx redis"
EXTERNAL_DEPENDENCIES="payment-gateway email-service cdn"

# Recovery Thresholds
MAX_REPLICATION_LAG=300
MAX_RECOVERY_TIME=1800
MIN_DISK_SPACE_GB=10

# Backup Verification
BACKUP_TEST_FREQUENCY=daily
BACKUP_INTEGRITY_CHECK=true
BACKUP_RESTORE_TEST=weekly