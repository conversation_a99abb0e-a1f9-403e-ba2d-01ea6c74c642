"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2125],{2056:(e,r,t)=>{t.d(r,{LP:()=>l,Ti:()=>n,To:()=>i,Uk:()=>a,Y9:()=>c,yH:()=>o});var s=t(7141);let a=()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN),r=localStorage.getItem(s.d5.REFRESH_TOKEN);if(e&&r)return{access:e,refresh:r}}catch(e){console.error("Error getting stored tokens:",e)}return null},o=e=>{try{localStorage.setItem(s.d5.ACCESS_TOKEN,e.access),localStorage.setItem(s.d5.REFRESH_TOKEN,e.refresh)}catch(e){console.error("Error storing tokens:",e)}},c=()=>{try{localStorage.removeItem(s.d5.ACCESS_TOKEN),localStorage.removeItem(s.d5.REFRESH_TOKEN)}catch(e){console.error("Error removing tokens:",e)}},n=()=>{try{let e=localStorage.getItem(s.d5.USER);return e?JSON.parse(e):null}catch(e){return console.error("Error getting stored user:",e),null}},i=e=>{try{localStorage.setItem(s.d5.USER,JSON.stringify(e))}catch(e){console.error("Error storing user:",e)}},l=()=>{try{localStorage.removeItem(s.d5.USER)}catch(e){console.error("Error removing user:",e)}}},2302:(e,r,t)=>{t.d(r,{uE:()=>n});var s=t(3464),a=t(7141),o=t(2056);class c{setupInterceptors(){this.client.interceptors.request.use(e=>{let r=(0,o.Uk)();return(null==r?void 0:r.access)&&e.headers&&(e.headers.Authorization="Bearer ".concat(r.access)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{if(e&&"object"==typeof e&&"config"in e&&"response"in e){var r;let t=e.config;if((null==(r=e.response)?void 0:r.status)===401&&!t._retry){t._retry=!0;try{let e=(0,o.Uk)();if(null==e?void 0:e.refresh){let r=(await this.refreshToken(e.refresh)).data;return(0,o.yH)(r),t.headers&&(t.headers.Authorization="Bearer ".concat(r.access)),this.client(t)}}catch(e){(0,o.Y9)(),window.location.href="/auth/login"}}}return Promise.reject(e)})}async refreshToken(e){return this.client.post("/auth/refresh/",{refresh:e})}async get(e,r){try{let t=await this.client.get(e,r);return{success:!0,data:t.data}}catch(e){return this.handleError(e)}}async post(e,r,t){try{let s=await this.client.post(e,r,t);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async put(e,r,t){try{let s=await this.client.put(e,r,t);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async patch(e,r,t){try{let s=await this.client.patch(e,r,t);return{success:!0,data:s.data}}catch(e){return this.handleError(e)}}async delete(e,r){try{let t=await this.client.delete(e,r);return{success:!0,data:t.data}}catch(e){return this.handleError(e)}}handleError(e){if(e&&"object"==typeof e&&"response"in e){if(e.response){var r,t,s,a,o,c,n;return{success:!1,error:{message:(null==(t=e.response.data)||null==(r=t.error)?void 0:r.message)||(null==(s=e.response.data)?void 0:s.message)||"An error occurred",code:(null==(o=e.response.data)||null==(a=o.error)?void 0:a.code)||"api_error",status_code:e.response.status,details:(null==(n=e.response.data)||null==(c=n.error)?void 0:c.details)||e.response.data}}}if(e.request)return{success:!1,error:{message:"Network error. Please check your connection.",code:"network_error",status_code:0}}}return{success:!1,error:{message:e instanceof Error?e.message:"An unexpected error occurred",code:"unknown_error",status_code:0}}}constructor(){this.client=s.A.create({baseURL:a.JR,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let n=new c,{get:i,post:l,put:d,patch:E,delete:u}=n},3741:(e,r,t)=>{t.d(r,{$:()=>a,A:()=>o});var s=t(5155);t(2115);let a=e=>{let{variant:r="primary",size:t="md",loading:a=!1,disabled:o,children:c,className:n="",...i}=e;return(0,s.jsxs)("button",{className:"".concat("inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 touch-manipulation select-none"," ").concat({primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300",outline:"border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400 disabled:border-gray-200",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-blue-500 disabled:text-gray-400 disabled:hover:bg-transparent"}[r]," ").concat({sm:"px-3 py-2 text-sm min-h-[36px]",md:"px-4 py-2 text-sm min-h-[44px]",lg:"px-6 py-3 text-base min-h-[48px]"}[t]," ").concat("disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none"," ").concat(n),disabled:o||a,...i,children:[a&&(0,s.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"}),c]})},o=a},6389:(e,r,t)=>{t.d(r,{B0:()=>n,Ei:()=>a,F$:()=>c,Hp:()=>o,Ss:()=>i,l$:()=>s});let s={HOME:"/",PRODUCTS:"/products",PRODUCT_DETAIL:e=>"/products/".concat(e),CART:"/cart",CHECKOUT:"/checkout",SEARCH:"/search",ABOUT:"/about",CONTACT:"/contact",TERMS:"/terms",PRIVACY:"/privacy",FAQ:"/faq"},a={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email"},o={DASHBOARD:"/profile",ORDERS:"/profile/orders",ORDER_DETAIL:e=>"/profile/orders/".concat(e),ADDRESSES:"/profile/addresses",WISHLIST:"/profile/wishlist",SETTINGS:"/profile/settings",NOTIFICATIONS:"/profile/notifications"},c={DASHBOARD:"/admin",ANALYTICS:"/admin/analytics",ORDERS:"/admin/orders",ORDER_DETAIL:e=>"/admin/orders/".concat(e),PRODUCTS:"/admin/products",PRODUCT_EDIT:e=>"/admin/products/".concat(e,"/edit"),PRODUCT_CREATE:"/admin/products/create",CUSTOMERS:"/admin/customers",CUSTOMER_DETAIL:e=>"/admin/customers/".concat(e),CONTENT:"/admin/content",REPORTS:"/admin/reports",SYSTEM:"/admin/system",NOTIFICATIONS:"/admin/notifications",SETTINGS:"/admin/settings"},n={DASHBOARD:"/seller/dashboard",PRODUCTS:"/seller/products",PRODUCT_EDIT:e=>"/seller/products/".concat(e,"/edit"),PRODUCT_CREATE:"/seller/products/create",ORDERS:"/seller/orders",ORDER_DETAIL:e=>"/seller/orders/".concat(e),PROFILE:"/seller/profile",KYC:"/seller/kyc",BANK_ACCOUNTS:"/seller/bank-accounts",PAYOUTS:"/seller/payouts",ANALYTICS:"/seller/analytics",SETTINGS:"/seller/settings"},i={"/":"Home","/products":"Products","/cart":"Shopping Cart","/checkout":"Checkout","/search":"Search","/about":"About Us","/contact":"Contact Us","/terms":"Terms of Service","/privacy":"Privacy Policy","/faq":"FAQ","/auth/login":"Login","/auth/register":"Register","/auth/forgot-password":"Forgot Password","/auth/reset-password":"Reset Password","/auth/verify-email":"Verify Email","/profile":"My Account","/profile/orders":"My Orders","/profile/addresses":"My Addresses","/profile/wishlist":"My Wishlist","/profile/settings":"Account Settings","/profile/notifications":"Notifications","/admin":"Admin Dashboard","/admin/analytics":"Analytics","/admin/orders":"Orders Management","/admin/products":"Products Management","/admin/products/create":"Create Product","/admin/customers":"Customers Management","/admin/content":"Content Management","/admin/reports":"Reports","/admin/system":"System Health","/admin/notifications":"Notifications","/admin/settings":"Admin Settings","/seller/dashboard":"Seller Dashboard","/seller/products":"My Products","/seller/products/create":"Add New Product","/seller/orders":"My Orders","/seller/profile":"Seller Profile","/seller/kyc":"KYC Verification","/seller/bank-accounts":"Bank Accounts","/seller/payouts":"Payouts","/seller/analytics":"Sales Analytics","/seller/settings":"Seller Settings"}},7141:(e,r,t)=>{t.d(r,{Cy:()=>n,Hp:()=>s.Hp,JR:()=>a,Sn:()=>o,bw:()=>l,d5:()=>c,oO:()=>d,w8:()=>i});var s=t(6389);let a="http://localhost:8000/api/v1",o={AUTH:{LOGIN:"/auth/login/",REGISTER:"/auth/register/",LOGOUT:"/auth/logout/",REFRESH:"/auth/refresh/",PROFILE:"/auth/profile/",FORGOT_PASSWORD:"/auth/forgot-password/",RESET_PASSWORD:"/auth/reset-password/",VALIDATE_RESET_TOKEN:e=>"/auth/validate-reset-token/".concat(e,"/")},PRODUCTS:{LIST:"/products/",DETAIL:e=>"/products/".concat(e,"/"),CATEGORIES:"/products/categories/"},CART:{LIST:"/cart/",ADD:"/cart/add/",UPDATE:e=>"/cart/".concat(e,"/"),REMOVE:e=>"/cart/".concat(e,"/")},ORDERS:{LIST:"/orders/",DETAIL:e=>"/orders/".concat(e,"/"),CREATE:"/orders/",CANCEL:e=>"/orders/".concat(e,"/cancel/"),TIMELINE:e=>"/orders/".concat(e,"/timeline/"),INVOICE:e=>"/orders/".concat(e,"/invoice/"),DOWNLOAD_INVOICE:e=>"/orders/".concat(e,"/download_invoice/")},RETURNS:{LIST:"/return-requests/",DETAIL:e=>"/return-requests/".concat(e,"/"),CREATE:"/return-requests/"},REPLACEMENTS:{LIST:"/replacements/",DETAIL:e=>"/replacements/".concat(e,"/"),CREATE:"/replacements/",UPDATE_STATUS:e=>"/replacements/".concat(e,"/update_status/")},SEARCH:{PRODUCTS:"/search/products/",SUGGESTIONS:"/search/suggestions/",FILTERS:"/search/filters/",POPULAR:"/search/popular/",RELATED:"/search/related/"},CUSTOMER:{PROFILE:"/customer/profile/",ADDRESSES:"/customer/addresses/",ADDRESS_DETAIL:e=>"/customer/addresses/".concat(e,"/"),PREFERENCES:"/customer/preferences/"},WISHLIST:{LIST:"/wishlist/",ADD:"/wishlist/add/",REMOVE:e=>"/wishlist/".concat(e,"/"),CLEAR:"/wishlist/clear/"},PAYMENTS:{METHODS:"/payments/methods/",CURRENCIES:"/payments/currencies/",CREATE:"/payments/create/",VERIFY:"/payments/verify/",STATUS:e=>"/payments/".concat(e,"/status/"),WALLET:"/payments/wallet/",GIFT_CARD:{VALIDATE:"/payments/gift-card/validate/",BALANCE:e=>"/payments/gift-card/".concat(e,"/balance/")},CONVERT_CURRENCY:"/payments/convert-currency/"},ADMIN:{DASHBOARD:"/admin/dashboard/",ANALYTICS:"/admin/analytics/",PRODUCTS:"/admin/products/",ORDERS:"/admin/orders/",CUSTOMERS:"/admin/customers/",SETTINGS:"/admin/settings/"},SELLER:{DASHBOARD:"/seller/dashboard/",PRODUCTS:"/seller/products/",ORDERS:"/seller/orders/",PROFILE:"/seller/profile/",KYC:"/seller/kyc/",PAYOUTS:"/seller/payouts/"}},c={ACCESS_TOKEN:"access_token",REFRESH_TOKEN:"refresh_token",USER:"user",CART:"cart"},n={CUSTOMER:"customer",SELLER:"seller",ADMIN:"admin"},i={PENDING:"PENDING",CONFIRMED:"CONFIRMED",PROCESSING:"PROCESSING",SHIPPED:"SHIPPED",DELIVERED:"DELIVERED",CANCELLED:"CANCELLED",RETURNED:"RETURNED"},l={...s.l$,...s.Ei,PROFILE:s.Hp.DASHBOARD,ORDERS:s.Hp.ORDERS,PROFILE_ADDRESSES:s.Hp.ADDRESSES,PROFILE_WISHLIST:s.Hp.WISHLIST,PROFILE_PREFERENCES:s.Hp.SETTINGS,ADMIN:s.F$.DASHBOARD,SELLER:s.B0.DASHBOARD},d={PASSWORD_MIN_LENGTH:8,USERNAME_MIN_LENGTH:3,PHONE_REGEX:/^[\+]?[1-9][\d]{0,15}$/,EMAIL_REGEX:/^[^\s@]+@[^\s@]+\.[^\s@]+$/}}}]);