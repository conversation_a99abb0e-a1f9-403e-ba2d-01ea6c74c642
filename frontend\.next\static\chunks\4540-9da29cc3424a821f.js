"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4540],{1992:(e,t,n)=>{e.exports=n(4993)},4540:(e,t,n)=>{n.d(t,{Kq:()=>b,d4:()=>x,wA:()=>S});var r=n(2115),u=n(1992),o={notify(){},get:()=>[]},i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,c="undefined"!=typeof navigator&&"ReactNative"===navigator.product,a=i||c?r.useLayoutEffect:r.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var l=Symbol.for("react-redux-context"),s="undefined"!=typeof globalThis?globalThis:{},f=function(){if(!r.createContext)return{};let e=s[l]??=new Map,t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}(),b=function(e){let{children:t,context:n,serverState:u,store:i}=e,c=r.useMemo(()=>{let e=function(e,t){let n,r=o,u=0,i=!1;function c(){s.onStateChange&&s.onStateChange()}function a(){if(u++,!n){let t,u;n=e.subscribe(c),t=null,u=null,r={clear(){t=null,u=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let n=!0,r=u={callback:e,next:null,prev:u};return r.prev?r.prev.next=r:t=r,function(){n&&null!==t&&(n=!1,r.next?r.next.prev=r.prev:u=r.prev,r.prev?r.prev.next=r.next:t=r.next)}}}}}function l(){u--,n&&0===u&&(n(),n=void 0,r.clear(),r=o)}let s={addNestedSub:function(e){a();let t=r.subscribe(e),n=!1;return()=>{n||(n=!0,t(),l())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:c,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,a())},tryUnsubscribe:function(){i&&(i=!1,l())},getListeners:()=>r};return s}(i);return{store:i,subscription:e,getServerState:u?()=>u:void 0}},[i,u]),l=r.useMemo(()=>i.getState(),[i]);return a(()=>{let{subscription:e}=c;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[c,l]),r.createElement((n||f).Provider,{value:c},t)};function v(e=f){return function(){return r.useContext(e)}}var d=v();function p(e=f){let t=e===f?d:v(e),n=()=>{let{store:e}=t();return e};return Object.assign(n,{withTypes:()=>n}),n}var y=p(),S=function(e=f){let t=e===f?y:p(e),n=()=>t().dispatch;return Object.assign(n,{withTypes:()=>n}),n}(),g=(e,t)=>e===t,x=function(e=f){let t=e===f?d:v(e),n=(e,n={})=>{let{equalityFn:o=g}="function"==typeof n?{equalityFn:n}:n,{store:i,subscription:c,getServerState:a}=t();r.useRef(!0);let l=r.useCallback({[e.name]:t=>e(t)}[e.name],[e]),s=(0,u.useSyncExternalStoreWithSelector)(c.addNestedSub,i.getState,a||i.getState,l,o);return r.useDebugValue(s),s};return Object.assign(n,{withTypes:()=>n}),n}()},4993:(e,t,n)=>{var r=n(2115),u="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useSyncExternalStore,i=r.useRef,c=r.useEffect,a=r.useMemo,l=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var f=i(null);if(null===f.current){var b={hasValue:!1,value:null};f.current=b}else b=f.current;var v=o(e,(f=a(function(){function e(e){if(!c){if(c=!0,o=e,e=r(e),void 0!==s&&b.hasValue){var t=b.value;if(s(t,e))return i=t}return i=e}if(t=i,u(o,e))return t;var n=r(e);return void 0!==s&&s(t,n)?(o=e,t):(o=e,i=n)}var o,i,c=!1,a=void 0===n?null:n;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,n,r,s]))[0],f[1]);return c(function(){b.hasValue=!0,b.value=v},[v]),l(v),v}}}]);