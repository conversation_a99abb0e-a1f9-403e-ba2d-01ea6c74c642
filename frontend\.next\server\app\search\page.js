(()=>{var a={};a.id=959,a.ids=[959],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2643:(a,b,c)=>{"use strict";c.d(b,{$:()=>e,A:()=>f});var d=c(60687);c(43210);let e=({variant:a="primary",size:b="md",loading:c=!1,disabled:e,children:f,className:g="",...h})=>(0,d.jsxs)("button",{className:`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 touch-manipulation select-none ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300",outline:"border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400 disabled:border-gray-200",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-blue-500 disabled:text-gray-400 disabled:hover:bg-transparent"}[a]} ${{sm:"px-3 py-2 text-sm min-h-[36px]",md:"px-4 py-2 text-sm min-h-[44px]",lg:"px-6 py-3 text-base min-h-[48px]"}[b]} disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none ${g}`,disabled:e||c,...h,children:[c&&(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"}),f]}),f=e},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11867:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,41951)),"C:\\Local_ecom\\frontend\\src\\app\\search\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Local_ecom\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Local_ecom\\frontend\\src\\app\\search\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/search/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19935:(a,b,c)=>{Promise.resolve().then(c.bind(c,97102))},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41951:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Local_ecom\\\\frontend\\\\src\\\\app\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\app\\search\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89255:(a,b,c)=>{"use strict";c.d(b,{q:()=>j});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c(2643);function i({product:a}){let[b,c]=(0,g.useState)(!1),[e,i]=(0,g.useState)(!1),j=a.originalPrice?Math.round((a.originalPrice-a.price)/a.originalPrice*100):a.discount||0,k=a=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",maximumFractionDigits:0}).format(a);return(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg hover:shadow-lg transition-shadow duration-200 group",children:[(0,d.jsxs)(f(),{href:a.id?`/products/${a.id}`:"/products",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("button",{onClick:a=>{a.preventDefault(),c(!b)},className:"absolute top-2 right-2 z-10 p-2 rounded-full bg-white shadow-md hover:bg-gray-50 transition-colors",children:(0,d.jsx)("svg",{className:`w-5 h-5 ${b?"text-red-500 fill-current":"text-gray-400"}`,fill:b?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),j>0&&(0,d.jsxs)("div",{className:"absolute top-2 left-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded",children:[j,"% off"]}),(0,d.jsx)("div",{className:"aspect-square p-4 flex items-center justify-center bg-gray-50",children:e?(0,d.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,d.jsx)("svg",{className:"w-16 h-16",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}):(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-contain group-hover:scale-105 transition-transform duration-200",onError:()=>i(!0)})})]}),(0,d.jsxs)("div",{className:"p-4",children:[a.brand&&(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:a.brand}),(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors",children:a.name}),a.features&&a.features.length>0&&(0,d.jsx)("ul",{className:"text-xs text-gray-600 mb-2 space-y-1",children:a.features.slice(0,2).map((a,b)=>(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-gray-400 rounded-full mr-2"}),a]},b))}),(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("div",{className:"flex items-center mr-2",children:(a=>{let b=[],c=Math.floor(a);for(let a=0;a<c;a++)b.push((0,d.jsx)("svg",{className:"w-4 h-4 text-yellow-400 fill-current",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})},a));a%1!=0&&b.push((0,d.jsxs)("svg",{className:"w-4 h-4 text-yellow-400",viewBox:"0 0 20 20",children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("linearGradient",{id:"half-fill",children:[(0,d.jsx)("stop",{offset:"50%",stopColor:"currentColor"}),(0,d.jsx)("stop",{offset:"50%",stopColor:"transparent"})]})}),(0,d.jsx)("path",{fill:"url(#half-fill)",d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})]},"half"));let e=5-Math.ceil(a);for(let a=0;a<e;a++)b.push((0,d.jsx)("svg",{className:"w-4 h-4 text-gray-300",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})},`empty-${a}`));return b})(a.rating)}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["(",a.reviewCount.toLocaleString(),")"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("span",{className:"text-lg font-bold text-gray-900",children:k(a.price)}),a.originalPrice&&(0,d.jsx)("span",{className:"text-sm text-gray-500 line-through",children:k(a.originalPrice)}),j>0&&(0,d.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[j,"% off"]})]}),(0,d.jsxs)("div",{className:"space-y-1 mb-3",children:[a.freeDelivery&&(0,d.jsxs)("div",{className:"flex items-center text-xs text-green-600",children:[(0,d.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Free Delivery"]}),a.exchangeOffer&&(0,d.jsxs)("div",{className:"flex items-center text-xs text-blue-600",children:[(0,d.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})}),"Exchange Offer"]})]})]})]}),(0,d.jsx)("div",{className:"px-4 pb-4",children:(0,d.jsx)(h.$,{size:"sm",className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium",onClick:b=>{b.preventDefault(),console.log("Added to cart:",a.id)},children:"Add to Cart"})})]})}function j({products:a,loading:b=!1}){return b?(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:Array.from({length:20}).map((a,b)=>(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg animate-pulse",children:[(0,d.jsx)("div",{className:"aspect-square bg-gray-200 rounded-t-lg"}),(0,d.jsxs)("div",{className:"p-4 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded"})]})]},b))}):0===a.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-4",children:(0,d.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"})})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]}):(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:a.map(a=>(0,d.jsx)(i,{product:a},a.id))})}},89671:(a,b,c)=>{Promise.resolve().then(c.bind(c,41951))},94735:a=>{"use strict";a.exports=require("events")},97102:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(60687),e=c(16189),f=c(43210);function g({placeholder:a="Search for products...",className:b="",onSearch:c,autoFocus:g=!1,initialValue:h="",categoryContext:i,brandContext:j,maxSuggestions:k=5}){let[l,m]=(0,f.useState)(h),[n,o]=(0,f.useState)([]),[p,q]=(0,f.useState)([]),[r,s]=(0,f.useState)(!1),[t,u]=(0,f.useState)(!1),[v,w]=(0,f.useState)(null);var x=l;let[y,z]=(0,f.useState)(x),A=(0,e.useRouter)(),B=(0,f.useRef)(null);return(0,d.jsxs)("div",{className:`relative ${b}`,ref:B,children:[(0,d.jsx)("form",{onSubmit:a=>{a.preventDefault(),l.trim()&&(c?c(l):A.push(`/products?search=${encodeURIComponent(l)}`),u(!1))},className:"w-full",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,d.jsx)("input",{type:"text",value:l,onChange:a=>{m(a.target.value),u(!0)},onFocus:()=>u(!0),onKeyDown:a=>{"Escape"===a.key&&u(!1)},placeholder:a,autoFocus:g,className:"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Search","aria-expanded":t,"aria-autocomplete":"list",role:"combobox"}),l&&(0,d.jsx)("button",{type:"button",onClick:()=>{m(""),o([]),q([]),w(null)},className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":"Clear search",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,d.jsx)("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":"Search",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),t&&l.length>=2&&(0,d.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto",role:"listbox",children:r?(0,d.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,d.jsx)("div",{className:"inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"}),"Loading suggestions..."]}):v?(0,d.jsxs)("div",{className:"p-4 text-center text-red-500",children:[(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),v]}):(0,d.jsx)(d.Fragment,{children:0===n.length&&0===p.length?(0,d.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No suggestions found"}):(0,d.jsxs)(d.Fragment,{children:[n.length>0&&(0,d.jsxs)("div",{className:"p-2",children:[(0,d.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-1",children:"Suggestions"}),(0,d.jsx)("ul",{children:n.map((a,b)=>(0,d.jsx)("li",{role:"option",children:(0,d.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",onClick:()=>{m(a),c?c(a):A.push(`/products?search=${encodeURIComponent(a)}`),u(!1)},children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,d.jsx)("span",{dangerouslySetInnerHTML:{__html:a.replace(RegExp(`(${l})`,"gi"),'<span class="font-semibold text-blue-600">$1</span>')}})]})})},b))})]}),p.length>0&&(0,d.jsxs)("div",{className:`p-2 ${n.length>0?"border-t border-gray-200":""}`,children:[(0,d.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-1",children:"Products"}),(0,d.jsx)("ul",{children:p.map(a=>(0,d.jsx)("li",{role:"option",children:(0,d.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",onClick:()=>{A.push(`/products/${a.slug}`),u(!1)},children:(0,d.jsxs)("div",{className:"flex items-center",children:[a.image?(0,d.jsx)("div",{className:"h-12 w-12 relative mr-3 flex-shrink-0",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"h-full w-full object-cover rounded",loading:"lazy"})}):(0,d.jsx)("div",{className:"h-12 w-12 bg-gray-200 mr-3 flex-shrink-0 rounded flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-500 text-xs",children:"No image"})}),(0,d.jsxs)("div",{className:"flex-grow min-w-0",children:[(0,d.jsx)("div",{className:"font-medium truncate",children:(0,d.jsx)("span",{dangerouslySetInnerHTML:{__html:a.name.replace(RegExp(`(${l})`,"gi"),'<span class="font-semibold text-blue-600">$1</span>')}})}),(0,d.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,d.jsxs)("span",{className:"font-medium",children:["$",a.price.toFixed(2)]}),a.category&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"mx-1",children:"•"}),(0,d.jsx)("span",{className:"truncate",children:a.category})]})]})]})]})})},a.id))})]})]})})})]})}function h({className:a="",onFilterChange:b,initialFilters:c={}}){let[g,h]=(0,f.useState)({}),[i,j]=(0,f.useState)(c),[k,l]=(0,f.useState)(!1),[m,n]=(0,f.useState)(null),[o,p]=(0,f.useState)([null,null]),q=(0,e.useRouter)(),r=(0,e.useSearchParams)(),s=a=>{p(a);let b={...i};b.price_range=a,t(b)},t=a=>{j(a),b&&b(a)},u=Object.keys(i).length>0;return(0,d.jsxs)("div",{className:`bg-white rounded-lg shadow p-4 ${a}`,children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold",children:"Filters"}),u&&(0,d.jsx)("button",{onClick:()=>{if(j({}),p([null,null]),b)b({});else{let a=r.get("search");a?q.push(`/products?search=${encodeURIComponent(a)}`):q.push("/products")}},className:"text-sm text-blue-600 hover:text-blue-800",children:"Clear All"})]}),k?(0,d.jsxs)("div",{className:"py-4 text-center",children:[(0,d.jsx)("div",{className:"inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"}),(0,d.jsx)("span",{className:"text-gray-500",children:"Loading filters..."})]}):m?(0,d.jsxs)("div",{className:"py-4 text-center text-red-500",children:[(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),m]}):(0,d.jsxs)(d.Fragment,{children:[g.categories&&g.categories.length>0&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Categories"}),(0,d.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:g.categories.map(a=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:`category-${a.name}`,checked:i.category===a.name,onChange:()=>(a=>{let b={...i};b.category===a?delete b.category:b.category=a,t(b)})(a.name),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsxs)("label",{htmlFor:`category-${a.name}`,className:"ml-2 text-sm text-gray-700",children:[a.name," ",(0,d.jsxs)("span",{className:"text-gray-500",children:["(",a.count,")"]})]})]},a.name))})]}),g.brands&&g.brands.length>0&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Brands"}),(0,d.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:g.brands.map(a=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:`brand-${a.name}`,checked:i.brand===a.name,onChange:()=>(a=>{let b={...i};b.brand===a?delete b.brand:b.brand=a,t(b)})(a.name),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsxs)("label",{htmlFor:`brand-${a.name}`,className:"ml-2 text-sm text-gray-700",children:[a.name," ",(0,d.jsxs)("span",{className:"text-gray-500",children:["(",a.count,")"]})]})]},a.name))})]}),g.price_ranges&&g.price_ranges.length>0&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,d.jsxs)("div",{className:"space-y-2",children:[g.price_ranges.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",id:`price-range-${b}`,name:"price-range",checked:o[0]===a.from&&o[1]===a.to,onChange:()=>s([a.from,a.to]),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsxs)("label",{htmlFor:`price-range-${b}`,className:"ml-2 text-sm text-gray-700",children:[a.label," ",(0,d.jsxs)("span",{className:"text-gray-500",children:["(",a.count,")"]})]})]},b)),(0,d.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"number",placeholder:"Min",value:null!==o[0]?o[0]:"",onChange:a=>{s([a.target.value?parseFloat(a.target.value):null,o[1]])},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded",min:"0"}),(0,d.jsx)("span",{className:"text-gray-500",children:"to"}),(0,d.jsx)("input",{type:"number",placeholder:"Max",value:null!==o[1]?o[1]:"",onChange:a=>{let b=a.target.value?parseFloat(a.target.value):null;s([o[0],b])},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded",min:"0"})]})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Additional Filters"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"discount-only",checked:!!i.discount_only,onChange:a=>(a=>{let b={...i};a?b.discount_only=!0:delete b.discount_only,t(b)})(a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"discount-only",className:"ml-2 text-sm text-gray-700",children:"Discounted Items Only"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"featured-only",checked:!!i.is_featured,onChange:a=>(a=>{let b={...i};a?b.is_featured=!0:delete b.is_featured,t(b)})(a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"featured-only",className:"ml-2 text-sm text-gray-700",children:"Featured Items Only"})]})]})]})]})]})}c(17327),c(7791);var i=c(89255);function j({pagination:a,onPageChange:b}){let{current_page:c,total_pages:e}=a;return e<=1?null:(0,d.jsx)("nav",{className:"flex justify-center mt-8",children:(0,d.jsxs)("ul",{className:"flex items-center space-x-1",children:[(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>b(c-1),disabled:1===c,className:`px-3 py-2 rounded-md text-sm ${1===c?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,"aria-label":"Previous page",children:(0,d.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})}),(()=>{let a=[];a.push(1);let b=Math.max(2,c-Math.floor(2.5)),d=Math.min(e-1,b+5-2);b<=2&&(b=2,d=Math.min(e-1,5)),d>=e-1&&(d=e-1,b=Math.max(2,e-5+1)),b>2&&a.push("ellipsis-start");for(let c=b;c<=d;c++)a.push(c);return d<e-1&&a.push("ellipsis-end"),e>1&&a.push(e),a})().map((a,e)=>(0,d.jsx)("li",{children:"ellipsis-start"===a||"ellipsis-end"===a?(0,d.jsx)("span",{className:"px-3 py-2 text-gray-500",children:"..."}):(0,d.jsx)("button",{onClick:()=>b(a),className:`px-3 py-2 rounded-md text-sm ${c===a?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"}`,children:a})},`page-${a}-${e}`)),(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>b(c+1),disabled:c===e,className:`px-3 py-2 rounded-md text-sm ${c===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,"aria-label":"Next page",children:(0,d.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})})]})})}function k({className:a="",initialQuery:b="",initialFilters:c={}}){let[g,h]=(0,f.useState)([]),[k,l]=(0,f.useState)(!1),[m,n]=(0,f.useState)(null),[o,p]=(0,f.useState)(0),[q,r]=(0,f.useState)(1),[s,t]=(0,f.useState)(20),[u,v]=(0,f.useState)(1),[w,x]=(0,f.useState)("relevance"),[y,z]=(0,f.useState)(b),[A,B]=(0,f.useState)(c);return(0,e.useRouter)(),(0,e.useSearchParams)(),(0,d.jsxs)("div",{className:a,children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[(0,d.jsxs)("div",{className:"mb-4 sm:mb-0",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:y?`Search results for "${y}"`:"All Products"}),(0,d.jsx)("p",{className:"text-gray-500 text-sm mt-1",children:k?"Loading...":`${o} products found`})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("label",{htmlFor:"sort-by",className:"text-sm text-gray-600 mr-2",children:"Sort by:"}),(0,d.jsxs)("select",{id:"sort-by",value:w,onChange:a=>{x(a.target.value)},className:"text-sm border border-gray-300 rounded-md py-1 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:k,children:[y&&(0,d.jsx)("option",{value:"relevance",children:"Relevance"}),(0,d.jsx)("option",{value:"price_asc",children:"Price: Low to High"}),(0,d.jsx)("option",{value:"price_desc",children:"Price: High to Low"}),(0,d.jsx)("option",{value:"created_at",children:"Newest First"}),(0,d.jsx)("option",{value:"-created_at",children:"Oldest First"}),(0,d.jsx)("option",{value:"discount",children:"Biggest Discount"})]})]})]}),k?(0,d.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,d.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-2"}),(0,d.jsx)("span",{className:"text-gray-500",children:"Loading search results..."})]}):m?(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 text-center text-red-600",children:[(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),m]}):0===g.length?(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-8 text-center",children:[(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"No products found"}),(0,d.jsx)("p",{className:"text-gray-500",children:y?`We couldn't find any products matching "${y}". Try using different keywords or filters.`:"No products match the selected filters. Try changing your filter options."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.q,{products:g}),u>1&&(0,d.jsx)("div",{className:"mt-8",children:(0,d.jsx)(j,{pagination:{current_page:q,total_pages:u,count:o,page_size:s,next:q<u?`?page=${q+1}`:null,previous:q>1?`?page=${q-1}`:null},onPageChange:a=>{r(a)}})})]})]})}function l(){let a=(0,e.useSearchParams)().get("search")||"";return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)(g,{initialValue:a,autoFocus:!a,className:"max-w-3xl mx-auto"})}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,d.jsx)("div",{className:"w-full md:w-64 flex-shrink-0",children:(0,d.jsx)(f.Suspense,{fallback:(0,d.jsx)("div",{children:"Loading filters..."}),children:(0,d.jsx)(h,{})})}),(0,d.jsx)("div",{className:"flex-grow",children:(0,d.jsx)(f.Suspense,{fallback:(0,d.jsx)("div",{children:"Loading results..."}),children:(0,d.jsx)(k,{initialQuery:a})})})]})]})}function m(){return(0,d.jsx)(f.Suspense,{fallback:(0,d.jsx)("div",{children:"Loading..."}),children:(0,d.jsx)(l,{})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,503,418,998],()=>b(b.s=11867));module.exports=c})();