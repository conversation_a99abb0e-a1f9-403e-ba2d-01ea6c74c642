(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9413],{5695:(e,s,u)=>{"use strict";var r=u(8999);u.o(r,"useParams")&&u.d(s,{useParams:function(){return r.useParams}}),u.o(r,"usePathname")&&u.d(s,{usePathname:function(){return r.usePathname}}),u.o(r,"useRouter")&&u.d(s,{useRouter:function(){return r.useRouter}}),u.o(r,"useSearchParams")&&u.d(s,{useSearchParams:function(){return r.useSearchParams}})},6195:(e,s,u)=>{"use strict";u.r(s),u.d(s,{default:()=>o});var r=u(5155);u(2115);var n=u(5695),a=u(3865),t=u(7141);function o(){let e=(0,n.useRouter)();return(0,r.jsx)("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f5f5f5",padding:"20px"},children:(0,r.jsx)(a.F,{onSuccess:()=>{},onBackToLogin:()=>{e.push(t.bw.LOGIN)}})})}},7858:(e,s,u)=>{Promise.resolve().then(u.bind(u,6195))}},e=>{e.O(0,[3464,3568,2834,2125,7,3865,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=7858)),_N_E=e.O()}]);