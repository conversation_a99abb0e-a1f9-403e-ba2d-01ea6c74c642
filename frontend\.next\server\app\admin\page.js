(()=>{var a={};a.id=698,a.ids=[698],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1132:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Local_ecom\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\app\\admin\\page.tsx","default")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9418:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(60687),e=c(83033),f=c(43210),g=c(31158),h=c(23928),i=c(28561),j=c(41312),k=c(62688);let l=(0,k.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var m=c(19080),n=c(35029),o=c(25541);let p=(0,k.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),q={blue:"text-blue-500 bg-blue-100",green:"text-green-500 bg-green-100",purple:"text-purple-500 bg-purple-100",yellow:"text-yellow-500 bg-yellow-100",red:"text-red-500 bg-red-100"};function r({title:a,value:b,change:c,changeType:e,icon:f,color:g}){return(0,d.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,d.jsx)("div",{className:"p-5",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:`p-3 rounded-md ${q[g]}`,children:(0,d.jsx)(f,{className:"h-6 w-6"})})}),(0,d.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:a}),(0,d.jsxs)("dd",{className:"flex items-baseline",children:[(0,d.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:b}),void 0!==c&&(0,d.jsxs)("div",{className:`ml-2 flex items-baseline text-sm font-semibold ${"increase"===e?"text-green-600":"text-red-600"}`,children:["increase"===e?(0,d.jsx)(o.A,{className:"self-center flex-shrink-0 h-4 w-4 mr-1"}):(0,d.jsx)(p,{className:"self-center flex-shrink-0 h-4 w-4 mr-1"}),(0,d.jsxs)("span",{className:"sr-only",children:["increase"===e?"Increased":"Decreased"," by"]}),Math.abs(c).toFixed(1),"%"]})]})]})})]})})})}var s=c(41953);let t={pending:"#f59e0b",confirmed:"#3b82f6",shipped:"#8b5cf6",delivered:"#10b981",cancelled:"#ef4444",returned:"#f97316"},u={pending:"Pending",confirmed:"Confirmed",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled",returned:"Returned"};function v({data:a}){let b=Object.values(a).reduce((a,b)=>a+b,0);if(0===b)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No order data available"});let c=0,e=Object.entries(a).map(([a,d])=>{let e=d/b*100,f={status:a,count:d,percentage:e,color:t[a]||"#6b7280",label:u[a]||a,startAngle:3.6*c};return c+=e,f});return(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsxs)("div",{className:"relative w-48 h-48",children:[(0,d.jsxs)("svg",{className:"w-full h-full transform -rotate-90",viewBox:"0 0 100 100",children:[(0,d.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#f3f4f6",strokeWidth:"8"}),e.map((a,b)=>{let c=2*Math.PI*40,f=`${a.percentage/100*c} ${c}`,g=-(e.slice(0,b).reduce((a,b)=>a+b.percentage,0)/100*c);return(0,d.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:a.color,strokeWidth:"8",strokeDasharray:f,strokeDashoffset:g,className:"transition-all duration-300"},a.status)})]}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:b}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Total Orders"})]})})]}),(0,d.jsx)("div",{className:"space-y-2",children:e.map(a=>(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:a.color}}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.label}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[a.count," (",a.percentage.toFixed(1),"%)"]})]})]},a.status))})]})}var w=c(9776);function x({dateRange:a}){let[b,c]=(0,f.useState)([]),[e,g]=(0,f.useState)(!0);return e?(0,d.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,d.jsx)(w.A,{})}):b.length?(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quantity Sold"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Revenue"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map((a,b)=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",b+1]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.product__name}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:["SKU: ",a.product__sku]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.product__category__name}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.total_quantity.toLocaleString()}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["$",a.total_revenue.toLocaleString()]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.order_count.toLocaleString()}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",a.product__price.toFixed(2)]})]},a.product__id))})]})}):(0,d.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No product data available for the selected period"})}var y=c(93409);let z=(0,k.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),A=(0,k.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var B=c(58559);function C(){let[a,b]=(0,f.useState)(null),[c,e]=(0,f.useState)(!0);if(c)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)(w.A,{})});if(!a)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Unable to load system health data"});let g=a=>{switch(a){case"healthy":return(0,d.jsx)(z,{className:"h-6 w-6 text-green-500"});case"warning":return(0,d.jsx)(l,{className:"h-6 w-6 text-yellow-500"});case"critical":return(0,d.jsx)(A,{className:"h-6 w-6 text-red-500"});default:return(0,d.jsx)(B.A,{className:"h-6 w-6 text-gray-500"})}},h=[{label:"Response Time",current:`${a.current_metrics.response_time.toFixed(0)}ms`,average:`${a.period_summary.avg_response_time?.toFixed(0)||0}ms`,status:a.current_metrics.response_time>1e3?"warning":"healthy"},{label:"Error Rate",current:`${a.current_metrics.error_rate.toFixed(1)}%`,average:`${a.period_summary.avg_error_rate?.toFixed(1)||0}%`,status:a.current_metrics.error_rate>5?"critical":a.current_metrics.error_rate>2?"warning":"healthy"},{label:"Active Users",current:a.current_metrics.active_users.toString(),average:Math.round(a.period_summary.avg_active_users||0).toString(),status:"healthy"},{label:"Memory Usage",current:`${a.current_metrics.memory_usage.toFixed(1)}%`,average:"N/A",status:a.current_metrics.memory_usage>80?"critical":a.current_metrics.memory_usage>60?"warning":"healthy"},{label:"CPU Usage",current:`${a.current_metrics.cpu_usage.toFixed(1)}%`,average:"N/A",status:a.current_metrics.cpu_usage>80?"critical":a.current_metrics.cpu_usage>60?"warning":"healthy"}];return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[g(a.health_status),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"System Status"}),(0,d.jsx)("p",{className:`text-sm font-medium px-2 py-1 rounded-full inline-block ${(a=>{switch(a){case"healthy":return"text-green-600 bg-green-100";case"warning":return"text-yellow-600 bg-yellow-100";case"critical":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(a.health_status)}`,children:a.health_status.charAt(0).toUpperCase()+a.health_status.slice(1)})]})]}),(0,d.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:h.map(a=>(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a.label}),g(a.status)]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:a.current}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["24h avg: ",a.average]})]})]},a.label))}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.period_summary.max_active_users||0}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Peak Users (24h)"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[((1-(a.period_summary.avg_error_rate||0)/100)*100).toFixed(2),"%"]}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Uptime (24h)"})]})]})]})}var D=c(53504),E=c(37590);function F(){let[a,b]=(0,f.useState)(null),[c,e]=(0,f.useState)(!0),[k,o]=(0,f.useState)({from:new Date(Date.now()-2592e6).toISOString().split("T")[0],to:new Date().toISOString().split("T")[0]}),p=async()=>{try{e(!0);let a=await n.i.getDashboardMetrics(k.from,k.to);b(a)}catch(a){console.error("Failed to fetch dashboard metrics:",a),E.Ay.error("Failed to load dashboard data")}finally{e(!1)}},q=async()=>{try{await n.i.exportReport({report_type:"sales",export_format:"csv",date_from:k.from,date_to:k.to}),E.Ay.success("Report export started. You will be notified when ready.")}catch(a){console.error("Failed to export report:",a),E.Ay.error("Failed to export report")}};return c?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)(w.A,{size:"lg"})}):a?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Overview of your ecommerce platform performance"})]}),(0,d.jsxs)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-3",children:[(0,d.jsx)(D.A,{from:k.from,to:k.to,onChange:(a,b)=>{o({from:a,to:b})}}),(0,d.jsxs)("button",{onClick:q,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)(r,{title:"Total Revenue",value:`$${a.sales.total_revenue.toLocaleString()}`,change:a.sales.revenue_growth,changeType:a.sales.revenue_growth>=0?"increase":"decrease",icon:h.A,color:"green"}),(0,d.jsx)(r,{title:"Total Orders",value:a.sales.total_orders.toLocaleString(),icon:i.A,color:"blue"}),(0,d.jsx)(r,{title:"New Customers",value:a.customers.new_customers.toLocaleString(),icon:j.A,color:"purple"}),(0,d.jsx)(r,{title:"Low Stock Items",value:a.inventory.low_stock_products.toLocaleString(),icon:l,color:"yellow"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Sales Trend"}),(0,d.jsx)(s.A,{dateRange:k})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Order Status Distribution"}),(0,d.jsx)(v,{data:a.orders_by_status})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Customer Lifecycle"}),(0,d.jsx)(y.A,{})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"System Health"}),(0,d.jsx)(C,{})]})]}),(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Top Selling Products"})}),(0,d.jsx)(x,{dateRange:k})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(j.A,{className:"h-8 w-8 text-blue-500"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Customers"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:a.customers.total_customers.toLocaleString()})]})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(m.A,{className:"h-8 w-8 text-green-500"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Products"}),(0,d.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:a.inventory.total_products.toLocaleString()})]})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-purple-500"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Inventory Value"}),(0,d.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:["$",a.inventory.total_inventory_value.toLocaleString()]})]})]})})]})]}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("p",{className:"text-gray-500",children:"Failed to load dashboard data"}),(0,d.jsx)("button",{onClick:p,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Retry"})]})}function G(){return(0,d.jsx)(e.OV,{allowedUserTypes:["admin"],children:(0,d.jsx)(F,{})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19045:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1132)),"C:\\Local_ecom\\frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,99111)),"C:\\Local_ecom\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Local_ecom\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Local_ecom\\frontend\\src\\app\\admin\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23928:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30210:(a,b,c)=>{Promise.resolve().then(c.bind(c,1132))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48362:(a,b,c)=>{Promise.resolve().then(c.bind(c,9418))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,503,418,8,998,677,595,567,33,473],()=>b(b.s=19045));module.exports=c})();