(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{5868:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5899:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var r=s(5155),a=s(5493),l=s(2115),c=s(1788),i=s(5868),d=s(7809),n=s(7580),o=s(9946);let x=(0,o.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var m=s(7108),h=s(4615),u=s(3109);let g=(0,o.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),p={blue:"text-blue-500 bg-blue-100",green:"text-green-500 bg-green-100",purple:"text-purple-500 bg-purple-100",yellow:"text-yellow-500 bg-yellow-100",red:"text-red-500 bg-red-100"};function y(e){let{title:t,value:s,change:a,changeType:l,icon:c,color:i}=e;return(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"p-3 rounded-md ".concat(p[i]),children:(0,r.jsx)(c,{className:"h-6 w-6"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:t}),(0,r.jsxs)("dd",{className:"flex items-baseline",children:[(0,r.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:s}),void 0!==a&&(0,r.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold ".concat("increase"===l?"text-green-600":"text-red-600"),children:["increase"===l?(0,r.jsx)(u.A,{className:"self-center flex-shrink-0 h-4 w-4 mr-1"}):(0,r.jsx)(g,{className:"self-center flex-shrink-0 h-4 w-4 mr-1"}),(0,r.jsxs)("span",{className:"sr-only",children:["increase"===l?"Increased":"Decreased"," by"]}),Math.abs(a).toFixed(1),"%"]})]})]})})]})})})}var j=s(2083);let f={pending:"#f59e0b",confirmed:"#3b82f6",shipped:"#8b5cf6",delivered:"#10b981",cancelled:"#ef4444",returned:"#f97316"},v={pending:"Pending",confirmed:"Confirmed",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled",returned:"Returned"};function N(e){let{data:t}=e,s=Object.values(t).reduce((e,t)=>e+t,0);if(0===s)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No order data available"});let a=0,l=Object.entries(t).map(e=>{let[t,r]=e,l=r/s*100,c={status:t,count:r,percentage:l,color:f[t]||"#6b7280",label:v[t]||t,startAngle:3.6*a};return a+=l,c});return(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)("div",{className:"relative w-48 h-48",children:[(0,r.jsxs)("svg",{className:"w-full h-full transform -rotate-90",viewBox:"0 0 100 100",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#f3f4f6",strokeWidth:"8"}),l.map((e,t)=>{let s=2*Math.PI*40,a="".concat(e.percentage/100*s," ").concat(s),c=-(l.slice(0,t).reduce((e,t)=>e+t.percentage,0)/100*s);return(0,r.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:e.color,strokeWidth:"8",strokeDasharray:a,strokeDashoffset:c,className:"transition-all duration-300"},e.status)})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:s}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Orders"})]})})]}),(0,r.jsx)("div",{className:"space-y-2",children:l.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.color}}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.label}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.count," (",e.percentage.toFixed(1),"%)"]})]})]},e.status))})]})}var b=s(4338);function w(e){let{dateRange:t}=e,[s,a]=(0,l.useState)([]),[c,i]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{(async()=>{try{i(!0);let e=await h.i.getTopSellingProducts(t.from,t.to,10);a(e)}catch(e){console.error("Failed to fetch top products:",e)}finally{i(!1)}})()},[t]),c)?(0,r.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,r.jsx)(b.A,{})}):s.length?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quantity Sold"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Revenue"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((e,t)=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",t+1]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.product__name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["SKU: ",e.product__sku]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.product__category__name}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.total_quantity.toLocaleString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["$",e.total_revenue.toLocaleString()]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.order_count.toLocaleString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.product__price.toFixed(2)]})]},e.product__id))})]})}):(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No product data available for the selected period"})}var _=s(1675);let k=(0,o.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),S=(0,o.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var A=s(9397);function F(){var e,t;let[s,a]=(0,l.useState)(null),[c,i]=(0,l.useState)(!0);if((0,l.useEffect)(()=>{let e=async()=>{try{i(!0);let e=await h.i.getSystemHealth(24);a(e)}catch(e){console.error("Failed to fetch system health:",e)}finally{i(!1)}};e();let t=setInterval(e,3e4);return()=>clearInterval(t)},[]),c)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(b.A,{})});if(!s)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Unable to load system health data"});let d=e=>{switch(e){case"healthy":return(0,r.jsx)(k,{className:"h-6 w-6 text-green-500"});case"warning":return(0,r.jsx)(x,{className:"h-6 w-6 text-yellow-500"});case"critical":return(0,r.jsx)(S,{className:"h-6 w-6 text-red-500"});default:return(0,r.jsx)(A.A,{className:"h-6 w-6 text-gray-500"})}},n=[{label:"Response Time",current:"".concat(s.current_metrics.response_time.toFixed(0),"ms"),average:"".concat((null==(e=s.period_summary.avg_response_time)?void 0:e.toFixed(0))||0,"ms"),status:s.current_metrics.response_time>1e3?"warning":"healthy"},{label:"Error Rate",current:"".concat(s.current_metrics.error_rate.toFixed(1),"%"),average:"".concat((null==(t=s.period_summary.avg_error_rate)?void 0:t.toFixed(1))||0,"%"),status:s.current_metrics.error_rate>5?"critical":s.current_metrics.error_rate>2?"warning":"healthy"},{label:"Active Users",current:s.current_metrics.active_users.toString(),average:Math.round(s.period_summary.avg_active_users||0).toString(),status:"healthy"},{label:"Memory Usage",current:"".concat(s.current_metrics.memory_usage.toFixed(1),"%"),average:"N/A",status:s.current_metrics.memory_usage>80?"critical":s.current_metrics.memory_usage>60?"warning":"healthy"},{label:"CPU Usage",current:"".concat(s.current_metrics.cpu_usage.toFixed(1),"%"),average:"N/A",status:s.current_metrics.cpu_usage>80?"critical":s.current_metrics.cpu_usage>60?"warning":"healthy"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[d(s.health_status),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:"System Status"}),(0,r.jsx)("p",{className:"text-sm font-medium px-2 py-1 rounded-full inline-block ".concat((e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"warning":return"text-yellow-600 bg-yellow-100";case"critical":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(s.health_status)),children:s.health_status.charAt(0).toUpperCase()+s.health_status.slice(1)})]})]}),(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:n.map(e=>(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),d(e.status)]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e.current}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["24h avg: ",e.average]})]})]},e.label))}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.period_summary.max_active_users||0}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Peak Users (24h)"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[((1-(s.period_summary.avg_error_rate||0)/100)*100).toFixed(2),"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Uptime (24h)"})]})]})]})}var L=s(1015),C=s(3568);function T(){let[e,t]=(0,l.useState)(null),[s,a]=(0,l.useState)(!0),[o,u]=(0,l.useState)({from:new Date(Date.now()-2592e6).toISOString().split("T")[0],to:new Date().toISOString().split("T")[0]}),g=async()=>{try{a(!0);let e=await h.i.getDashboardMetrics(o.from,o.to);t(e)}catch(e){console.error("Failed to fetch dashboard metrics:",e),C.Ay.error("Failed to load dashboard data")}finally{a(!1)}};(0,l.useEffect)(()=>{g()},[o]);let p=async()=>{try{await h.i.exportReport({report_type:"sales",export_format:"csv",date_from:o.from,date_to:o.to}),C.Ay.success("Report export started. You will be notified when ready.")}catch(e){console.error("Failed to export report:",e),C.Ay.error("Failed to export report")}};return s?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(b.A,{size:"lg"})}):e?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Overview of your ecommerce platform performance"})]}),(0,r.jsxs)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-3",children:[(0,r.jsx)(L.A,{from:o.from,to:o.to,onChange:(e,t)=>{u({from:e,to:t})}}),(0,r.jsxs)("button",{onClick:p,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(y,{title:"Total Revenue",value:"$".concat(e.sales.total_revenue.toLocaleString()),change:e.sales.revenue_growth,changeType:e.sales.revenue_growth>=0?"increase":"decrease",icon:i.A,color:"green"}),(0,r.jsx)(y,{title:"Total Orders",value:e.sales.total_orders.toLocaleString(),icon:d.A,color:"blue"}),(0,r.jsx)(y,{title:"New Customers",value:e.customers.new_customers.toLocaleString(),icon:n.A,color:"purple"}),(0,r.jsx)(y,{title:"Low Stock Items",value:e.inventory.low_stock_products.toLocaleString(),icon:x,color:"yellow"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Sales Trend"}),(0,r.jsx)(j.A,{dateRange:o})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Order Status Distribution"}),(0,r.jsx)(N,{data:e.orders_by_status})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Customer Lifecycle"}),(0,r.jsx)(_.A,{})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"System Health"}),(0,r.jsx)(F,{})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Top Selling Products"})}),(0,r.jsx)(w,{dateRange:o})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n.A,{className:"h-8 w-8 text-blue-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Customers"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:e.customers.total_customers.toLocaleString()})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-green-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Total Products"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:e.inventory.total_products.toLocaleString()})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-purple-500"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Inventory Value"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:["$",e.inventory.total_inventory_value.toLocaleString()]})]})]})})]})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"Failed to load dashboard data"}),(0,r.jsx)("button",{onClick:g,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Retry"})]})}function O(){return(0,r.jsx)(a.OV,{allowedUserTypes:["admin"],children:(0,r.jsx)(T,{})})}},9350:(e,t,s)=>{Promise.resolve().then(s.bind(s,5899))},9397:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{e.O(0,[3464,4540,1990,7244,3568,2834,2125,6322,7,8359,3865,5493,6664,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=9350)),_N_E=e.O()}]);