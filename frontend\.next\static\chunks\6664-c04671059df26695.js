"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6664],{1015:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(5155);let s=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function l(e){let{from:t,to:a,onChange:l}=e,c=e=>{let t=new Date;l(new Date(t.getTime()-24*e*36e5).toISOString().split("T")[0],t.toISOString().split("T")[0])};return(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>c(7),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"7D"}),(0,r.jsx)("button",{onClick:()=>c(30),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"30D"}),(0,r.jsx)("button",{onClick:()=>c(90),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"90D"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 border border-gray-300 rounded-md px-3 py-2 bg-white",children:[(0,r.jsx)(s,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"date",value:t,onChange:e=>{l(e.target.value,a)},className:"text-sm border-none outline-none bg-transparent"}),(0,r.jsx)("span",{className:"text-gray-400",children:"to"}),(0,r.jsx)("input",{type:"date",value:a,onChange:e=>{l(t,e.target.value)},className:"text-sm border-none outline-none bg-transparent"})]})]})}},1675:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5155),s=a(2115),l=a(4615),c=a(4338);let n={new:"#3b82f6",active:"#10b981",at_risk:"#f59e0b",dormant:"#6b7280",churned:"#ef4444",vip:"#8b5cf6"},o={new:"New",active:"Active",at_risk:"At Risk",dormant:"Dormant",churned:"Churned",vip:"VIP"};function i(){var e;let[t,a]=(0,s.useState)(null),[i,d]=(0,s.useState)(!0);if((0,s.useEffect)(()=>{(async()=>{try{d(!0);let e=await l.i.getCustomerAnalyticsSummary();a(e)}catch(e){console.error("Failed to fetch customer analytics:",e)}finally{d(!1)}})()},[]),i)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(c.A,{})});if(!t||!t.lifecycle_distribution.length)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No customer lifecycle data available"});let m=t.lifecycle_distribution.reduce((e,t)=>e+t.count,0),u=Math.max(...t.lifecycle_distribution.map(e=>e.count));return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"space-y-3",children:t.lifecycle_distribution.map(e=>{let t=m>0?e.count/m*100:0,a=u>0?e.count/u*100:0;return(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"font-medium text-gray-700",children:o[e.lifecycle_stage]||e.lifecycle_stage}),(0,r.jsxs)("span",{className:"text-gray-500",children:[e.count," (",t.toFixed(1),"%)"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"h-3 rounded-full transition-all duration-300",style:{width:"".concat(a,"%"),backgroundColor:n[e.lifecycle_stage]||"#6b7280"}})})]},e.lifecycle_stage)})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:m}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Customers"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==(e=t.lifecycle_distribution.find(e=>"active"===e.lifecycle_stage))?void 0:e.count)||0}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Active Customers"})]})]}),t.top_customers.length>0&&(0,r.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Top Customers by Lifetime Value"}),(0,r.jsx)("div",{className:"space-y-2",children:t.top_customers.slice(0,5).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-gray-400",children:["#",t+1]}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.email}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("vip"===e.lifecycle_stage?"bg-purple-100 text-purple-800":"active"===e.lifecycle_stage?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:o[e.lifecycle_stage]||e.lifecycle_stage})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:["$",e.lifetime_value.toLocaleString()]}),(0,r.jsxs)("div",{className:"text-gray-500",children:[e.total_orders," orders"]})]})]},e.customer_id))})]})]})}},1788:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2083:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(5155),s=a(2115),l=a(4615),c=a(4338);function n(e){let{dateRange:t}=e,[a,n]=(0,s.useState)(null),[o,i]=(0,s.useState)(!0);if((0,s.useEffect)(()=>{(async()=>{try{i(!0);let e=await l.i.getSalesReport({date_from:t.from,date_to:t.to});n(e)}catch(e){console.error("Failed to fetch sales data:",e)}finally{i(!1)}})()},[t]),o)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(c.A,{})});if(!a||!a.daily_breakdown.length)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No sales data available for the selected period"});let d=Math.max(...a.daily_breakdown.map(e=>e.revenue));return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-64 flex items-end space-x-2 overflow-x-auto",children:a.daily_breakdown.map((e,t)=>{let a=e.revenue/d*100;return(0,r.jsx)("div",{className:"flex flex-col items-center min-w-0 flex-1",children:(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-8 bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600",style:{height:"".concat(a,"%")},title:"".concat(e.day,": $").concat(e.revenue.toLocaleString())}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-2 transform -rotate-45 origin-left",children:new Date(e.day).toLocaleDateString("en-US",{month:"short",day:"numeric"})})]})},t)})}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["Revenue: $",a.summary.total_revenue.toLocaleString()]}),(0,r.jsxs)("span",{children:["Orders: ",a.summary.total_orders.toLocaleString()]}),(0,r.jsxs)("span",{children:["Avg: $",a.summary.average_order_value.toFixed(2)]})]})]})}},3109:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4338:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(5155);let s={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"};function l(e){let{size:t="md",className:a=""}=e;return(0,r.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ".concat(s[t]," ").concat(a)})}},4615:(e,t,a)=>{a.d(t,{i:()=>l});var r=a(2302);let s="/api/analytics",l={async getDashboardMetrics(e,t){let a=new URLSearchParams;e&&a.append("date_from",e),t&&a.append("date_to",t);let l=await r.uE.get("".concat(s,"/dashboard_metrics/?").concat(a.toString()));if(!l.success||!l.data){var c;throw Error((null==(c=l.error)?void 0:c.message)||"Failed to fetch dashboard metrics")}return l.data},async getSalesReport(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await r.uE.get("".concat(s,"/sales_report/?").concat(t.toString()));if(!a.success||!a.data){var l;throw Error((null==(l=a.error)?void 0:l.message)||"Failed to fetch sales report")}return a.data},async getTopSellingProducts(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,l=new URLSearchParams;e&&l.append("date_from",e),t&&l.append("date_to",t),l.append("limit",a.toString());let c=await r.uE.get("".concat(s,"/top_selling_products/?").concat(l.toString()));if(!c.success||!c.data){var n;throw Error((null==(n=c.error)?void 0:n.message)||"Failed to fetch top selling products")}return c.data},async getCustomerAnalyticsSummary(){let e=await r.uE.get("".concat(s,"/customer_analytics_summary/"));if(!e.success||!e.data){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Failed to fetch customer analytics summary")}return e.data},async getStockMaintenanceReport(){let e=await r.uE.get("".concat(s,"/stock_maintenance_report/"));if(!e.success||!e.data){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Failed to fetch stock maintenance report")}return e.data},async getSystemHealth(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:24,t=await r.uE.get("".concat(s,"/system_health/?hours=").concat(e));if(!t.success||!t.data){var a;throw Error((null==(a=t.error)?void 0:a.message)||"Failed to fetch system health")}return t.data},async getContentAnalytics(e,t){let a=new URLSearchParams;e&&a.append("date_from",e),t&&a.append("date_to",t);let s=await r.uE.get("".concat("/api/content","/content_management/performance_summary/?").concat(a.toString()));if(!s.success||!s.data){var l;throw Error((null==(l=s.error)?void 0:l.message)||"Failed to fetch content analytics")}return s.data},async exportReport(e){let t=await r.uE.post("".concat(s,"/export_report/"),e);if(!t.success||!t.data){var a;throw Error((null==(a=t.error)?void 0:a.message)||"Failed to export report")}return t.data},async getExportHistory(){let e=await r.uE.get("".concat(s,"/export_history/"));if(!e.success||!e.data){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Failed to fetch export history")}return e.data},async downloadExport(e){let t=await r.uE.get("".concat(s,"/download_export/?export_id=").concat(e),{responseType:"blob"});if(!t.success||!t.data){var a;throw Error((null==(a=t.error)?void 0:a.message)||"Failed to download export")}let l=window.URL.createObjectURL(new Blob([t.data])),c=document.createElement("a");c.href=l,c.setAttribute("download","report_".concat(e,".csv")),document.body.appendChild(c),c.click(),c.remove(),window.URL.revokeObjectURL(l)},async generateDailyReports(e){let t=await r.uE.post("".concat(s,"/generate_daily_reports/"),e?{date:e}:{});if(!t.success||!t.data){var a;throw Error((null==(a=t.error)?void 0:a.message)||"Failed to generate daily reports")}return t.data}}},7108:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7580:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7809:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},9946:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(2115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:i="",children:d,iconNode:m,...u}=e;return(0,r.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:a,strokeWidth:o?24*Number(n)/Number(s):n,className:l("lucide",i),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let a=(0,r.forwardRef)((a,c)=>{let{className:o,...i}=a;return(0,r.createElement)(n,{ref:c,iconNode:t,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...i})});return a.displayName=s(e),a}}}]);