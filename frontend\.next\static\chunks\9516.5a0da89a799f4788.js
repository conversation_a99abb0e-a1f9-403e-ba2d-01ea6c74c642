"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9516],{4416:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9516:(e,t,a)=>{a.r(t),a.d(t,{AccessibilityMenu:()=>x});var s=a(5155),r=a(2115),l=a(9042);function i(e){return Array.from(e.querySelectorAll('a[href],button:not([disabled]),input:not([disabled]),select:not([disabled]),textarea:not([disabled]),[tabindex]:not([tabindex="-1"]),[contenteditable]'))}var n=a(9946);let c=(0,n.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var d=a(4416);let o=(0,n.A)("contrast",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 18a6 6 0 0 0 0-12v12z",key:"j4l70d"}]]),u=(0,n.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),h=(0,n.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),m=(0,n.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]),b=(0,n.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),y=(0,n.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function x(){let[e,t]=(0,r.useState)(!1),{highContrast:a,toggleHighContrast:n,increaseFontSize:x,decreaseFontSize:g,resetFontSize:f,reduceMotion:p,toggleReduceMotion:k,announce:v}=(0,l.W)(),j=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1?arguments[1]:void 0,a=(0,r.useRef)(null);return(0,r.useEffect)(()=>{if(!e)return;let s=a.current;if(!s)return;let r=document.activeElement;if(null==t?void 0:t.current)t.current.focus();else{let e=i(s);e.length>0?e[0].focus():s.focus()}let l=e=>{if("Tab"!==e.key)return;let t=i(s);if(0===t.length)return;let a=t[0],r=t[t.length-1];e.shiftKey&&document.activeElement===a?(e.preventDefault(),r.focus()):e.shiftKey||document.activeElement!==r||(e.preventDefault(),a.focus())};return s.addEventListener("keydown",l),()=>{s.removeEventListener("keydown",l),r&&"focus"in r&&r.focus()}},[e,t]),a}(e),w=()=>{let a=!e;t(a),a?v("Accessibility menu opened","polite"):v("Accessibility menu closed","polite")};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{className:"fixed bottom-4 right-4 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:w,"aria-label":"Accessibility options","aria-expanded":e,"aria-controls":"accessibility-menu",children:(0,s.jsx)(c,{className:"h-6 w-6"})}),e&&(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center",role:"dialog","aria-modal":"true","aria-labelledby":"accessibility-title",children:(0,s.jsxs)("div",{ref:j,id:"accessibility-menu",className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{id:"accessibility-title",className:"text-xl font-bold",children:"Accessibility Options"}),(0,s.jsx)("button",{onClick:w,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200","aria-label":"Close accessibility menu",children:(0,s.jsx)(d.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Display"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("button",{onClick:()=>{n(),v("High contrast mode ".concat(a?"disabled":"enabled"),"polite")},className:"flex items-center justify-between w-full px-4 py-2 rounded-md ".concat(a?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),"aria-pressed":a,children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(o,{className:"mr-2 h-5 w-5"}),"High Contrast"]}),(0,s.jsx)("span",{className:"text-sm",children:a?"On":"Off"})]}),(0,s.jsxs)("button",{onClick:()=>{k(),v("Reduced motion ".concat(p?"disabled":"enabled"),"polite")},className:"flex items-center justify-between w-full px-4 py-2 rounded-md ".concat(p?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),"aria-pressed":p,children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(u,{className:"mr-2 h-5 w-5"}),"Reduce Motion"]}),(0,s.jsx)("span",{className:"text-sm",children:p?"On":"Off"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Text Size"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{g(),v("Font size decreased","polite")},className:"p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600","aria-label":"Decrease font size",children:(0,s.jsx)(h,{className:"h-5 w-5"})}),(0,s.jsxs)("button",{onClick:()=>{f(),v("Font size reset to default","polite")},className:"flex-1 p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center","aria-label":"Reset font size",children:[(0,s.jsx)(m,{className:"mr-2 h-5 w-5"}),(0,s.jsx)("span",{children:"Reset"}),(0,s.jsx)(b,{className:"ml-2 h-4 w-4"})]}),(0,s.jsx)("button",{onClick:()=>{x(),v("Font size increased","polite")},className:"p-2 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600","aria-label":"Increase font size",children:(0,s.jsx)(y,{className:"h-5 w-5"})})]})]})]})]})})]})}},9946:(e,t,a)=>{a.d(t,{A:()=>c});var s=a(2115);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:d="",children:o,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:t,...i,width:r,height:r,stroke:a,strokeWidth:c?24*Number(n)/Number(r):n,className:l("lucide",d),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(o)?o:[o]])}),c=(e,t)=>{let a=(0,s.forwardRef)((a,i)=>{let{className:c,...d}=a;return(0,s.createElement)(n,{ref:i,iconNode:t,className:l("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...d})});return a.displayName=r(e),a}}}]);