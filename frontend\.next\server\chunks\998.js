exports.id=998,exports.ids=[998],exports.modules={5202:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},6377:(a,b,c)=>{Promise.resolve().then(c.bind(c,25857))},7791:(a,b,c)=>{"use strict";c.d(b,{Cy:()=>h,Hp:()=>d.Hp,JR:()=>e,Sn:()=>f,bw:()=>j,d5:()=>g,oO:()=>k,w8:()=>i});var d=c(24125);let e="http://localhost:8000/api/v1",f={AUTH:{LOGIN:"/auth/login/",REGISTER:"/auth/register/",LOGOUT:"/auth/logout/",REFRESH:"/auth/refresh/",PROFILE:"/auth/profile/",FORGOT_PASSWORD:"/auth/forgot-password/",RESET_PASSWORD:"/auth/reset-password/",VALIDATE_RESET_TOKEN:a=>`/auth/validate-reset-token/${a}/`},PRODUCTS:{LIST:"/products/",DETAIL:a=>`/products/${a}/`,CATEGORIES:"/products/categories/"},CART:{LIST:"/cart/",ADD:"/cart/add/",UPDATE:a=>`/cart/${a}/`,REMOVE:a=>`/cart/${a}/`},ORDERS:{LIST:"/orders/",DETAIL:a=>`/orders/${a}/`,CREATE:"/orders/",CANCEL:a=>`/orders/${a}/cancel/`,TIMELINE:a=>`/orders/${a}/timeline/`,INVOICE:a=>`/orders/${a}/invoice/`,DOWNLOAD_INVOICE:a=>`/orders/${a}/download_invoice/`},RETURNS:{LIST:"/return-requests/",DETAIL:a=>`/return-requests/${a}/`,CREATE:"/return-requests/"},REPLACEMENTS:{LIST:"/replacements/",DETAIL:a=>`/replacements/${a}/`,CREATE:"/replacements/",UPDATE_STATUS:a=>`/replacements/${a}/update_status/`},SEARCH:{PRODUCTS:"/search/products/",SUGGESTIONS:"/search/suggestions/",FILTERS:"/search/filters/",POPULAR:"/search/popular/",RELATED:"/search/related/"},CUSTOMER:{PROFILE:"/customer/profile/",ADDRESSES:"/customer/addresses/",ADDRESS_DETAIL:a=>`/customer/addresses/${a}/`,PREFERENCES:"/customer/preferences/"},WISHLIST:{LIST:"/wishlist/",ADD:"/wishlist/add/",REMOVE:a=>`/wishlist/${a}/`,CLEAR:"/wishlist/clear/"},PAYMENTS:{METHODS:"/payments/methods/",CURRENCIES:"/payments/currencies/",CREATE:"/payments/create/",VERIFY:"/payments/verify/",STATUS:a=>`/payments/${a}/status/`,WALLET:"/payments/wallet/",GIFT_CARD:{VALIDATE:"/payments/gift-card/validate/",BALANCE:a=>`/payments/gift-card/${a}/balance/`},CONVERT_CURRENCY:"/payments/convert-currency/"},ADMIN:{DASHBOARD:"/admin/dashboard/",ANALYTICS:"/admin/analytics/",PRODUCTS:"/admin/products/",ORDERS:"/admin/orders/",CUSTOMERS:"/admin/customers/",SETTINGS:"/admin/settings/"},SELLER:{DASHBOARD:"/seller/dashboard/",PRODUCTS:"/seller/products/",ORDERS:"/seller/orders/",PROFILE:"/seller/profile/",KYC:"/seller/kyc/",PAYOUTS:"/seller/payouts/"}},g={ACCESS_TOKEN:"access_token",REFRESH_TOKEN:"refresh_token",USER:"user",CART:"cart"},h={CUSTOMER:"customer",SELLER:"seller",ADMIN:"admin"},i={PENDING:"PENDING",CONFIRMED:"CONFIRMED",PROCESSING:"PROCESSING",SHIPPED:"SHIPPED",DELIVERED:"DELIVERED",CANCELLED:"CANCELLED",RETURNED:"RETURNED"},j={...d.l$,...d.Ei,PROFILE:d.Hp.DASHBOARD,ORDERS:d.Hp.ORDERS,PROFILE_ADDRESSES:d.Hp.ADDRESSES,PROFILE_WISHLIST:d.Hp.WISHLIST,PROFILE_PREFERENCES:d.Hp.SETTINGS,ADMIN:d.F$.DASHBOARD,SELLER:d.B0.DASHBOARD},k={PASSWORD_MIN_LENGTH:8,USERNAME_MIN_LENGTH:3,PHONE_REGEX:/^[\+]?[1-9][\d]{0,15}$/,EMAIL_REGEX:/^[^\s@]+@[^\s@]+\.[^\s@]+$/}},11985:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>p,Hc:()=>k,YF:()=>l,_t:()=>m,hw:()=>j,ii:()=>g,j$:()=>h,wc:()=>i});var d=c(9317),e=c(17327),f=c(7791);let g=(0,d.zD)("customer/fetchProfile",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.CUSTOMER.PROFILE);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch profile")}catch(a){return b(a.message||"Failed to fetch profile")}}),h=(0,d.zD)("customer/updateProfile",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.put(f.Sn.CUSTOMER.PROFILE,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to update profile")}catch(a){return b(a.message||"Failed to update profile")}}),i=(0,d.zD)("customer/updatePreferences",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.put(f.Sn.CUSTOMER.PREFERENCES,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to update preferences")}catch(a){return b(a.message||"Failed to update preferences")}}),j=(0,d.zD)("customer/fetchAddresses",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.CUSTOMER.ADDRESSES);if(a.success&&a.data)return a.data.results||a.data;return b(a.error?.message||"Failed to fetch addresses")}catch(a){return b(a.message||"Failed to fetch addresses")}}),k=(0,d.zD)("customer/createAddress",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.CUSTOMER.ADDRESSES,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to create address")}catch(a){return b(a.message||"Failed to create address")}}),l=(0,d.zD)("customer/updateAddress",async({id:a,addressData:b},{rejectWithValue:c})=>{try{let d=await e.uE.put(f.Sn.CUSTOMER.ADDRESS_DETAIL(a),b);if(d.success&&d.data)return d.data;return c(d.error?.message||"Failed to update address")}catch(a){return c(a.message||"Failed to update address")}}),m=(0,d.zD)("customer/deleteAddress",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.delete(f.Sn.CUSTOMER.ADDRESS_DETAIL(a));if(c.success)return a;return b(c.error?.message||"Failed to delete address")}catch(a){return b(a.message||"Failed to delete address")}}),n=(0,d.Z0)({name:"customer",initialState:{profile:null,addresses:[],loading:!1,error:null},reducers:{clearError:a=>{a.error=null}},extraReducers:a=>{a.addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.profile=b.payload,a.error=null}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,a.profile=b.payload,a.error=null}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1,a.profile&&(a.profile.preferences=b.payload),a.error=null}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,(a,b)=>{a.loading=!1,a.addresses=b.payload,a.error=null}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.addresses.push(b.payload),a.error=null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0,a.error=null}).addCase(l.fulfilled,(a,b)=>{a.loading=!1;let c=a.addresses.findIndex(a=>a.id===b.payload.id);-1!==c&&(a.addresses[c]=b.payload),a.error=null}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.pending,a=>{a.loading=!0,a.error=null}).addCase(m.fulfilled,(a,b)=>{a.loading=!1,a.addresses=a.addresses.filter(a=>a.id!==b.payload),a.error=null}).addCase(m.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:o}=n.actions,p=n.reducer},14028:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>l});var d=c(60687),e=c(54864),f=c(37590),g=c(68991),h=c(43210);function i({children:a}){return(0,g.jL)(),(0,d.jsx)(d.Fragment,{children:a})}c(56423);var j=c(31464);let k=(0,h.lazy)(()=>c.e(711).then(c.bind(c,32711)).then(a=>({default:a.AccessibilityMenu})));function l({children:a}){return(0,d.jsx)(e.Kq,{store:g.M_,children:(0,d.jsx)(i,{children:(0,d.jsxs)(j.Q,{children:[a,(0,d.jsx)(f.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}),(0,d.jsx)(h.Suspense,{fallback:null,children:(0,d.jsx)(k,{})})]})})})}},17327:(a,b,c)=>{"use strict";c.d(b,{uE:()=>h});var d=c(51060),e=c(7791),f=c(47718);class g{constructor(){this.client=d.A.create({baseURL:e.JR,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(a=>{let b=(0,f.Uk)();return b?.access&&a.headers&&(a.headers.Authorization=`Bearer ${b.access}`),a},a=>Promise.reject(a)),this.client.interceptors.response.use(a=>a,async a=>{if(a&&"object"==typeof a&&"config"in a&&"response"in a){let b=a.config;if(a.response?.status===401&&!b._retry){b._retry=!0;try{let a=(0,f.Uk)();if(a?.refresh){let c=(await this.refreshToken(a.refresh)).data;return(0,f.yH)(c),b.headers&&(b.headers.Authorization=`Bearer ${c.access}`),this.client(b)}}catch(a){(0,f.Y9)()}}}return Promise.reject(a)})}async refreshToken(a){return this.client.post("/auth/refresh/",{refresh:a})}async get(a,b){try{let c=await this.client.get(a,b);return{success:!0,data:c.data}}catch(a){return this.handleError(a)}}async post(a,b,c){try{let d=await this.client.post(a,b,c);return{success:!0,data:d.data}}catch(a){return this.handleError(a)}}async put(a,b,c){try{let d=await this.client.put(a,b,c);return{success:!0,data:d.data}}catch(a){return this.handleError(a)}}async patch(a,b,c){try{let d=await this.client.patch(a,b,c);return{success:!0,data:d.data}}catch(a){return this.handleError(a)}}async delete(a,b){try{let c=await this.client.delete(a,b);return{success:!0,data:c.data}}catch(a){return this.handleError(a)}}handleError(a){if(a&&"object"==typeof a&&"response"in a){if(a.response)return{success:!1,error:{message:a.response.data?.error?.message||a.response.data?.message||"An error occurred",code:a.response.data?.error?.code||"api_error",status_code:a.response.status,details:a.response.data?.error?.details||a.response.data}};if(a.request)return{success:!1,error:{message:"Network error. Please check your connection.",code:"network_error",status_code:0}}}return{success:!1,error:{message:a instanceof Error?a.message:"An unexpected error occurred",code:"unknown_error",status_code:0}}}}let h=new g,{get:i,post:j,put:k,patch:l,delete:m}=h},17558:(a,b,c)=>{"use strict";c.d(b,{kn:()=>j,i8:()=>h,ET:()=>n,Zb:()=>q,Ay:()=>u,Fn:()=>i,VZ:()=>l,MI:()=>s,it:()=>o,e6:()=>p,Ox:()=>k});var d=c(9317),e=c(17327);let f={getShippingPartners:()=>e.uE.get("/shipping/partners/"),checkServiceability:a=>e.uE.get(`/shipping/serviceable-areas/check_serviceability/?pin_code=${a}`),getAvailableDeliverySlots:a=>e.uE.post("/shipping/delivery-slots/available_slots/",a),calculateShippingRates:a=>e.uE.post("/shipping/shipping-rates/calculate/",a),getUserShipments:a=>{let b=new URLSearchParams;return a&&Object.entries(a).forEach(([a,c])=>{void 0!==c&&b.append(a,c.toString())}),e.uE.get(`/shipping/shipments/?${b.toString()}`)},trackShipment:a=>e.uE.get(`/shipping/shipments/${a}/track/`)},g=(0,d.zD)("shipping/fetchPartners",async(a,{rejectWithValue:b})=>{try{return(await f.getShippingPartners()).data}catch(a){return b(a.response?.data?.message||"Failed to fetch shipping partners")}}),h=(0,d.zD)("shipping/checkServiceability",async(a,{rejectWithValue:b})=>{try{return(await f.checkServiceability(a.pin_code)).data}catch(a){return b(a.response?.data?.message||"Failed to check serviceability")}}),i=(0,d.zD)("shipping/fetchAvailableSlots",async(a,{rejectWithValue:b})=>{try{return(await f.getAvailableDeliverySlots(a)).data}catch(a){return b(a.response?.data?.message||"Failed to fetch delivery slots")}}),j=(0,d.zD)("shipping/calculateRates",async(a,{rejectWithValue:b})=>{try{return(await f.calculateShippingRates(a)).data}catch(a){return b(a.response?.data?.message||"Failed to calculate shipping rates")}}),k=(0,d.zD)("shipping/trackShipment",async(a,{rejectWithValue:b})=>{try{return(await f.trackShipment(a)).data}catch(a){return b(a.response?.data?.message||"Failed to track shipment")}}),l=(0,d.zD)("shipping/fetchUserShipments",async(a,{rejectWithValue:b})=>{try{return(await f.getUserShipments()).data}catch(a){return b(a.response?.data?.message||"Failed to fetch shipments")}}),m=(0,d.Z0)({name:"shipping",initialState:{partners:[],serviceableAreas:[],deliverySlots:[],shipments:[],currentShipment:null,shippingRates:[],selectedDeliverySlot:null,selectedShippingAddress:null,loading:!1,error:null},reducers:{clearError:a=>{a.error=null},setSelectedDeliverySlot:(a,b)=>{a.selectedDeliverySlot=b.payload},setSelectedShippingAddress:(a,b)=>{a.selectedShippingAddress=b.payload},clearShippingRates:a=>{a.shippingRates=[]},clearDeliverySlots:a=>{a.deliverySlots=[]},setCurrentShipment:(a,b)=>{a.currentShipment=b.payload},resetShippingState:a=>{a.selectedDeliverySlot=null,a.selectedShippingAddress=null,a.shippingRates=[],a.deliverySlots=[],a.error=null}},extraReducers:a=>{a.addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.partners=b.payload||[]}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,b.payload&&b.payload.serviceable&&b.payload.areas&&(a.serviceableAreas=b.payload.areas)}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.serviceableAreas=[]}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1,a.deliverySlots=b.payload||[]}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.deliverySlots=[]}).addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,(a,b)=>{a.loading=!1,b.payload?a.shippingRates=Array.isArray(b.payload)?b.payload:[b.payload]:a.shippingRates=[]}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.shippingRates=[]}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.currentShipment=b.payload?.shipment||null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.currentShipment=null}).addCase(l.pending,a=>{a.loading=!0,a.error=null}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,b.payload?Array.isArray(b.payload)?a.shipments=b.payload:b.payload.results?a.shipments=b.payload.results:a.shipments=[]:a.shipments=[]}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:n,setSelectedDeliverySlot:o,setSelectedShippingAddress:p,clearShippingRates:q,clearDeliverySlots:r,setCurrentShipment:s,resetShippingState:t}=m.actions,u=m.reducer},24125:(a,b,c)=>{"use strict";c.d(b,{B0:()=>h,Ei:()=>e,F$:()=>g,Hp:()=>f,Ss:()=>i,l$:()=>d});let d={HOME:"/",PRODUCTS:"/products",PRODUCT_DETAIL:a=>`/products/${a}`,CART:"/cart",CHECKOUT:"/checkout",SEARCH:"/search",ABOUT:"/about",CONTACT:"/contact",TERMS:"/terms",PRIVACY:"/privacy",FAQ:"/faq"},e={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email"},f={DASHBOARD:"/profile",ORDERS:"/profile/orders",ORDER_DETAIL:a=>`/profile/orders/${a}`,ADDRESSES:"/profile/addresses",WISHLIST:"/profile/wishlist",SETTINGS:"/profile/settings",NOTIFICATIONS:"/profile/notifications"},g={DASHBOARD:"/admin",ANALYTICS:"/admin/analytics",ORDERS:"/admin/orders",ORDER_DETAIL:a=>`/admin/orders/${a}`,PRODUCTS:"/admin/products",PRODUCT_EDIT:a=>`/admin/products/${a}/edit`,PRODUCT_CREATE:"/admin/products/create",CUSTOMERS:"/admin/customers",CUSTOMER_DETAIL:a=>`/admin/customers/${a}`,CONTENT:"/admin/content",REPORTS:"/admin/reports",SYSTEM:"/admin/system",NOTIFICATIONS:"/admin/notifications",SETTINGS:"/admin/settings"},h={DASHBOARD:"/seller/dashboard",PRODUCTS:"/seller/products",PRODUCT_EDIT:a=>`/seller/products/${a}/edit`,PRODUCT_CREATE:"/seller/products/create",ORDERS:"/seller/orders",ORDER_DETAIL:a=>`/seller/orders/${a}`,PROFILE:"/seller/profile",KYC:"/seller/kyc",BANK_ACCOUNTS:"/seller/bank-accounts",PAYOUTS:"/seller/payouts",ANALYTICS:"/seller/analytics",SETTINGS:"/seller/settings"},i={"/":"Home","/products":"Products","/cart":"Shopping Cart","/checkout":"Checkout","/search":"Search","/about":"About Us","/contact":"Contact Us","/terms":"Terms of Service","/privacy":"Privacy Policy","/faq":"FAQ","/auth/login":"Login","/auth/register":"Register","/auth/forgot-password":"Forgot Password","/auth/reset-password":"Reset Password","/auth/verify-email":"Verify Email","/profile":"My Account","/profile/orders":"My Orders","/profile/addresses":"My Addresses","/profile/wishlist":"My Wishlist","/profile/settings":"Account Settings","/profile/notifications":"Notifications","/admin":"Admin Dashboard","/admin/analytics":"Analytics","/admin/orders":"Orders Management","/admin/products":"Products Management","/admin/products/create":"Create Product","/admin/customers":"Customers Management","/admin/content":"Content Management","/admin/reports":"Reports","/admin/system":"System Health","/admin/notifications":"Notifications","/admin/settings":"Admin Settings","/seller/dashboard":"Seller Dashboard","/seller/products":"My Products","/seller/products/create":"Add New Product","/seller/orders":"My Orders","/seller/profile":"Seller Profile","/seller/kyc":"KYC Verification","/seller/bank-accounts":"Bank Accounts","/seller/payouts":"Payouts","/seller/analytics":"Sales Analytics","/seller/settings":"Seller Settings"}},25857:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\components\\providers\\Providers.tsx","Providers")},31464:(a,b,c)=>{"use strict";c.d(b,{Q:()=>h,W:()=>i});var d=c(60687),e=c(43210);function f(a,b="polite"){}let g=(0,e.createContext)(void 0);function h({children:a}){let[b,c]=(0,e.useState)(!1),[h,i]=(0,e.useState)(16),[j,k]=(0,e.useState)(!1);return(0,d.jsx)(g.Provider,{value:{highContrast:b,toggleHighContrast:()=>{c(a=>!a)},fontSize:h,increaseFontSize:()=>{i(a=>Math.min(a+1,24))},decreaseFontSize:()=>{i(a=>Math.max(a-1,12))},resetFontSize:()=>{i(16)},reduceMotion:j,toggleReduceMotion:()=>{k(a=>!a)},announce:f},children:a})}function i(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useAccessibility must be used within an AccessibilityProvider");return a}},35880:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>r,CO:()=>j,Dz:()=>k,IZ:()=>o,Ty:()=>n,Y0:()=>i,a4:()=>h,b7:()=>f,gZ:()=>m,rf:()=>l,xF:()=>g});var d=c(9317),e=c(51060);let f=(0,d.zD)("seller/fetchProfile",async(a,{rejectWithValue:b})=>{try{return(await e.A.get("/api/v1/seller/profile/")).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to fetch seller profile")}}),g=(0,d.zD)("seller/register",async(a,{rejectWithValue:b})=>{try{let b=new FormData;return Object.entries(a).forEach(([a,c])=>{c instanceof File?b.append(a,c):null!=c&&b.append(a,c.toString())}),(await e.A.post("/api/v1/seller/register/",b,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to register as seller")}}),h=(0,d.zD)("seller/updateProfile",async(a,{rejectWithValue:b})=>{try{let b=new FormData;return Object.entries(a).forEach(([a,c])=>{c instanceof File?b.append(a,c):null!=c&&b.append(a,String(c))}),(await e.A.put("/api/v1/seller/profile/",b,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to update seller profile")}}),i=(0,d.zD)("seller/fetchKYCDocuments",async(a,{rejectWithValue:b})=>{try{return(await e.A.get("/api/v1/seller/kyc/")).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to fetch KYC documents")}}),j=(0,d.zD)("seller/uploadKYCDocument",async(a,{rejectWithValue:b})=>{try{let b=new FormData;return Object.entries(a).forEach(([a,c])=>{c instanceof File?b.append(a,c):null!=c&&b.append(a,c.toString())}),(await e.A.post("/api/v1/seller/kyc/",b,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to upload KYC document")}}),k=(0,d.zD)("seller/fetchBankAccounts",async(a,{rejectWithValue:b})=>{try{return(await e.A.get("/api/v1/seller/bank-accounts/")).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to fetch bank accounts")}}),l=(0,d.zD)("seller/addBankAccount",async(a,{rejectWithValue:b})=>{try{let b=new FormData;return Object.entries(a).forEach(([a,c])=>{c instanceof File?b.append(a,c):null!=c&&b.append(a,c.toString())}),(await e.A.post("/api/v1/seller/bank-accounts/",b,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to add bank account")}}),m=(0,d.zD)("seller/setPrimaryBankAccount",async(a,{rejectWithValue:b})=>{try{return(await e.A.post(`/api/v1/seller/bank-accounts/${a}/set_primary/`)).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to set primary bank account")}}),n=(0,d.zD)("seller/fetchPayoutHistory",async(a,{rejectWithValue:b})=>{try{return(await e.A.get("/api/v1/seller/payouts/")).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to fetch payout history")}}),o=(0,d.zD)("seller/fetchAnalytics",async(a,{rejectWithValue:b})=>{try{return(await e.A.get("/api/v1/seller/analytics/")).data.data}catch(a){return b(a.response?.data?.error?.message||"Failed to fetch seller analytics")}}),p=(0,d.Z0)({name:"seller",initialState:{profile:null,kycDocuments:[],bankAccounts:[],payouts:[],analytics:null,loading:!1,error:null},reducers:{clearSellerError:a=>{a.error=null}},extraReducers:a=>{a.addCase(f.pending,a=>{a.loading=!0,a.error=null}).addCase(f.fulfilled,(a,b)=>{a.loading=!1,a.profile=b.payload}).addCase(f.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.profile=b.payload}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,a.profile=b.payload}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1,a.kycDocuments=b.payload}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,(a,b)=>{a.loading=!1,a.kycDocuments=[...a.kycDocuments,b.payload]}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.bankAccounts=b.payload}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0,a.error=null}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,a.bankAccounts=[...a.bankAccounts,b.payload]}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.pending,a=>{a.loading=!0,a.error=null}).addCase(m.fulfilled,(a,b)=>{a.loading=!1,a.bankAccounts=a.bankAccounts.map(a=>({...a,is_primary:a.id===b.payload.id}))}).addCase(m.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(n.pending,a=>{a.loading=!0,a.error=null}).addCase(n.fulfilled,(a,b)=>{a.loading=!1,a.payouts=b.payload}).addCase(n.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(o.pending,a=>{a.loading=!0,a.error=null}).addCase(o.fulfilled,(a,b)=>{a.loading=!1,a.analytics=b.payload}).addCase(o.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearSellerError:q}=p.actions,r=p.reducer},47718:(a,b,c)=>{"use strict";c.d(b,{LP:()=>i,Ti:()=>g,To:()=>h,Uk:()=>d,Y9:()=>f,yH:()=>e}),c(7791);let d=()=>null,e=a=>{},f=()=>{},g=()=>null,h=a=>{},i=()=>{}},52058:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},53593:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>n,fS:()=>m});var d=c(9317),e=c(7791);let f=(0,d.Z0)({name:"orders",initialState:{orders:[],currentOrder:null,loading:!1,error:null,pagination:{count:0,next:null,previous:null,page_size:10,total_pages:0,current_page:1},returnRequestLoading:!1,returnRequestError:null},reducers:{setOrders:(a,b)=>{a.orders=b.payload,a.loading=!1,a.error=null},setCurrentOrder:(a,b)=>{a.currentOrder=b.payload,a.loading=!1,a.error=null},setLoading:(a,b)=>{a.loading=b.payload},setError:(a,b)=>{a.error=b.payload,a.loading=!1},updateOrderStatus:(a,b)=>{let{orderId:c,status:d,message:e,trackingData:f,timestamp:g}=b.payload,h=a.orders.findIndex(a=>a.id===c);if(-1!==h){a.orders[h].status=d;let b={id:Date.now().toString(),status:d,description:e,location:f.location,created_at:f.timestamp||g};a.orders[h].timeline||(a.orders[h].timeline=[]),a.orders[h].timeline.unshift(b)}if(a.currentOrder&&a.currentOrder.id===c){a.currentOrder.status=d;let b={id:Date.now().toString(),status:d,description:e,location:f.location,created_at:f.timestamp||g};a.currentOrder.timeline||(a.currentOrder.timeline=[]),a.currentOrder.timeline.unshift(b)}},clearOrders:a=>{a.orders=[],a.currentOrder=null,a.loading=!1,a.error=null}}}),{setOrders:g,setCurrentOrder:h,setLoading:i,setError:j,updateOrderStatus:k,clearOrders:l}=f.actions;(0,d.zD)("orders/fetchOrders",async(a,{dispatch:b})=>{try{b(i(!0));let a=[];return b(g(a)),a}catch(a){throw b(j(a instanceof Error?a.message:"Failed to fetch orders")),a}}),(0,d.zD)("orders/fetchOrderById",async(a,{dispatch:b})=>{try{b(i(!0));let a={};return b(h(a)),a}catch(a){throw b(j(a instanceof Error?a.message:"Failed to fetch order")),a}}),(0,d.zD)("orders/cancelOrder",async(a,{dispatch:b})=>{try{return b(i(!0)),{success:!0}}catch(a){throw b(j(a instanceof Error?a.message:"Failed to cancel order")),a}});let m=(0,d.zD)("orders/createOrder",async(a,{dispatch:b})=>{try{b(i(!0));let c={id:"new-order-id",order_number:`ORD-${Date.now()}`,status:e.w8.PENDING,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),items:a.items.map(a=>({id:`item-${Date.now()}-${Math.random().toString(36).substring(2,9)}`,product:{id:a.product_id,name:"Product Name",slug:"product-name",description:"Product description",short_description:"Short description",category:{id:"cat-1",name:"Category",slug:"category",is_active:!0,created_at:new Date().toISOString()},brand:"Brand",sku:"SKU001",price:100,is_active:!0,is_featured:!1,dimensions:{},images:[],created_at:new Date().toISOString(),updated_at:new Date().toISOString()},quantity:a.quantity,unit_price:100,total_price:100*a.quantity,status:e.w8.PENDING,can_return:!1})),shipping_address:a.shipping_address,billing_address:a.billing_address,payment_method:"credit_card",payment_status:"pending",shipping_amount:10,tax_amount:20,discount_amount:0,total_amount:130,timeline:[]};return b(h(c)),c}catch(a){throw b(j(a instanceof Error?a.message:"Failed to create order")),a}});(0,d.zD)("orders/createReturnRequest",async(a,{dispatch:b})=>{try{return b(i(!0)),{success:!0}}catch(a){throw b(j(a instanceof Error?a.message:"Failed to create return request")),a}});let n=f.reducer},53808:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>m,Ej:()=>g,Jw:()=>j,Qg:()=>i});var d=c(9317),e=c(17327),f=c(7791);let g=(0,d.zD)("wishlist/fetch",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.WISHLIST.LIST);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch wishlist")}catch(a){return b(a.message||"Failed to fetch wishlist")}}),h=(0,d.zD)("wishlist/add",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.WISHLIST.ADD,{product_id:a});if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to add to wishlist")}catch(a){return b(a.message||"Failed to add to wishlist")}}),i=(0,d.zD)("wishlist/remove",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.delete(f.Sn.WISHLIST.REMOVE(a));if(c.success)return a;return b(c.error?.message||"Failed to remove from wishlist")}catch(a){return b(a.message||"Failed to remove from wishlist")}}),j=(0,d.zD)("wishlist/clear",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.delete(f.Sn.WISHLIST.CLEAR);if(a.success)return null;return b(a.error?.message||"Failed to clear wishlist")}catch(a){return b(a.message||"Failed to clear wishlist")}}),k=(0,d.Z0)({name:"wishlist",initialState:{wishlist:null,loading:!1,error:null},reducers:{clearError:a=>{a.error=null}},extraReducers:a=>{a.addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.wishlist=b.payload,a.error=null}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,a.wishlist?a.wishlist.items.push(b.payload):a.wishlist={id:"temp-id",items:[b.payload],created_at:new Date().toISOString()},a.error=null}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1,a.wishlist&&(a.wishlist.items=a.wishlist.items.filter(a=>a.id!==b.payload)),a.error=null}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,a=>{a.loading=!1,a.wishlist&&(a.wishlist.items=[]),a.error=null}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:l}=k.actions,m=k.reducer},56423:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>q,DY:()=>i,Lx:()=>h,Nu:()=>m,y4:()=>j});var d=c(9317),e=c(17327),f=c(7791),g=c(47718);let h=(0,d.zD)("auth/login",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.AUTH.LOGIN,a);if(!c.success||!c.data)return b(c.error?.message||"Login failed");{let{user:a,tokens:b}=c.data;return(0,g.yH)(b),(0,g.To)(a),{user:a,tokens:b}}}catch(a){return b(a.message||"Login failed")}}),i=(0,d.zD)("auth/register",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.AUTH.REGISTER,a);if(!c.success||!c.data)return b(c.error?.message||"Registration failed");{let{user:a,tokens:b}=c.data;return(0,g.yH)(b),(0,g.To)(a),{user:a,tokens:b}}}catch(a){return b(a.message||"Registration failed")}}),j=(0,d.zD)("auth/logout",async()=>{try{let a=(0,g.Uk)();return a?.refresh&&await e.uE.post(f.Sn.AUTH.LOGOUT,{refresh:a.refresh}),(0,g.Y9)(),(0,g.LP)(),null}catch(a){return(0,g.Y9)(),(0,g.LP)(),null}}),k=(0,d.zD)("auth/fetchProfile",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.AUTH.PROFILE);if(!a.success||!a.data)return b(a.error?.message||"Failed to fetch profile");{let b=a.data.user;return(0,g.To)(b),b}}catch(a){return b(a.message||"Failed to fetch profile")}}),l=(0,d.zD)("auth/updateProfile",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.put(f.Sn.AUTH.PROFILE,a);if(!c.success||!c.data)return b(c.error?.message||"Failed to update profile");{let a=c.data.user;return(0,g.To)(a),a}}catch(a){return b(a.message||"Failed to update profile")}}),m=(0,d.zD)("auth/initialize",async(a,{dispatch:b})=>{let c=(0,g.Uk)(),d=(0,g.Ti)();if(c&&d)try{return await b(k()).unwrap(),{user:d,tokens:c}}catch(a){(0,g.Y9)(),(0,g.LP)()}return null}),n=(0,d.Z0)({name:"auth",initialState:{user:null,tokens:null,isAuthenticated:!1,loading:!1,error:null},reducers:{clearError:a=>{a.error=null},setTokens:(a,b)=>{a.tokens=b.payload,(0,g.yH)(b.payload)}},extraReducers:a=>{a.addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,a.user=b.payload.user,a.tokens=b.payload.tokens,a.isAuthenticated=!0,a.error=null}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.isAuthenticated=!1}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1,a.user=b.payload.user,a.tokens=b.payload.tokens,a.isAuthenticated=!0,a.error=null}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.isAuthenticated=!1}).addCase(j.fulfilled,a=>{a.user=null,a.tokens=null,a.isAuthenticated=!1,a.loading=!1,a.error=null}).addCase(k.pending,a=>{a.loading=!0}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.user=b.payload,a.error=null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,a.user=b.payload,a.error=null}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.fulfilled,(a,b)=>{b.payload&&(a.user=b.payload.user,a.tokens=b.payload.tokens,a.isAuthenticated=!0),a.loading=!1})}}),{clearError:o,setTokens:p}=n.actions,q=n.reducer},61135:()=>{},68991:(a,b,c)=>{"use strict";c.d(b,{M_:()=>W,jL:()=>X,GV:()=>Y});var d=c(9317),e=c(54864),f=c(56423),g=c(71431),h=c(17327),i=c(7791);let j=(0,d.zD)("products/fetchProducts",async(a={},{rejectWithValue:b})=>{try{let c=new URLSearchParams;a.page&&c.append("page",a.page.toString()),a.filters&&Object.entries(a.filters).forEach(([a,b])=>{null!=b&&""!==b&&c.append(a,b.toString())});let d=`${i.Sn.PRODUCTS.LIST}?${c.toString()}`,e=await h.uE.get(d);if(e.success&&e.data)return e.data;return b(e.error?.message||"Failed to fetch products")}catch(a){return b(a.message||"Failed to fetch products")}}),k=(0,d.zD)("products/fetchProductById",async(a,{rejectWithValue:b})=>{try{let c=await h.uE.get(i.Sn.PRODUCTS.DETAIL(a));if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to fetch product")}catch(a){return b(a.message||"Failed to fetch product")}}),l=(0,d.zD)("products/fetchCategories",async(a,{rejectWithValue:b})=>{try{let a=await h.uE.get(i.Sn.PRODUCTS.CATEGORIES);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch categories")}catch(a){return b(a.message||"Failed to fetch categories")}}),m=(0,d.zD)("products/searchProducts",async(a,{rejectWithValue:b})=>{try{let c=await h.uE.get(`${i.Sn.PRODUCTS.LIST}?search=${encodeURIComponent(a)}`);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to search products")}catch(a){return b(a.message||"Failed to search products")}}),n=(0,d.Z0)({name:"products",initialState:{products:[],categories:[],currentProduct:null,loading:!1,error:null,pagination:null,filters:{}},reducers:{clearError:a=>{a.error=null},setFilters:(a,b)=>{a.filters={...a.filters,...b.payload}},clearFilters:a=>{a.filters={}},setCurrentProduct:(a,b)=>{a.currentProduct=b.payload}},extraReducers:a=>{a.addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,(a,b)=>{a.loading=!1,a.products=b.payload.results,a.pagination=b.payload.pagination,a.error=null}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.currentProduct=b.payload,a.error=null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,a.categories=b.payload}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.pending,a=>{a.loading=!0,a.error=null}).addCase(m.fulfilled,(a,b)=>{a.loading=!1,a.products=b.payload.results,a.pagination=b.payload.pagination,a.error=null}).addCase(m.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:o,setFilters:p,clearFilters:q,setCurrentProduct:r}=n.actions,s=n.reducer;var t=c(53593);let u=(0,d.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,isNotificationCenterOpen:!1},reducers:{addNotification:(a,b)=>{!a.notifications.some(a=>a.id===b.payload.id)&&(a.notifications.unshift(b.payload),b.payload.isRead||(a.unreadCount+=1))},markNotificationRead:(a,b)=>{let c=a.notifications.find(a=>a.id===b.payload);c&&!c.isRead&&(c.isRead=!0,a.unreadCount=Math.max(0,a.unreadCount-1))},markAllNotificationsRead:a=>{a.notifications.forEach(a=>{a.isRead=!0}),a.unreadCount=0},clearNotifications:a=>{a.notifications=[],a.unreadCount=0},toggleNotificationCenter:a=>{a.isNotificationCenterOpen=!a.isNotificationCenterOpen}}}),{addNotification:v,markNotificationRead:w,markAllNotificationsRead:x,clearNotifications:y,toggleNotificationCenter:z}=u.actions,A=u.reducer,B=(0,d.Z0)({name:"inventory",initialState:{items:{},loading:!1,error:null},reducers:{setInventoryItems:(a,b)=>{a.items=b.payload,a.loading=!1,a.error=null},setLoading:(a,b)=>{a.loading=b.payload},setError:(a,b)=>{a.error=b.payload,a.loading=!1},updateInventoryLevel:(a,b)=>{let{productId:c,quantity:d,timestamp:e}=b.payload;a.items[c]?(a.items[c].quantity=d,a.items[c].lastUpdated=e):a.items[c]={productId:c,quantity:d,lastUpdated:e}},clearInventory:a=>{a.items={},a.loading=!1,a.error=null}}}),{setInventoryItems:C,setLoading:D,setError:E,updateInventoryLevel:F,clearInventory:G}=B.actions,H=B.reducer,I=(0,d.Z0)({name:"chat",initialState:{rooms:{},activeRoomId:null,loading:!1,error:null},reducers:{setChatRooms:(a,b)=>{let c={};b.payload.forEach(a=>{c[a.id]=a}),a.rooms=c,a.loading=!1,a.error=null},setActiveRoom:(a,b)=>{a.activeRoomId=b.payload,a.rooms[b.payload]&&(a.rooms[b.payload].messages.forEach(a=>{a.isRead=!0}),a.rooms[b.payload].unreadCount=0)},setLoading:(a,b)=>{a.loading=b.payload},setError:(a,b)=>{a.error=b.payload,a.loading=!1},updateChatMessages:(a,b)=>{let{roomId:c,message:d}=b.payload;a.rooms[c]||(a.rooms[c]={id:c,name:`Room ${c}`,roomType:"UNKNOWN",participants:[],messages:[],unreadCount:0,isActive:!1}),a.rooms[c].messages.push(d),a.activeRoomId===c||d.isRead||(a.rooms[c].unreadCount+=1)},markRoomMessagesRead:(a,b)=>{let c=b.payload;a.rooms[c]&&(a.rooms[c].messages.forEach(a=>{a.isRead=!0}),a.rooms[c].unreadCount=0)},clearChat:a=>{a.rooms={},a.activeRoomId=null,a.loading=!1,a.error=null}}}),{setChatRooms:J,setActiveRoom:K,setLoading:L,setError:M,updateChatMessages:N,markRoomMessagesRead:O,clearChat:P}=I.actions,Q=I.reducer;var R=c(69351),S=c(17558),T=c(35880),U=c(53808),V=c(11985);let W=(0,d.U1)({reducer:{auth:f.Ay,cart:g.Ay,products:s,orders:t.Ay,notifications:A,inventory:H,chat:Q,payments:R.Ay,shipping:S.Ay,seller:T.Ay,wishlist:U.Ay,customer:V.Ay},middleware:a=>a({serializableCheck:{ignoredActions:["auth/setUser","auth/setToken"],ignoredActionPaths:["payload.data"],ignoredPaths:["auth.user","auth.token"]}})}),X=()=>(0,e.wA)(),Y=e.d4},69351:(a,b,c)=>{"use strict";c.d(b,{$f:()=>r,Ay:()=>u,BG:()=>s,CW:()=>h,DB:()=>k,KF:()=>j,Lb:()=>i,ON:()=>t,OS:()=>l,Pw:()=>m,hZ:()=>n,v4:()=>g,vg:()=>q});var d=c(9317),e=c(17327),f=c(7791);let g=(0,d.zD)("payments/fetchPaymentMethods",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.PAYMENTS.METHODS);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch payment methods")}catch(a){return b(a.message||"Failed to fetch payment methods")}}),h=(0,d.zD)("payments/fetchCurrencies",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.PAYMENTS.CURRENCIES);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch currencies")}catch(a){return b(a.message||"Failed to fetch currencies")}}),i=(0,d.zD)("payments/createPayment",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.PAYMENTS.CREATE,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to create payment")}catch(a){return b(a.message||"Failed to create payment")}}),j=(0,d.zD)("payments/verifyPayment",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.PAYMENTS.VERIFY,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to verify payment")}catch(a){return b(a.message||"Failed to verify payment")}}),k=(0,d.zD)("payments/getPaymentStatus",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.get(f.Sn.PAYMENTS.STATUS(a));if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to get payment status")}catch(a){return b(a.message||"Failed to get payment status")}}),l=(0,d.zD)("payments/getWalletDetails",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.PAYMENTS.WALLET);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to get wallet details")}catch(a){return b(a.message||"Failed to get wallet details")}}),m=(0,d.zD)("payments/validateGiftCard",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.PAYMENTS.GIFT_CARD.VALIDATE,{code:a});if(c.success&&c.data)return c.data;return b(c.error?.message||"Invalid gift card")}catch(a){return b(a.message||"Failed to validate gift card")}}),n=(0,d.zD)("payments/convertCurrency",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(f.Sn.PAYMENTS.CONVERT_CURRENCY,a);if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to convert currency")}catch(a){return b(a.message||"Failed to convert currency")}}),o=(0,d.Z0)({name:"payments",initialState:{paymentMethods:[],currencies:[],selectedCurrency:"USD",selectedPaymentMethod:null,currentPayment:null,wallet:null,giftCard:null,loading:!1,error:null,paymentProcessing:!1,paymentSuccess:!1,paymentError:null,currencyConversion:null},reducers:{clearError:a=>{a.error=null,a.paymentError=null},resetPaymentState:a=>{a.paymentProcessing=!1,a.paymentSuccess=!1,a.paymentError=null,a.currentPayment=null},setSelectedCurrency:(a,b)=>{a.selectedCurrency=b.payload},setSelectedPaymentMethod:(a,b)=>{a.selectedPaymentMethod=b.payload},clearGiftCard:a=>{a.giftCard=null}},extraReducers:a=>{a.addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.paymentMethods=b.payload,!a.selectedPaymentMethod&&b.payload.length>0&&(a.selectedPaymentMethod=b.payload[0].id),a.error=null}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1,a.currencies=b.payload,a.error=null}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(i.pending,a=>{a.paymentProcessing=!0,a.paymentSuccess=!1,a.paymentError=null}).addCase(i.fulfilled,(a,b)=>{a.paymentProcessing=!1,a.paymentSuccess=!0,a.currentPayment={id:b.payload.payment_id,status:b.payload.status,amount:b.payload.amount,currency:b.payload.currency,payment_method:a.paymentMethods.find(a=>a.method_type===b.payload.payment_method)||{id:"",name:b.payload.payment_method,method_type:b.payload.payment_method,gateway:"INTERNAL",processing_fee_percentage:0,processing_fee_fixed:0,is_active:!0},order_id:"",processing_fee:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},a.paymentError=null}).addCase(i.rejected,(a,b)=>{a.paymentProcessing=!1,a.paymentSuccess=!1,a.paymentError=b.payload}).addCase(j.pending,a=>{a.paymentProcessing=!0,a.paymentError=null}).addCase(j.fulfilled,(a,b)=>{a.paymentProcessing=!1,a.paymentSuccess="COMPLETED"===b.payload.status,a.currentPayment&&(a.currentPayment.status=b.payload.status),a.paymentError=null}).addCase(j.rejected,(a,b)=>{a.paymentProcessing=!1,a.paymentSuccess=!1,a.paymentError=b.payload}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.currentPayment&&(a.currentPayment.status=b.payload.status),a.paymentSuccess="COMPLETED"===b.payload.status,a.error=null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0,a.error=null}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,a.wallet=b.payload,a.error=null}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.pending,a=>{a.loading=!0,a.error=null}).addCase(m.fulfilled,(a,b)=>{a.loading=!1,a.giftCard=b.payload,a.error=null}).addCase(m.rejected,(a,b)=>{a.loading=!1,a.error=b.payload,a.giftCard=null}).addCase(n.pending,a=>{a.loading=!0,a.error=null}).addCase(n.fulfilled,(a,b)=>{a.loading=!1,a.currencyConversion=b.payload,a.error=null}).addCase(n.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:p,resetPaymentState:q,setSelectedCurrency:r,setSelectedPaymentMethod:s,clearGiftCard:t}=o.actions,u=o.reducer},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},71431:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>t,bE:()=>h});var d=c(9317),e=c(17327),f=c(7791);let g=(0,d.zD)("cart/fetchCart",async(a,{rejectWithValue:b})=>{try{let a=await e.uE.get(f.Sn.CART.LIST);if(a.success&&a.data)return a.data;return b(a.error?.message||"Failed to fetch cart")}catch(a){return b(a.message||"Failed to fetch cart")}}),h=(0,d.zD)("cart/addToCart",async({productId:a,quantity:b},{rejectWithValue:c})=>{try{let d=await e.uE.post(f.Sn.CART.ADD,{product_id:a,quantity:b});if(d.success&&d.data)return d.data;return c(d.error?.message||"Failed to add item to cart")}catch(a){return c(a.message||"Failed to add item to cart")}}),i=(0,d.zD)("cart/updateCartItem",async({itemId:a,quantity:b},{rejectWithValue:c})=>{try{let d=await e.uE.patch(f.Sn.CART.UPDATE(a),{quantity:b});if(d.success&&d.data)return d.data;return c(d.error?.message||"Failed to update cart item")}catch(a){return c(a.message||"Failed to update cart item")}}),j=(0,d.zD)("cart/removeCartItem",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.delete(f.Sn.CART.REMOVE(a));if(c.success)return a;return b(c.error?.message||"Failed to remove item from cart")}catch(a){return b(a.message||"Failed to remove item from cart")}}),k=(0,d.zD)("cart/saveForLater",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(`/cart/save-for-later/${a}/`);if(c.success&&c.data)return{savedItem:c.data,itemId:a};return b(c.error?.message||"Failed to save item for later")}catch(a){return b(a.message||"Failed to save item for later")}}),l=(0,d.zD)("cart/moveToCart",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post(`/cart/move-to-cart/${a}/`);if(c.success&&c.data)return{cartItem:c.data,savedItemId:a};return b(c.error?.message||"Failed to move item to cart")}catch(a){return b(a.message||"Failed to move item to cart")}}),m=(0,d.zD)("cart/removeSavedItem",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.delete(`/cart/saved-items/${a}/`);if(c.success)return a;return b(c.error?.message||"Failed to remove saved item")}catch(a){return b(a.message||"Failed to remove saved item")}}),n=(0,d.zD)("cart/applyCoupon",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.post("/cart/apply-coupon/",{code:a});if(c.success&&c.data)return c.data;return b(c.error?.message||"Failed to apply coupon")}catch(a){return b(a.message||"Failed to apply coupon")}}),o=(0,d.zD)("cart/removeCoupon",async(a,{rejectWithValue:b})=>{try{let c=await e.uE.delete(`/cart/remove-coupon/${a}/`);if(c.success)return a;return b(c.error?.message||"Failed to remove coupon")}catch(a){return b(a.message||"Failed to remove coupon")}}),p=a=>({itemCount:a.reduce((a,b)=>a+b.quantity,0),totalAmount:a.reduce((a,b)=>a+(b.product.discount_price||b.product.price)*b.quantity,0)}),q=(0,d.Z0)({name:"cart",initialState:{items:[],savedItems:[],appliedCoupons:[],itemCount:0,subtotal:0,discountAmount:0,totalAmount:0,loading:!1,error:null},reducers:{clearError:a=>{a.error=null},clearCart:a=>{a.items=[],a.itemCount=0,a.totalAmount=0}},extraReducers:a=>{a.addCase(g.pending,a=>{a.loading=!0,a.error=null}).addCase(g.fulfilled,(a,b)=>{a.loading=!1,a.items=b.payload.items,a.savedItems=b.payload.saved_items||[],a.appliedCoupons=b.payload.applied_coupons||[],a.subtotal=b.payload.subtotal,a.discountAmount=b.payload.discount_amount,a.totalAmount=b.payload.total_amount;let{itemCount:c}=p(b.payload.items);a.itemCount=c,a.error=null}).addCase(g.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(h.pending,a=>{a.loading=!0,a.error=null}).addCase(h.fulfilled,(a,b)=>{a.loading=!1;let c=a.items.findIndex(a=>a.product.id===b.payload.product.id);c>=0?a.items[c].quantity+=b.payload.quantity:a.items.push(b.payload);let{itemCount:d,totalAmount:e}=p(a.items);a.itemCount=d,a.totalAmount=e,a.error=null}).addCase(h.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(i.pending,a=>{a.loading=!0,a.error=null}).addCase(i.fulfilled,(a,b)=>{a.loading=!1;let c=a.items.findIndex(a=>a.id===b.payload.id);c>=0&&(a.items[c].quantity=b.payload.quantity);let{itemCount:d,totalAmount:e}=p(a.items);a.itemCount=d,a.totalAmount=e,a.error=null}).addCase(i.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(j.pending,a=>{a.loading=!0,a.error=null}).addCase(j.fulfilled,(a,b)=>{a.loading=!1,a.items=a.items.filter(a=>a.id!==b.payload);let{itemCount:c,totalAmount:d}=p(a.items);a.itemCount=c,a.totalAmount=d,a.error=null}).addCase(j.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(k.pending,a=>{a.loading=!0,a.error=null}).addCase(k.fulfilled,(a,b)=>{a.loading=!1,a.items=a.items.filter(a=>a.id!==b.payload.itemId),a.savedItems.push(b.payload.savedItem);let{itemCount:c,totalAmount:d}=p(a.items);a.itemCount=c,a.totalAmount=d,a.error=null}).addCase(k.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(l.pending,a=>{a.loading=!0,a.error=null}).addCase(l.fulfilled,(a,b)=>{a.loading=!1,a.savedItems=a.savedItems.filter(a=>a.id!==b.payload.savedItemId),a.items.push(b.payload.cartItem);let{itemCount:c,totalAmount:d}=p(a.items);a.itemCount=c,a.totalAmount=d,a.error=null}).addCase(l.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(m.pending,a=>{a.loading=!0,a.error=null}).addCase(m.fulfilled,(a,b)=>{a.loading=!1,a.savedItems=a.savedItems.filter(a=>a.id!==b.payload),a.error=null}).addCase(m.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(n.pending,a=>{a.loading=!0,a.error=null}).addCase(n.fulfilled,(a,b)=>{a.loading=!1,a.appliedCoupons.push(b.payload),a.discountAmount+=b.payload.discount_amount,a.totalAmount=a.subtotal-a.discountAmount,a.error=null}).addCase(n.rejected,(a,b)=>{a.loading=!1,a.error=b.payload}).addCase(o.pending,a=>{a.loading=!0,a.error=null}).addCase(o.fulfilled,(a,b)=>{a.loading=!1;let c=a.appliedCoupons.find(a=>a.coupon.id===b.payload);c&&(a.discountAmount-=c.discount_amount,a.appliedCoupons=a.appliedCoupons.filter(a=>a.coupon.id!==b.payload),a.totalAmount=a.subtotal-a.discountAmount),a.error=null}).addCase(o.rejected,(a,b)=>{a.loading=!1,a.error=b.payload})}}),{clearError:r,clearCart:s}=q.actions,t=q.reducer},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(37413),e=c(85041),f=c.n(e);c(61135);var g=c(25857);let h={title:"Ecommerce Platform",description:"Modern ecommerce platform with authentication"};function i({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:f().className,children:(0,d.jsx)(g.Providers,{children:a})})})}},96649:(a,b,c)=>{Promise.resolve().then(c.bind(c,14028))}};