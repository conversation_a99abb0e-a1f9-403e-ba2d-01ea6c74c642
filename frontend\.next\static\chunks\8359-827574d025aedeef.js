"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8359],{8359:(e,s,o)=>{o.d(s,{F:()=>f});var r=o(5155),t=o(9137),a=o.n(t),n=o(2115),i=o(2177),d=o(3741),l=o(2476),c=o(9406),x=o(662),p=o(4444),h=o(1821);let f=e=>{let{token:s,onSuccess:o,onBackToLogin:t}=e,[f,m]=(0,n.useState)(!1),[b,w]=(0,n.useState)(!1),[y,j]=(0,n.useState)(null),[k,v]=(0,n.useState)(null),[S,N]=(0,n.useState)(!0),{register:C,handleSubmit:z,formState:{errors:R},watch:P,reset:B}=(0,i.mN)(),W=P("password"),{markTokenUsed:L,isTokenValid:T}=(0,p.xf)(),{onPasswordResetSuccess:E,onPasswordResetFailure:_}=(0,h.mO)();(0,n.useEffect)(()=>{let e=async()=>{try{var e,o,r;N(!0);let t=await l.Z.validateResetToken(s);t.success&&(null==(e=t.data)?void 0:e.valid)?v(!0):(v(!1),j({message:(null==(o=t.data)?void 0:o.expired)?"This password reset link has expired. Please request a new one.":"This password reset link is invalid. Please request a new one.",code:(null==(r=t.data)?void 0:r.expired)?"token_expired":"token_invalid",canRetry:!1}))}catch(o){let e=(0,c.extractErrorInfo)(o);(0,x.tb)(o,"validateToken",{token:s.substring(0,8)+"..."}),(0,x.n0)("validate",{token:s,success:!1,errorCode:e.code}),v(!1),j({message:(0,x.E1)(o),code:e.code,canRetry:(0,x.lX)(e.code)})}finally{N(!1)}};s?e():(v(!1),N(!1))},[s]);let A=async e=>{m(!0),j(null);try{let r=await l.Z.resetPassword(s,e.password);if(r.success)(0,x.n0)("reset",{token:s,success:!0}),L(s),E("",s),w(!0),null==o||o();else{let e=(0,c.extractErrorInfo)(r.error);(0,x.tb)(r.error,"onSubmit",{token:s.substring(0,8)+"..."}),(0,x.n0)("reset",{token:s,success:!1,errorCode:e.code}),_("",e.code,s),j({message:(0,x.E1)(r.error),code:e.code,canRetry:(0,x.lX)(e.code)})}}catch(o){let e=(0,c.extractErrorInfo)(o);(0,x.tb)(o,"onSubmit",{token:s.substring(0,8)+"..."}),(0,x.n0)("reset",{token:s,success:!1,errorCode:e.code}),j({message:(0,x.E1)(o),code:e.code,canRetry:(0,x.lX)(e.code)})}finally{m(!1)}},I=async()=>{if(!1===k){N(!0),j(null);try{var e,o,r;let t=await l.Z.validateResetToken(s);t.success&&(null==(e=t.data)?void 0:e.valid)?v(!0):(v(!1),j({message:(null==(o=t.data)?void 0:o.expired)?"This password reset link has expired. Please request a new one.":"This password reset link is invalid. Please request a new one.",code:(null==(r=t.data)?void 0:r.expired)?"token_expired":"token_invalid",canRetry:!1}))}catch(o){let e=(0,c.extractErrorInfo)(o);(0,x.tb)(o,"handleRetry",{token:s.substring(0,8)+"..."}),j({message:(0,x.E1)(o),code:e.code,canRetry:(0,x.lX)(e.code)})}finally{N(!1)}}};return S?(0,r.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",textAlign:"center"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #2196f3",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto 24px auto"},className:"jsx-7a90291f0de70b21"}),(0,r.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"Validating Reset Link"}),(0,r.jsx)("p",{style:{color:"#757575",fontSize:"14px"},className:"jsx-7a90291f0de70b21",children:"Please wait while we verify your password reset link..."}),(0,r.jsx)(a(),{id:"7a90291f0de70b21",children:"@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}"})]}):b?(0,r.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,r.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,r.jsx)("div",{style:{width:"80px",height:"80px",backgroundColor:"#4caf50",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px auto",boxShadow:"0 4px 16px rgba(76, 175, 80, 0.3)"},children:(0,r.jsx)("svg",{style:{width:"40px",height:"40px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"12px"},children:"Password Reset Successful!"}),(0,r.jsx)("p",{style:{color:"#757575",fontSize:"16px",lineHeight:"1.6",marginBottom:"24px"},children:"Your password has been successfully updated. You can now log in with your new password."})]}),(0,r.jsx)(d.$,{onClick:t,style:{width:"100%",backgroundColor:"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600"},children:"Continue to Login"})]}):!1===k?(0,r.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,r.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,r.jsx)("div",{style:{width:"80px",height:"80px",backgroundColor:"#f44336",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px auto",boxShadow:"0 4px 16px rgba(244, 67, 54, 0.3)"},children:(0,r.jsx)("svg",{style:{width:"40px",height:"40px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"12px"},children:"Invalid Reset Link"}),(0,r.jsx)("p",{style:{color:"#757575",fontSize:"16px",lineHeight:"1.6",marginBottom:"24px"},children:(null==y?void 0:y.message)||"This password reset link is invalid or has expired."})]}),(null==y?void 0:y.canRetry)&&(0,r.jsx)(d.$,{onClick:I,variant:"outline",style:{width:"100%",marginBottom:"16px"},children:"Try Again"}),(0,r.jsx)(d.$,{onClick:t,style:{width:"100%",backgroundColor:"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600"},children:"Back to Login"})]}):(0,r.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,r.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,r.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"8px"},children:"Reset Your Password"}),(0,r.jsx)("p",{style:{color:"#757575",fontSize:"14px"},children:"Enter your new password below. Make sure it's strong and secure."})]}),(0,r.jsxs)("form",{onSubmit:z(A),className:"jsx-7a90291f0de70b21",children:[(0,r.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"New Password"}),(0,r.jsx)("input",{type:"password",...C("password",{required:"Password is required",validate:e=>e.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])/.test(e)?/(?=.*[A-Z])/.test(e)?/(?=.*\d)/.test(e)?!!/(?=.*[@$!%*?&])/.test(e)||"Password must contain at least one special character (@$!%*?&)":"Password must contain at least one number":"Password must contain at least one uppercase letter":"Password must contain at least one lowercase letter"}),style:{width:"100%",padding:"14px 16px",border:"2px solid ".concat(R.password?"#f44336":"#e0e0e0"),borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s, box-shadow 0.2s",boxSizing:"border-box",backgroundColor:R.password?"#ffeaea":"white"},placeholder:"Enter your new password",disabled:f,onFocus:e=>{R.password||(e.target.style.borderColor="#2196f3",e.target.style.boxShadow="0 0 0 3px rgba(33, 150, 243, 0.1)")},onBlur:e=>{R.password||(e.target.style.borderColor="#e0e0e0",e.target.style.boxShadow="none")},className:"jsx-7a90291f0de70b21"}),R.password&&(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginTop:"8px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("svg",{style:{width:"16px",height:"16px",color:"#f44336",flexShrink:0},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})}),(0,r.jsx)("p",{style:{color:"#f44336",fontSize:"13px",margin:0,fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:R.password.message})]}),W&&(0,r.jsxs)("div",{style:{marginTop:"12px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("div",{style:{fontSize:"12px",fontWeight:"500",color:"#757575",marginBottom:"6px"},className:"jsx-7a90291f0de70b21",children:"Password Strength"}),(0,r.jsx)("div",{style:{display:"flex",gap:"4px",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:[1,2,3,4].map(e=>{let s=g(W);return(0,r.jsx)("div",{style:{flex:1,height:"4px",borderRadius:"2px",backgroundColor:s>=e?1===s?"#f44336":2===s?"#ff9800":3===s?"#2196f3":"#4caf50":"#e0e0e0"},className:"jsx-7a90291f0de70b21"},e)})}),(0,r.jsx)("div",{style:{fontSize:"11px",color:"#757575"},className:"jsx-7a90291f0de70b21",children:u(g(W))})]})]}),(0,r.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"Confirm New Password"}),(0,r.jsx)("input",{type:"password",...C("confirmPassword",{required:"Please confirm your password",validate:e=>e===W||"Passwords do not match"}),style:{width:"100%",padding:"14px 16px",border:"2px solid ".concat(R.confirmPassword?"#f44336":"#e0e0e0"),borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s, box-shadow 0.2s",boxSizing:"border-box",backgroundColor:R.confirmPassword?"#ffeaea":"white"},placeholder:"Confirm your new password",disabled:f,onFocus:e=>{R.confirmPassword||(e.target.style.borderColor="#2196f3",e.target.style.boxShadow="0 0 0 3px rgba(33, 150, 243, 0.1)")},onBlur:e=>{R.confirmPassword||(e.target.style.borderColor="#e0e0e0",e.target.style.boxShadow="none")},className:"jsx-7a90291f0de70b21"}),R.confirmPassword&&(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginTop:"8px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("svg",{style:{width:"16px",height:"16px",color:"#f44336",flexShrink:0},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})}),(0,r.jsx)("p",{style:{color:"#f44336",fontSize:"13px",margin:0,fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:R.confirmPassword.message})]})]}),y&&(0,r.jsx)("div",{style:{backgroundColor:"network_error"===y.code?"#fff3cd":"#ffebee",border:"1px solid ".concat("network_error"===y.code?"#ffeaa7":"#ffcdd2"),borderRadius:"8px",padding:"16px",marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("div",{style:{width:"20px",height:"20px",flexShrink:0,marginTop:"2px"},className:"jsx-7a90291f0de70b21",children:(0,r.jsx)("svg",{style:{width:"20px",height:"20px",color:"network_error"===y.code?"#856404":"#c62828"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})})}),(0,r.jsxs)("div",{style:{flex:1},className:"jsx-7a90291f0de70b21",children:[(0,r.jsx)("p",{style:{color:"network_error"===y.code?"#856404":"#c62828",fontSize:"14px",margin:"0 0 8px 0",fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:y.message}),y.canRetry&&(0,r.jsx)("button",{onClick:I,disabled:f,style:{background:"none",border:"none",color:"network_error"===y.code?"#856404":"#c62828",fontSize:"13px",cursor:f?"not-allowed":"pointer",textDecoration:"underline",padding:"0",fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:f?"Retrying...":"Try Again"})]})]})}),(0,r.jsxs)(d.$,{type:"submit",disabled:f,style:{width:"100%",marginBottom:"16px",backgroundColor:f?"#90caf9":"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",cursor:f?"not-allowed":"pointer",transition:"background-color 0.2s ease"},children:[f&&(0,r.jsx)("div",{style:{width:"16px",height:"16px",border:"2px solid transparent",borderTop:"2px solid white",borderRadius:"50%",animation:"spin 1s linear infinite"},className:"jsx-7a90291f0de70b21"}),f?"Resetting Password...":"Reset Password"]}),(0,r.jsx)(a(),{id:"7a90291f0de70b21",children:"@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}"}),(0,r.jsx)("div",{style:{textAlign:"center"},className:"jsx-7a90291f0de70b21",children:(0,r.jsx)("button",{type:"button",onClick:t,style:{background:"none",border:"none",color:"#2196f3",fontSize:"14px",cursor:"pointer",textDecoration:"underline"},className:"jsx-7a90291f0de70b21",children:"Back to Login"})})]})]})},g=e=>{let s=0;return e.length>=8&&s++,/(?=.*[a-z])/.test(e)&&s++,/(?=.*[A-Z])/.test(e)&&s++,/(?=.*\d)/.test(e)&&s++,/(?=.*[@$!%*?&])/.test(e)&&s++,Math.min(s,4)},u=e=>{switch(e){case 0:case 1:return"Weak - Add more characters and variety";case 2:return"Fair - Add uppercase, numbers, or symbols";case 3:return"Good - Consider adding more variety";case 4:return"Strong - Great password!";default:return""}}}}]);