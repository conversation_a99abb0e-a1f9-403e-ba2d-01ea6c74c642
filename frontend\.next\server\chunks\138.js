"use strict";exports.id=138,exports.ids=[138],exports.modules={25138:(a,b,c)=>{c.d(b,{a8:()=>n,Xh:()=>x,Qg:()=>o,c$:()=>u,ar:()=>B});var d=c(60687),e=c(43210),f=c(54864);let g=()=>(0,f.wA)(),h=f.d4;var i=c(11985),j=c(2643),k=c(51907),l=c(37590);let m={type:"HOME",first_name:"",last_name:"",company:"",address_line_1:"",address_line_2:"",city:"",state:"",postal_code:"",country:"US",phone:"",is_default:!1};function n(){let a=g(),{addresses:b,loading:c}=h(a=>a.customer),[f,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)(null),[q,r]=(0,e.useState)(m),[s,t]=(0,e.useState)({}),u=a=>{let{name:b,value:c,type:d}=a.target,e=a.target.checked;r(a=>({...a,[b]:"checkbox"===d?e:c})),s[b]&&t(a=>({...a,[b]:""}))},v=async b=>{if(b.preventDefault(),(()=>{let a={};return q.first_name.trim()||(a.first_name="First name is required"),q.last_name.trim()||(a.last_name="Last name is required"),q.address_line_1.trim()||(a.address_line_1="Address is required"),q.city.trim()||(a.city="City is required"),q.state.trim()||(a.state="State is required"),q.postal_code.trim()||(a.postal_code="Postal code is required"),q.country.trim()||(a.country="Country is required"),q.phone&&!/^[\+]?[1-9][\d]{0,15}$/.test(q.phone)&&(a.phone="Please enter a valid phone number"),t(a),0===Object.keys(a).length})())try{o?(await a((0,i.YF)({id:o.id,addressData:q})).unwrap(),l.Ay.success("Address updated successfully!")):(await a((0,i.Hc)(q)).unwrap(),l.Ay.success("Address added successfully!")),x()}catch(a){l.Ay.error(a||"Failed to save address")}},w=async b=>{if(window.confirm("Are you sure you want to delete this address?"))try{await a((0,i._t)(b.id)).unwrap(),l.Ay.success("Address deleted successfully!")}catch(a){l.Ay.error(a||"Failed to delete address")}},x=()=>{n(!1),p(null),r(m),t({})},y=async()=>{try{await a((0,i.hw)()).unwrap(),l.Ay.success("Addresses refreshed")}catch(a){l.Ay.error("Failed to refresh addresses")}};return(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["My Addresses",b.length>0&&(0,d.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",b.length," ",1===b.length?"address":"addresses",")"]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:y,loading:c,children:"Refresh"}),(0,d.jsx)(j.$,{variant:"primary",size:"sm",onClick:()=>n(!0),children:"Add Address"})]})]})}),(0,d.jsx)("div",{className:"px-6 py-4",children:0===b.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No addresses found"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Add your first address to get started with faster checkout."}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(j.$,{variant:"primary",onClick:()=>n(!0),children:"Add Address"})})]}):(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:b.map(a=>(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:[(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"HOME"===a.type?"bg-blue-100 text-blue-800":"WORK"===a.type?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:a.type}),a.is_default&&(0,d.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800",children:"Default"})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,d.jsxs)("p",{className:"font-medium",children:[a.first_name," ",a.last_name]}),a.company&&(0,d.jsx)("p",{className:"text-gray-600",children:a.company}),(0,d.jsx)("p",{children:a.address_line_1}),a.address_line_2&&(0,d.jsx)("p",{children:a.address_line_2}),(0,d.jsxs)("p",{children:[a.city,", ",a.state," ",a.postal_code]}),(0,d.jsx)("p",{children:a.country}),a.phone&&(0,d.jsxs)("p",{className:"text-gray-600",children:["Phone: ",a.phone]})]})]})}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:()=>{p(a),n(!0)},children:"Edit"}),(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:()=>w(a),className:"text-red-600 hover:text-red-700",children:"Delete"})]})]},a.id))})}),f&&(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,d.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:o?"Edit Address":"Add New Address"}),(0,d.jsx)("button",{onClick:x,className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,d.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Type"}),(0,d.jsxs)("select",{name:"type",value:q.type,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,d.jsx)("option",{value:"HOME",children:"Home"}),(0,d.jsx)("option",{value:"WORK",children:"Work"}),(0,d.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{id:"is_default",name:"is_default",type:"checkbox",checked:q.is_default,onChange:u,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"is_default",className:"ml-2 block text-sm text-gray-900",children:"Set as default address"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(k.p,{label:"First Name",name:"first_name",type:"text",value:q.first_name,onChange:u,error:s.first_name,required:!0}),(0,d.jsx)(k.p,{label:"Last Name",name:"last_name",type:"text",value:q.last_name,onChange:u,error:s.last_name,required:!0})]}),(0,d.jsx)(k.p,{label:"Company (Optional)",name:"company",type:"text",value:q.company,onChange:u}),(0,d.jsx)(k.p,{label:"Address Line 1",name:"address_line_1",type:"text",value:q.address_line_1,onChange:u,error:s.address_line_1,required:!0}),(0,d.jsx)(k.p,{label:"Address Line 2 (Optional)",name:"address_line_2",type:"text",value:q.address_line_2,onChange:u}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsx)(k.p,{label:"City",name:"city",type:"text",value:q.city,onChange:u,error:s.city,required:!0}),(0,d.jsx)(k.p,{label:"State",name:"state",type:"text",value:q.state,onChange:u,error:s.state,required:!0}),(0,d.jsx)(k.p,{label:"Postal Code",name:"postal_code",type:"text",value:q.postal_code,onChange:u,error:s.postal_code,required:!0})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(k.p,{label:"Country",name:"country",type:"text",value:q.country,onChange:u,error:s.country,required:!0}),(0,d.jsx)(k.p,{label:"Phone (Optional)",name:"phone",type:"tel",value:q.phone,onChange:u,error:s.phone,placeholder:"+1234567890"})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:x,children:"Cancel"}),(0,d.jsx)(j.$,{type:"submit",loading:c,children:o?"Update Address":"Add Address"})]})]})]})})})]})}function o(){let a=g(),{user:b}=h(a=>a.auth),{profile:c,loading:f}=h(a=>a.customer),[m,n]=(0,e.useState)({date_of_birth:"",gender:""}),[o,p]=(0,e.useState)({}),[q,r]=(0,e.useState)(!1),s=a=>{let{name:b,value:c}=a.target;n(a=>({...a,[b]:c})),o[b]&&p(a=>({...a,[b]:""}))},t=async b=>{if(b.preventDefault(),(()=>{let a={};if(m.date_of_birth){let b=new Date(m.date_of_birth),c=new Date,d=c.getFullYear()-b.getFullYear();b>c?a.date_of_birth="Date of birth cannot be in the future":d>120&&(a.date_of_birth="Please enter a valid date of birth")}return p(a),0===Object.keys(a).length})())try{await a((0,i.j$)({...m,gender:m.gender||void 0})).unwrap(),l.Ay.success("Profile updated successfully!"),r(!1)}catch(a){l.Ay.error(a||"Failed to update profile")}},u=async()=>{try{await a((0,i.ii)()).unwrap(),l.Ay.success("Profile refreshed")}catch(a){l.Ay.error("Failed to refresh profile")}};return b?(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Customer Profile"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:u,loading:f,children:"Refresh"}),!q&&(0,d.jsx)(j.$,{variant:"primary",size:"sm",onClick:()=>r(!0),children:"Edit Profile"})]})]})}),(0,d.jsx)("div",{className:"px-6 py-4",children:q?(0,d.jsxs)("form",{onSubmit:t,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(k.p,{label:"Date of Birth",name:"date_of_birth",type:"date",value:m.date_of_birth,onChange:s,error:o.date_of_birth}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gender"}),(0,d.jsxs)("select",{name:"gender",value:m.gender,onChange:s,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,d.jsx)("option",{value:"",children:"Select Gender"}),(0,d.jsx)("option",{value:"M",children:"Male"}),(0,d.jsx)("option",{value:"F",children:"Female"}),(0,d.jsx)("option",{value:"O",children:"Other"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>{c&&n({date_of_birth:c.date_of_birth||"",gender:c.gender||""}),p({}),r(!1)},children:"Cancel"}),(0,d.jsx)(j.$,{type:"submit",loading:f,children:"Save Changes"})]})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:b.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:b.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:b.phone_number||"Not provided"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900 capitalize",children:b.user_type})]})]}),(0,d.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Additional Information"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Date of Birth"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c?.date_of_birth?new Date(c.date_of_birth).toLocaleDateString():"Not provided"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Gender"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c?.gender?"M"===c.gender?"Male":"F"===c.gender?"Female":"Other":"Not specified"})]})]})]}),(0,d.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Verification Status"}),(0,d.jsx)("p",{className:"mt-1 text-sm",children:(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${b.is_verified?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:b.is_verified?"Verified":"Unverified"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Member Since"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(b.created_at).toLocaleDateString()})]})]})})]})})]}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"No user data available"})})}var p=c(85814),q=c.n(p),r=c(16189),s=c(7791);let t=[{name:"Profile",href:s.bw.PROFILE,icon:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})},{name:"Addresses",href:s.bw.PROFILE_ADDRESSES,icon:(0,d.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})},{name:"Wishlist",href:s.bw.PROFILE_WISHLIST,icon:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})},{name:"Preferences",href:s.bw.PROFILE_PREFERENCES,icon:(0,d.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}];function u({children:a}){let b=(0,r.usePathname)(),[c,f]=(0,e.useState)(!1);return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-5",children:[(0,d.jsx)("aside",{className:"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3",children:(0,d.jsxs)("nav",{className:"space-y-1",children:[(0,d.jsx)("div",{className:"lg:hidden",children:(0,d.jsxs)("button",{type:"button",className:"bg-white p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>f(!c),children:[(0,d.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,d.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})}),(0,d.jsx)("div",{className:`${c?"block":"hidden"} lg:block`,children:t.map(a=>{let c=b===a.href;return(0,d.jsxs)(q(),{href:a.href||"/profile",className:`${c?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900"} group border-l-4 px-3 py-2 flex items-center text-sm font-medium`,onClick:()=>f(!1),children:[(0,d.jsx)("span",{className:`${c?"text-blue-500":"text-gray-400 group-hover:text-gray-500"} flex-shrink-0 -ml-1 mr-3`,children:a.icon}),(0,d.jsx)("span",{className:"truncate",children:a.name})]},a.name)})})]})}),(0,d.jsx)("div",{className:"space-y-6 sm:px-6 lg:px-0 lg:col-span-9",children:a})]})})})}let v=[{value:"en",label:"English"},{value:"es",label:"Spanish"},{value:"fr",label:"French"},{value:"de",label:"German"}],w=[{value:"USD",label:"US Dollar ($)"},{value:"EUR",label:"Euro (€)"},{value:"GBP",label:"British Pound (\xa3)"},{value:"CAD",label:"Canadian Dollar (C$)"}];function x(){let a=g(),{profile:b,loading:c}=h(a=>a.customer),[f,k]=(0,e.useState)({newsletter_subscription:!1,sms_notifications:!1,email_notifications:!0,language:"en",currency:"USD"}),[m,n]=(0,e.useState)(!1),o=a=>{let{name:b,value:c,type:d}=a.target,e=a.target.checked;k(a=>({...a,[b]:"checkbox"===d?e:c}))},p=async b=>{b.preventDefault();try{await a((0,i.wc)(f)).unwrap(),l.Ay.success("Preferences updated successfully!"),n(!1)}catch(a){l.Ay.error(a||"Failed to update preferences")}},q=async()=>{try{await a((0,i.ii)()).unwrap(),l.Ay.success("Preferences refreshed")}catch(a){l.Ay.error("Failed to refresh preferences")}};return(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Preferences"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:q,loading:c,children:"Refresh"}),!m&&(0,d.jsx)(j.$,{variant:"primary",size:"sm",onClick:()=>n(!0),children:"Edit Preferences"})]})]})}),(0,d.jsx)("div",{className:"px-6 py-4",children:m?(0,d.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notifications"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex items-center h-5",children:(0,d.jsx)("input",{id:"email_notifications",name:"email_notifications",type:"checkbox",checked:f.email_notifications,onChange:o,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,d.jsxs)("div",{className:"ml-3 text-sm",children:[(0,d.jsx)("label",{htmlFor:"email_notifications",className:"font-medium text-gray-700",children:"Email Notifications"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Receive order updates and promotions via email"})]})]}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex items-center h-5",children:(0,d.jsx)("input",{id:"sms_notifications",name:"sms_notifications",type:"checkbox",checked:f.sms_notifications,onChange:o,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,d.jsxs)("div",{className:"ml-3 text-sm",children:[(0,d.jsx)("label",{htmlFor:"sms_notifications",className:"font-medium text-gray-700",children:"SMS Notifications"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Receive order updates via SMS"})]})]}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex items-center h-5",children:(0,d.jsx)("input",{id:"newsletter_subscription",name:"newsletter_subscription",type:"checkbox",checked:f.newsletter_subscription,onChange:o,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,d.jsxs)("div",{className:"ml-3 text-sm",children:[(0,d.jsx)("label",{htmlFor:"newsletter_subscription",className:"font-medium text-gray-700",children:"Newsletter Subscription"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Receive our newsletter with deals and updates"})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Localization"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Language"}),(0,d.jsx)("select",{name:"language",value:f.language,onChange:o,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:v.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Currency"}),(0,d.jsx)("select",{name:"currency",value:f.currency,onChange:o,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:w.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>{b?.preferences&&k(b.preferences),n(!1)},children:"Cancel"}),(0,d.jsx)(j.$,{type:"submit",loading:c,children:"Save Preferences"})]})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notifications"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Email Notifications"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Receive order updates and promotions via email"})]}),(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f.email_notifications?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:f.email_notifications?"Enabled":"Disabled"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"SMS Notifications"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Receive order updates via SMS"})]}),(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f.sms_notifications?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:f.sms_notifications?"Enabled":"Disabled"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Newsletter Subscription"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Receive our newsletter with deals and updates"})]}),(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f.newsletter_subscription?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:f.newsletter_subscription?"Subscribed":"Unsubscribed"})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Localization"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Language"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:v.find(a=>a.value===f.language)?.label||f.language})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Currency"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:w.find(a=>a.value===f.currency)?.label||f.currency})]})]})]})]})})]})}var y=c(30474),z=c(53808),A=c(71431);function B(){let a=g(),{wishlist:b,loading:c}=h(a=>a.wishlist),[f,i]=(0,e.useState)(new Set),k=async b=>{i(a=>new Set(a).add(b));try{await a((0,z.Qg)(b)).unwrap(),l.Ay.success("Item removed from wishlist")}catch(a){l.Ay.error(a||"Failed to remove item")}finally{i(a=>{let c=new Set(a);return c.delete(b),c})}},m=async(b,c)=>{try{await a((0,A.bE)({productId:b,quantity:1})).unwrap(),l.Ay.success(`${c} added to cart`)}catch(a){l.Ay.error(a||"Failed to add to cart")}},n=async()=>{if(b?.items.length&&window.confirm("Are you sure you want to clear your entire wishlist?"))try{await a((0,z.Jw)()).unwrap(),l.Ay.success("Wishlist cleared")}catch(a){l.Ay.error(a||"Failed to clear wishlist")}},o=async()=>{try{await a((0,z.Ej)()).unwrap(),l.Ay.success("Wishlist refreshed")}catch(a){l.Ay.error("Failed to refresh wishlist")}};return c&&!b?(0,d.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,d.jsx)("div",{className:"px-6 py-4",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"h-20 w-20 bg-gray-200 rounded"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]},b))})]})})}):(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["My Wishlist",b?.items&&(0,d.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",b.items.length," ",1===b.items.length?"item":"items",")"]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:o,loading:c,children:"Refresh"}),b?.items&&b.items.length>0&&(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:n,className:"text-red-600 hover:text-red-700",children:"Clear All"})]})]})}),(0,d.jsx)("div",{className:"px-6 py-4",children:b?.items&&0!==b.items.length?(0,d.jsx)("div",{className:"space-y-4",children:b.items.map(a=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"h-20 w-20 relative",children:a.product.images&&a.product.images.length>0?(0,d.jsx)(y.default,{src:a.product.images.find(a=>a.is_primary)?.image||a.product.images[0].image,alt:a.product.name,fill:!0,className:"object-cover rounded-md"}):(0,d.jsx)("div",{className:"h-full w-full bg-gray-200 rounded-md flex items-center justify-center",children:(0,d.jsx)("svg",{className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),(0,d.jsx)("div",{className:"flex-1 min-w-0",children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)(q(),{href:a.product.slug?s.bw.PRODUCT_DETAIL(a.product.slug):s.bw.PRODUCTS,className:"text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2",children:a.product.name}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1 line-clamp-1",children:a.product.short_description}),(0,d.jsxs)("div",{className:"flex items-center mt-2 space-x-2",children:[(0,d.jsxs)("span",{className:"text-lg font-semibold text-gray-900",children:["$",a.product.discount_price||a.product.price]}),a.product.discount_price&&(0,d.jsxs)("span",{className:"text-sm text-gray-500 line-through",children:["$",a.product.price]})]}),(0,d.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Added ",new Date(a.added_at).toLocaleDateString()]})]})})}),(0,d.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,d.jsx)(j.$,{size:"sm",onClick:()=>m(a.product.id,a.product.name),disabled:!a.product.is_active,children:"Add to Cart"}),(0,d.jsx)(j.$,{variant:"outline",size:"sm",onClick:()=>k(a.id),loading:f.has(a.id),className:"text-red-600 hover:text-red-700",children:"Remove"})]})]},a.id))}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Your wishlist is empty"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Start adding products you love to your wishlist."}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(q(),{href:s.bw.PRODUCTS,children:(0,d.jsx)(j.$,{variant:"primary",children:"Browse Products"})})})]})})]})}}};