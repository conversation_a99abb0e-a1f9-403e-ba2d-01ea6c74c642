# Production Environment Configuration for MySQL Database Integration

# Database Configuration
DB_ENGINE=django.db.backends.mysql
DB_NAME=ecommerce_prod
DB_USER=ecommerce_prod_user
DB_PASSWORD=
DB_HOST=mysql-primary.internal
DB_PORT=3306

# Read Replica Configuration
DB_READ_HOST=mysql-replica.internal
DB_READ_USER=ecommerce_read_user
DB_READ_PASSWORD=

# SSL Configuration
DB_SSL_CA=/etc/mysql/ssl/ca-cert.pem
DB_SSL_CERT=/etc/mysql/ssl/client-cert.pem
DB_SSL_KEY=/etc/mysql/ssl/client-key.pem

# Connection Pool Settings
DB_CONN_MAX_AGE=3600
DB_CONN_HEALTH_CHECKS=true
DB_POOL_SIZE=20
DB_POOL_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE_FULL=0 2 * * *
BACKUP_SCHEDULE_INCREMENTAL=0 */4 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=
BACKUP_STORAGE_PATH=/var/backups/mysql
BACKUP_S3_BUCKET=ecommerce-db-backups-prod
BACKUP_S3_REGION=us-east-1

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_INTERVAL=60
MONITORING_ALERT_EMAIL=<EMAIL>
MONITORING_SLACK_WEBHOOK=

# Performance Thresholds
ALERT_CPU_THRESHOLD=80
ALERT_MEMORY_THRESHOLD=85
ALERT_DISK_THRESHOLD=90
ALERT_CONNECTION_THRESHOLD=80
ALERT_SLOW_QUERY_THRESHOLD=5
ALERT_REPLICATION_LAG_THRESHOLD=300

# Security Configuration
SECURITY_AUDIT_ENABLED=true
SECURITY_ENCRYPTION_ENABLED=true
SECURITY_SSL_REQUIRED=true
SECURITY_FAILED_LOGIN_THRESHOLD=5
SECURITY_LOCKOUT_DURATION=300

# Logging Configuration
LOG_LEVEL=INFO
LOG_SLOW_QUERIES=true
LOG_GENERAL_QUERIES=false
LOG_RETENTION_DAYS=30
LOG_MAX_SIZE=100M

# Replication Configuration
REPLICATION_ENABLED=true
REPLICATION_USER=replication
REPLICATION_PASSWORD=
REPLICATION_BINLOG_FORMAT=ROW
REPLICATION_EXPIRE_LOGS_DAYS=7

# Maintenance Configuration
MAINTENANCE_ENABLED=true
MAINTENANCE_SCHEDULE=0 3 * * 0
MAINTENANCE_OPTIMIZE_TABLES=true
MAINTENANCE_ANALYZE_TABLES=true
MAINTENANCE_CLEANUP_LOGS=true

# Application Configuration
DJANGO_SETTINGS_MODULE=ecommerce_project.settings.production
DEBUG=false
ALLOWED_HOSTS=ecommerce.com,www.ecommerce.com
SECRET_KEY=

# Cache Configuration
CACHE_BACKEND=redis
CACHE_LOCATION=redis://redis-cluster.internal:6379/1
CACHE_TIMEOUT=3600

# Celery Configuration
CELERY_BROKER_URL=redis://redis-cluster.internal:6379/0
CELERY_RESULT_BACKEND=redis://redis-cluster.internal:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=UTC

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.mailgun.org
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# Static Files Configuration
STATIC_URL=/static/
STATIC_ROOT=/var/www/ecommerce/static/
MEDIA_URL=/media/
MEDIA_ROOT=/var/www/ecommerce/media/

# AWS Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=ecommerce-static-prod
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=cdn.ecommerce.com

# Sentry Configuration
SENTRY_DSN=
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1