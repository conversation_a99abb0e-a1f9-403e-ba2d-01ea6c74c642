(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__09fbf3d1._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/constants/routes.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Centralized route definitions for the application
 */ // Main routes
__turbopack_context__.s({
    "ADMIN_ROUTES": ()=>ADMIN_ROUTES,
    "AUTH_ROUTES": ()=>AUTH_ROUTES,
    "MAIN_ROUTES": ()=>MAIN_ROUTES,
    "PROFILE_ROUTES": ()=>PROFILE_ROUTES,
    "ROUTE_LABELS": ()=>ROUTE_LABELS,
    "SELLER_ROUTES": ()=>SELLER_ROUTES
});
const MAIN_ROUTES = {
    HOME: '/',
    PRODUCTS: '/products',
    PRODUCT_DETAIL: (slug)=>`/products/${slug}`,
    CART: '/cart',
    CHECKOUT: '/checkout',
    SEARCH: '/search',
    ABOUT: '/about',
    CONTACT: '/contact',
    TERMS: '/terms',
    PRIVACY: '/privacy',
    FAQ: '/faq'
};
const AUTH_ROUTES = {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email'
};
const PROFILE_ROUTES = {
    DASHBOARD: '/profile',
    ORDERS: '/profile/orders',
    ORDER_DETAIL: (id)=>`/profile/orders/${id}`,
    ADDRESSES: '/profile/addresses',
    WISHLIST: '/profile/wishlist',
    SETTINGS: '/profile/settings',
    NOTIFICATIONS: '/profile/notifications'
};
const ADMIN_ROUTES = {
    DASHBOARD: '/admin',
    ANALYTICS: '/admin/analytics',
    ORDERS: '/admin/orders',
    ORDER_DETAIL: (id)=>`/admin/orders/${id}`,
    PRODUCTS: '/admin/products',
    PRODUCT_EDIT: (id)=>`/admin/products/${id}/edit`,
    PRODUCT_CREATE: '/admin/products/create',
    CUSTOMERS: '/admin/customers',
    CUSTOMER_DETAIL: (id)=>`/admin/customers/${id}`,
    CONTENT: '/admin/content',
    REPORTS: '/admin/reports',
    SYSTEM: '/admin/system',
    NOTIFICATIONS: '/admin/notifications',
    SETTINGS: '/admin/settings'
};
const SELLER_ROUTES = {
    DASHBOARD: '/seller/dashboard',
    PRODUCTS: '/seller/products',
    PRODUCT_EDIT: (id)=>`/seller/products/${id}/edit`,
    PRODUCT_CREATE: '/seller/products/create',
    ORDERS: '/seller/orders',
    ORDER_DETAIL: (id)=>`/seller/orders/${id}`,
    PROFILE: '/seller/profile',
    KYC: '/seller/kyc',
    BANK_ACCOUNTS: '/seller/bank-accounts',
    PAYOUTS: '/seller/payouts',
    ANALYTICS: '/seller/analytics',
    SETTINGS: '/seller/settings'
};
const ROUTE_LABELS = {
    '/': 'Home',
    '/products': 'Products',
    '/cart': 'Shopping Cart',
    '/checkout': 'Checkout',
    '/search': 'Search',
    '/about': 'About Us',
    '/contact': 'Contact Us',
    '/terms': 'Terms of Service',
    '/privacy': 'Privacy Policy',
    '/faq': 'FAQ',
    '/auth/login': 'Login',
    '/auth/register': 'Register',
    '/auth/forgot-password': 'Forgot Password',
    '/auth/reset-password': 'Reset Password',
    '/auth/verify-email': 'Verify Email',
    '/profile': 'My Account',
    '/profile/orders': 'My Orders',
    '/profile/addresses': 'My Addresses',
    '/profile/wishlist': 'My Wishlist',
    '/profile/settings': 'Account Settings',
    '/profile/notifications': 'Notifications',
    '/admin': 'Admin Dashboard',
    '/admin/analytics': 'Analytics',
    '/admin/orders': 'Orders Management',
    '/admin/products': 'Products Management',
    '/admin/products/create': 'Create Product',
    '/admin/customers': 'Customers Management',
    '/admin/content': 'Content Management',
    '/admin/reports': 'Reports',
    '/admin/system': 'System Health',
    '/admin/notifications': 'Notifications',
    '/admin/settings': 'Admin Settings',
    '/seller/dashboard': 'Seller Dashboard',
    '/seller/products': 'My Products',
    '/seller/products/create': 'Add New Product',
    '/seller/orders': 'My Orders',
    '/seller/profile': 'Seller Profile',
    '/seller/kyc': 'KYC Verification',
    '/seller/bank-accounts': 'Bank Accounts',
    '/seller/payouts': 'Payouts',
    '/seller/analytics': 'Sales Analytics',
    '/seller/settings': 'Seller Settings'
};
}),
"[project]/src/middleware/auth.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Authentication middleware utilities
__turbopack_context__.s({
    "authMiddleware": ()=>authMiddleware,
    "shouldProcessAuth": ()=>shouldProcessAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [middleware-edge] (ecmascript)");
;
;
// Protected routes that require authentication
const PROTECTED_ROUTES = [
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PROFILE_ROUTES"].DASHBOARD,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PROFILE_ROUTES"].ORDERS,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["MAIN_ROUTES"].CART,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["MAIN_ROUTES"].CHECKOUT,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["ADMIN_ROUTES"].DASHBOARD,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SELLER_ROUTES"].DASHBOARD
];
// Guest-only routes (redirect if authenticated)
const GUEST_ROUTES = [
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].LOGIN,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].REGISTER,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].FORGOT_PASSWORD,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].RESET_PASSWORD
];
function authMiddleware(request) {
    const { pathname } = request.nextUrl;
    // Get tokens from cookies or headers
    const accessToken = request.cookies.get('access_token')?.value;
    const refreshToken = request.cookies.get('refresh_token')?.value;
    const isAuthenticated = !!(accessToken || refreshToken);
    // Check if route requires authentication
    const isProtectedRoute = PROTECTED_ROUTES.some((route)=>pathname.startsWith(route));
    // Check if route is guest-only
    const isGuestRoute = GUEST_ROUTES.some((route)=>pathname.startsWith(route));
    // Check if route is admin-only or seller-only
    const isAdminRoute = pathname.startsWith('/admin');
    const isSellerRoute = pathname.startsWith('/seller');
    // Redirect unauthenticated users from protected routes
    if (isProtectedRoute && !isAuthenticated) {
        const loginUrl = new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].LOGIN, request.url);
        loginUrl.searchParams.set('redirect', pathname);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    // Redirect authenticated users from guest-only routes
    if (isGuestRoute && isAuthenticated) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["MAIN_ROUTES"].HOME, request.url));
    }
    // For admin/seller routes, we'll do basic authentication check here
    // but the full authorization will be handled in the components
    // since we need user data from the store
    if ((isAdminRoute || isSellerRoute) && !isAuthenticated) {
        const loginUrl = new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_ROUTES"].LOGIN, request.url);
        loginUrl.searchParams.set('redirect', pathname);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
function shouldProcessAuth(pathname) {
    // Skip API routes, static files, and Next.js internals
    if (pathname.startsWith('/api/') || pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico') || pathname.includes('.')) {
        return false;
    }
    return true;
}
}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>config,
    "middleware": ()=>middleware
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/middleware/auth.ts [middleware-edge] (ecmascript)");
;
function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip processing for certain paths
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["shouldProcessAuth"])(pathname)) {
        return;
    }
    // Apply authentication middleware
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["authMiddleware"])(request);
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__09fbf3d1._.js.map