"use strict";exports.id=33,exports.ids=[33],exports.modules={51907:(a,b,c)=>{c.d(b,{p:()=>e});var d=c(60687);function e({label:a,error:b,helperText:c,className:e="",id:f,...g}){let h=f||`input-${Math.random().toString(36).substr(2,9)}`,i=b?`${h}-error`:void 0,j=c?`${h}-helper`:void 0,k=`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors touch-manipulation ${b?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400"} disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed disabled:border-gray-200 ${e}`;return(0,d.jsxs)("div",{className:"space-y-1",children:[a&&(0,d.jsx)("label",{htmlFor:h,className:"block text-sm font-medium text-gray-700",children:a}),(0,d.jsx)("input",{id:h,className:k,"aria-invalid":b?"true":"false","aria-describedby":[i,j].filter(Boolean).join(" ")||void 0,...g}),b&&(0,d.jsx)("p",{id:i,className:"text-sm text-red-600",role:"alert",children:b}),c&&!b&&(0,d.jsx)("p",{id:j,className:"text-sm text-gray-500",children:c})]})}c(43210)},63547:(a,b,c)=>{c.d(b,{R:()=>e});var d=c(60687);function e({size:a="md",text:b,className:c=""}){return(0,d.jsx)("div",{className:`flex items-center justify-center ${c}`,children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsxs)("svg",{className:`animate-spin ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[a]} text-blue-600`,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),b&&(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b})]})})}c(43210)},83033:(a,b,c)=>{c.d(b,{CU:()=>u,QB:()=>p,OV:()=>t,s4:()=>q});var d=c(60687),e=c(43210),f=c(16189),g=c(68991),h=c(56423),i=c(2643),j=c(51907),k=c(7791),l=c(85814),m=c.n(l),n=c(37590);function o(){let[a,b]=(0,e.useState)({email:"",password:""}),[c,l]=(0,e.useState)({}),o=(0,g.jL)(),p=(0,f.useRouter)();(0,f.useSearchParams)();let{loading:q,error:r}=(0,g.GV)(a=>a.auth),s=a=>{let{name:d,value:e}=a.target;b(a=>({...a,[d]:e})),c[d]&&l(a=>({...a,[d]:""}))},t=async b=>{if(b.preventDefault(),(()=>{let b={};return a.email?/\S+@\S+\.\S+/.test(a.email)||(b.email="Email is invalid"):b.email="Email is required",a.password||(b.password="Password is required"),l(b),0===Object.keys(b).length})())try{await o((0,h.Lx)(a)).unwrap(),n.Ay.success("Login successful!"),p.push(k.bw.HOME)}catch(a){n.Ay.error(a||"Login failed")}};return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,d.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,d.jsx)(m(),{href:k.bw.REGISTER,className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,d.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:t,children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(j.p,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:s,error:c.email,placeholder:"Enter your email"}),(0,d.jsx)(j.p,{label:"Password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a.password,onChange:s,error:c.password,placeholder:"Enter your password"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,d.jsx)("div",{className:"text-sm",children:(0,d.jsx)(m(),{href:k.bw.FORGOT_PASSWORD,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),r&&(0,d.jsx)("div",{className:"text-red-600 text-sm text-center",children:r}),(0,d.jsx)("div",{children:(0,d.jsx)(i.$,{type:"submit",loading:q,className:"w-full",size:"lg",children:"Sign in"})})]})]})})}function p(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{children:"Loading..."}),children:(0,d.jsx)(o,{})})}function q(){let[a,b]=(0,e.useState)({username:"",email:"",password:"",password_confirm:"",user_type:k.Cy.CUSTOMER,phone_number:""}),[c,l]=(0,e.useState)({}),o=(0,g.jL)(),p=(0,f.useRouter)(),{loading:q,error:r}=(0,g.GV)(a=>a.auth),s=a=>{let{name:d,value:e}=a.target;b(a=>({...a,[d]:e})),c[d]&&l(a=>({...a,[d]:""}))},t=async b=>{if(b.preventDefault(),(()=>{let b={},c=(a=>{let b=[];return a.length<k.oO.USERNAME_MIN_LENGTH&&b.push(`Username must be at least ${k.oO.USERNAME_MIN_LENGTH} characters long`),/^[a-zA-Z0-9_]+$/.test(a)||b.push("Username can only contain letters, numbers, and underscores"),b})(a.username);if(c.length>0&&(b.username=c[0]),a.email){let c;c=a.email,k.oO.EMAIL_REGEX.test(c)||(b.email="Please enter a valid email address")}else b.email="Email is required";let d=(a=>{let b=[];return a.length<k.oO.PASSWORD_MIN_LENGTH&&b.push(`Password must be at least ${k.oO.PASSWORD_MIN_LENGTH} characters long`),/(?=.*[a-z])/.test(a)||b.push("Password must contain at least one lowercase letter"),/(?=.*[A-Z])/.test(a)||b.push("Password must contain at least one uppercase letter"),/(?=.*\d)/.test(a)||b.push("Password must contain at least one number"),b})(a.password);d.length>0&&(b.password=d[0]);let e=a.password!==a.password_confirm?"Passwords do not match":null;return e&&(b.password_confirm=e),a.phone_number&&!/^[\+]?[1-9][\d]{0,15}$/.test(a.phone_number)&&(b.phone_number="Please enter a valid phone number"),l(b),0===Object.keys(b).length})())try{await o((0,h.DY)(a)).unwrap(),n.Ay.success("Registration successful! Welcome to our platform!"),p.push(k.bw.HOME)}catch(a){n.Ay.error(a||"Registration failed")}};return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,d.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,d.jsx)(m(),{href:k.bw.LOGIN,className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,d.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:t,children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(j.p,{label:"Username",name:"username",type:"text",autoComplete:"username",required:!0,value:a.username,onChange:s,error:c.username,placeholder:"Choose a username",helperText:"Username must be at least 3 characters and contain only letters, numbers, and underscores"}),(0,d.jsx)(j.p,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:s,error:c.email,placeholder:"Enter your email"}),(0,d.jsx)(j.p,{label:"Password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:a.password,onChange:s,error:c.password,placeholder:"Create a password",helperText:"Password must be at least 8 characters with uppercase, lowercase, and number"}),(0,d.jsx)(j.p,{label:"Confirm Password",name:"password_confirm",type:"password",autoComplete:"new-password",required:!0,value:a.password_confirm,onChange:s,error:c.password_confirm,placeholder:"Confirm your password"}),(0,d.jsx)(j.p,{label:"Phone Number (Optional)",name:"phone_number",type:"tel",autoComplete:"tel",value:a.phone_number,onChange:s,error:c.phone_number,placeholder:"+**********"}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("label",{htmlFor:"user_type",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,d.jsxs)("select",{id:"user_type",name:"user_type",value:a.user_type,onChange:s,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,d.jsx)("option",{value:k.Cy.CUSTOMER,children:"Customer"}),(0,d.jsx)("option",{value:k.Cy.SELLER,children:"Seller"})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",(0,d.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Terms and Conditions"})," ","and"," ",(0,d.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),r&&(0,d.jsx)("div",{className:"text-red-600 text-sm text-center",children:r}),(0,d.jsx)("div",{children:(0,d.jsx)(i.$,{type:"submit",loading:q,className:"w-full",size:"lg",children:"Create Account"})})]})]})})}c(89465);var r=c(63547);function s({children:a,requireAuth:b=!1,requireGuest:c=!1,allowedUserTypes:e=[],redirectTo:h,fallback:i}){(0,g.jL)();let j=(0,f.useRouter)(),{user:l,isAuthenticated:m,loading:n}=(0,g.GV)(a=>a.auth);if(n)return i||(0,d.jsx)(r.R,{});if(b&&!m){let a=h||k.bw.LOGIN;return j.push(a),i||(0,d.jsx)(r.R,{})}if(c&&m){let a=h||k.bw.HOME;return j.push(a),i||(0,d.jsx)(r.R,{})}if(b&&m&&e.length>0&&l&&!e.includes(l.user_type)){let a=h||k.bw.HOME;return j.push(a),i||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,d.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}return(0,d.jsx)(d.Fragment,{children:a})}function t({children:a,allowedUserTypes:b=[],redirectTo:c,fallback:e}){return(0,d.jsx)(s,{requireAuth:!0,allowedUserTypes:b,redirectTo:c,fallback:e,children:a})}function u({children:a,redirectTo:b,fallback:c}){return(0,d.jsx)(s,{requireGuest:!0,redirectTo:b,fallback:c,children:a})}c(90567),c(43595)},89465:(a,b,c)=>{c.d(b,{z:()=>k});var d=c(60687),e=c(16189),f=c(68991),g=c(56423),h=c(2643),i=c(7791),j=c(37590);function k({variant:a="ghost",size:b="sm",className:c="",children:k="Logout",redirectTo:l=i.bw.HOME}){let m=(0,f.jL)(),n=(0,e.useRouter)(),{loading:o}=(0,f.GV)(a=>a.auth),p=async()=>{try{await m((0,g.y4)()).unwrap(),j.Ay.success("Logged out successfully"),n.push(l)}catch(a){j.Ay.error("Logout failed"),n.push(l)}};return(0,d.jsx)(h.$,{variant:a,size:b,className:c,onClick:p,loading:o,children:k})}}};