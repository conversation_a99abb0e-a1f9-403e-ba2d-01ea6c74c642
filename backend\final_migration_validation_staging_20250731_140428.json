{"timestamp": "2025-07-31T14:04:10.979243", "environment": "staging", "tests": {"Database Connectivity": {"status": "PASS", "message": "Database connectivity verified", "timestamp": "2025-07-31T14:04:11.006911"}, "Data Integrity": {"status": "FAIL", "message": "Data integrity validation failed: No users found in database", "timestamp": "2025-07-31T14:04:11.111685"}, "Database Performance": {"status": "PASS", "message": "Database performance acceptable. Query time: 0.00s", "timestamp": "2025-07-31T14:04:11.113694"}, "Application Functionality": {"status": "PASS", "message": "Application functionality verified", "timestamp": "2025-07-31T14:04:12.103605"}, "API Endpoints": {"status": "FAIL", "message": "Failed endpoints: /api/v1/health/ (Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/health/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA351FE530>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))), /api/v1/products/ (Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/products/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA351C7CA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))), /api/v1/categories/ (Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/categories/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA351E3A30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))), /admin/ (Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /admin/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA351E0AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')))", "timestamp": "2025-07-31T14:04:28.391990"}, "Cache Functionality": {"status": "PASS", "message": "Cache functionality verified", "timestamp": "2025-07-31T14:04:28.391990"}, "System Resources": {"status": "FAIL", "message": "System resource validation failed: [Errno 2] No such file or directory: '/proc/meminfo'", "timestamp": "2025-07-31T14:04:28.391990"}, "Monitoring Systems": {"status": "WARNING", "message": "Monitoring issues: Missing log files: logs/application.log, logs/database.log, logs/performance.log", "timestamp": "2025-07-31T14:04:28.445765"}, "Backup Systems": {"status": "WARNING", "message": "Backup system not enabled", "timestamp": "2025-07-31T14:04:28.445765"}, "Security Configuration": {"status": "PASS", "message": "Security configuration validated", "timestamp": "2025-07-31T14:04:28.445765"}, "SSL Configuration": {"status": "WARNING", "message": "Database SSL not configured", "timestamp": "2025-07-31T14:04:28.445765"}, "Query Performance": {"status": "PASS", "message": "Query performance acceptable. Average: 0.001s", "timestamp": "2025-07-31T14:04:28.455505"}, "Connection Pooling": {"status": "PASS", "message": "Connection pooling working. Avg: 0.000s", "timestamp": "2025-07-31T14:04:28.455505"}}, "overall_status": "FAIL", "summary": {"total_tests": 13, "passed_tests": 7, "failed_tests": 3, "warnings": 3}}