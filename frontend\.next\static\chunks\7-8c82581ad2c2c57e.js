"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7],{662:(e,t,s)=>{s.d(t,{E1:()=>a,lX:()=>i,n0:()=>c,tb:()=>l});var r=s(9406);let o={TOKEN_INVALID:"token_invalid",TOKEN_EXPIRED:"token_expired",TOKEN_NOT_FOUND:"token_not_found",TOKEN_ALREADY_USED:"token_already_used",EMAIL_NOT_FOUND:"email_not_found",EMAIL_SEND_FAILED:"email_send_failed",EMAIL_INVALID_FORMAT:"email_invalid_format",PASSWORD_TOO_WEAK:"password_too_weak",PASSWORD_MISMATCH:"password_mismatch",PASSWORD_SAME_AS_OLD:"password_same_as_old",RATE_LIMIT_EXCEEDED:"rate_limit_exceeded",TOO_MANY_ATTEMPTS:"too_many_attempts",NETWORK_ERROR:"network_error",SERVER_ERROR:"server_error",VALIDATION_ERROR:"validation_error"},n={[o.TOKEN_INVALID]:"This password reset link is invalid. Please request a new one.",[o.TOKEN_EXPIRED]:"This password reset link has expired. Please request a new one.",[o.TOKEN_NOT_FOUND]:"This password reset link is not valid. Please request a new one.",[o.TOKEN_ALREADY_USED]:"This password reset link has already been used. Please request a new one if needed.",[o.EMAIL_NOT_FOUND]:"If an account with this email exists, you will receive a password reset link.",[o.EMAIL_SEND_FAILED]:"We encountered an issue sending the reset email. Please try again.",[o.EMAIL_INVALID_FORMAT]:"Please enter a valid email address.",[o.PASSWORD_TOO_WEAK]:"Password must be at least 8 characters with uppercase, lowercase, number, and special character.",[o.PASSWORD_MISMATCH]:"Passwords do not match. Please try again.",[o.PASSWORD_SAME_AS_OLD]:"New password must be different from your current password.",[o.RATE_LIMIT_EXCEEDED]:"Too many password reset requests. Please wait before trying again.",[o.TOO_MANY_ATTEMPTS]:"Too many failed attempts. Please wait 15 minutes before trying again.",[o.NETWORK_ERROR]:"Unable to connect to the server. Please check your internet connection and try again.",[o.SERVER_ERROR]:"A server error occurred. Please try again later.",[o.VALIDATION_ERROR]:"Please check your input and try again."},i=e=>[o.NETWORK_ERROR,o.SERVER_ERROR,o.EMAIL_SEND_FAILED].includes(e),a=e=>{let t=(0,r.qQ)(e);return t.code in n?n[t.code]:(0,r.sC)(e)},l=(e,t,s)=>{(0,r.qQ)(e),(0,r.vV)(e,"PasswordReset.".concat(t))},c=(e,t)=>{let{logSecurityEvent:r}=s(1935),o=!1===t.success&&t.errorCode?"medium":"low";r("password_reset","password_reset_".concat(e),o,{success:t.success,errorCode:t.errorCode,email:t.email,token:t.token,...t.additionalData},{ipAddress:t.ipAddress,userAgent:t.userAgent})}},1821:(e,t,s)=>{s.d(t,{mO:()=>i});var r=s(1935),o=s(4444);let n={onPasswordResetSuccess:(e,t)=>{o.vC.markTokenUsed(t),(0,r.logSecurityEvent)("password_reset","password_reset_completed","low",{email:e.substring(0,3)+"***@"+e.split("@")[1],success:!0,timestamp:new Date().toISOString()}),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user")},onPasswordResetRequest:e=>{(0,r.logSecurityEvent)("password_reset","password_reset_requested","low",{email:e.substring(0,3)+"***@"+e.split("@")[1],timestamp:new Date().toISOString()})},onPasswordResetFailure:(e,t,s)=>{(0,r.logSecurityEvent)("password_reset","password_reset_failed","medium",{email:e?e.substring(0,3)+"***@"+e.split("@")[1]:void 0,errorCode:t,token:s?s.substring(0,8)+"...":void 0,timestamp:new Date().toISOString()})},getPostLoginRedirect:()=>{let e=sessionStorage.getItem("post_login_redirect");return e?(sessionStorage.removeItem("post_login_redirect"),e):"/"},setPostLoginRedirect:e=>{sessionStorage.setItem("post_login_redirect",e)},isSessionValid:()=>{let e=localStorage.getItem("access_token"),t=localStorage.getItem("user");return!!(e&&t)},clearAuthData:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user"),sessionStorage.removeItem("post_login_redirect")},getCurrentUser:()=>{try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error parsing user data:",e),null}},updateUserPassword:e=>{try{let t=localStorage.getItem("user");if(t){let s=JSON.parse(t);e&&(s.password_updated_at=new Date().toISOString()),localStorage.setItem("user",JSON.stringify(s))}}catch(e){console.error("Error updating user data:",e)}},isPasswordResetAvailable:()=>{let e="localStorage"in window,t="sessionStorage"in window;return e&&t},getPasswordResetStats:()=>({tokenStats:o.vC.getStats(),isSystemAvailable:n.isPasswordResetAvailable(),currentUser:n.getCurrentUser(),sessionValid:n.isSessionValid()})},i=()=>({onPasswordResetSuccess:n.onPasswordResetSuccess,onPasswordResetRequest:n.onPasswordResetRequest,onPasswordResetFailure:n.onPasswordResetFailure,isSessionValid:n.isSessionValid,getCurrentUser:n.getCurrentUser,clearAuthData:n.clearAuthData,getPostLoginRedirect:n.getPostLoginRedirect,setPostLoginRedirect:n.setPostLoginRedirect})},1935:(e,t,s)=>{s.r(t),s.d(t,{SUSPICIOUS_PATTERNS:()=>r,getPerformanceMetrics:()=>m,getSecurityMetrics:()=>l,logSecurityEvent:()=>i,recordPerformanceMetric:()=>g,shouldBlockIP:()=>c,withPerformanceMonitoring:()=>p});let r={RAPID_RESET_REQUESTS:{type:"rapid_requests",threshold:5,timeWindow:3e5,description:"Multiple password reset requests in short time"},INVALID_TOKEN_ATTEMPTS:{type:"invalid_tokens",threshold:10,timeWindow:6e5,description:"Multiple invalid token validation attempts"},EMAIL_ENUMERATION:{type:"email_enumeration",threshold:20,timeWindow:9e5,description:"Potential email enumeration attack"},BRUTE_FORCE_TOKENS:{type:"brute_force",threshold:50,timeWindow:18e5,description:"Potential brute force token attack"}};class o{addEvent(e){this.events.push(e),this.events.length>this.maxEvents&&(this.events=this.events.slice(-this.maxEvents)),this.checkSuspiciousActivity(e)}getRecentEvents(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:36e5,t=Date.now()-e;return this.events.filter(e=>new Date(e.timestamp).getTime()>t)}getEventsByType(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36e5;return this.getRecentEvents(t).filter(t=>t.event===e)}getEventsByIP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36e5;return this.getRecentEvents(t).filter(t=>t.ipAddress===e)}checkSuspiciousActivity(e){let t=e.ipAddress;t&&Object.entries(r).forEach(e=>{let[s,r]=e,o=this.getEventsByIP(t,r.timeWindow),n=this.filterEventsByPattern(o,r);n.length>=r.threshold&&this.triggerSuspiciousActivityAlert(s,r,t,n)})}filterEventsByPattern(e,t){switch(t.type){case"rapid_requests":return e.filter(e=>"password_reset_request"===e.event);case"invalid_tokens":return e.filter(e=>"token_validation"===e.event&&!1===e.details.success);case"email_enumeration":return e.filter(e=>"password_reset_request"===e.event);case"brute_force":return e.filter(e=>"token_validation"===e.event&&!1===e.details.success);default:return[]}}triggerSuspiciousActivityAlert(e,t,s,r){let o={type:"suspicious_activity",event:"pattern_detected",timestamp:new Date().toISOString(),severity:"high",details:{pattern:e,description:t.description,threshold:t.threshold,actualCount:r.length,timeWindow:t.timeWindow,affectedIP:s,eventSample:r.slice(0,5)},ipAddress:s};this.addEvent(o),this.notifyAdministrators(o)}notifyAdministrators(e){}constructor(){this.events=[],this.maxEvents=1e4}}let n=new o,i=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"low",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4?arguments[4]:void 0,i={type:e,event:t,timestamp:new Date().toISOString(),severity:s,details:{...r,...a(r)},userAgent:null==o?void 0:o.userAgent,ipAddress:null==o?void 0:o.ipAddress,sessionId:null==o?void 0:o.sessionId};n.addEvent(i)},a=e=>{let t={...e};if(t.email&&"string"==typeof t.email){let[e,s]=t.email.split("@");t.email="".concat(e.substring(0,2),"***@").concat(s)}return t.token&&"string"==typeof t.token&&(t.token="".concat(t.token.substring(0,8),"...")),delete t.password,delete t.newPassword,delete t.confirmPassword,t},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:36e5,t=n.getRecentEvents(e),s={totalEvents:t.length,eventsByType:{},eventsBySeverity:{},suspiciousActivityCount:0,topIPs:{},recentAlerts:[]};return t.forEach(e=>{s.eventsByType[e.event]=(s.eventsByType[e.event]||0)+1,s.eventsBySeverity[e.severity]=(s.eventsBySeverity[e.severity]||0)+1,"suspicious_activity"===e.type&&(s.suspiciousActivityCount++,s.recentAlerts.push(e)),e.ipAddress&&(s.topIPs[e.ipAddress]=(s.topIPs[e.ipAddress]||0)+1)}),s.recentAlerts.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),s},c=e=>n.getEventsByIP(e,18e5).filter(e=>"high"===e.severity||"critical"===e.severity).length>0;class d{recordMetric(e){this.metrics.push(e),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics))}getAverageResponseTime(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36e5,s=Date.now()-t,r=this.metrics.filter(t=>t.operation===e&&new Date(t.timestamp).getTime()>s);return 0===r.length?0:r.reduce((e,t)=>e+t.duration,0)/r.length}getSuccessRate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36e5,s=Date.now()-t,r=this.metrics.filter(t=>t.operation===e&&new Date(t.timestamp).getTime()>s);return 0===r.length?0:r.filter(e=>e.success).length/r.length*100}constructor(){this.metrics=[],this.maxMetrics=5e3}}let u=new d,g=(e,t,s,r)=>{let o=Date.now()-t;u.recordMetric({operation:e,duration:o,timestamp:new Date().toISOString(),success:s,details:r}),o>5e3&&i("password_reset","slow_operation","medium",{operation:e,duration:o,success:s,...r})},m=()=>["email_send","token_validation","password_reset"].reduce((e,t)=>(e[t]={averageResponseTime:u.getAverageResponseTime(t),successRate:u.getSuccessRate(t)},e),{}),p=async(e,t,s)=>{let r=Date.now(),o=!1;try{let e=await t();return o=!0,e}catch(e){throw o=!1,e}finally{g(e,r,o,s)}}},2476:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(2302),o=s(7141),n=s(1935);let i={requestPasswordReset:async e=>(0,n.withPerformanceMonitoring)("password_reset_request",async()=>{try{return await r.uE.post(o.Sn.AUTH.FORGOT_PASSWORD,{email:e})}catch(e){return{success:!1,error:{message:"Failed to request password reset",code:"request_failed",status_code:500}}}},{email:e.substring(0,3)+"***"}),validateResetToken:async e=>(0,n.withPerformanceMonitoring)("token_validation",async()=>{try{return await r.uE.get(o.Sn.AUTH.VALIDATE_RESET_TOKEN(e))}catch(e){return{success:!1,error:{message:"Failed to validate reset token",code:"validation_failed",status_code:500}}}},{token:e.substring(0,8)+"..."}),resetPassword:async(e,t)=>(0,n.withPerformanceMonitoring)("password_reset",async()=>{try{return await r.uE.post(o.Sn.AUTH.RESET_PASSWORD,{token:e,password:t})}catch(e){return{success:!1,error:{message:"Failed to reset password",code:"reset_failed",status_code:500}}}},{token:e.substring(0,8)+"..."})}},4444:(e,t,s)=>{s.d(t,{vC:()=>a,xf:()=>l});let r={maxTokenAge:36e5,cleanupInterval:9e5,maxStoredTokens:100},o="password_reset_tokens";class n{startCleanup(){this.cleanupTimer&&this.stopCleanup(),this.cleanupTimer=setInterval(()=>{this.performCleanup()},this.config.cleanupInterval),this.performCleanup()}stopCleanup(){this.cleanupTimer&&(clearInterval(this.cleanupTimer),this.cleanupTimer=null)}performCleanup(){try{let e=this.getStoredTokens(),t=Date.now(),s=e.filter(e=>t-e.timestamp<this.config.maxTokenAge&&!e.used).sort((e,t)=>t.timestamp-e.timestamp).slice(0,this.config.maxStoredTokens);this.setStoredTokens(s);let r=e.length-s.length;r>0&&console.log("Token cleanup: Removed ".concat(r," expired/used tokens"))}catch(e){console.error("Error during token cleanup:",e)}}storeToken(e,t){try{let s=this.getStoredTokens().filter(t=>t.token!==e),r={token:e,timestamp:Date.now(),used:!1,email:t};s.push(r);let o=s.sort((e,t)=>t.timestamp-e.timestamp).slice(0,this.config.maxStoredTokens);this.setStoredTokens(o)}catch(e){console.error("Error storing token:",e)}}markTokenAsUsed(e){try{let t=this.getStoredTokens(),s=t.find(t=>t.token===e);s&&(s.used=!0,this.setStoredTokens(t))}catch(e){console.error("Error marking token as used:",e)}}isTokenValid(e){try{let t=this.getStoredTokens().find(t=>t.token===e);if(!t||t.used)return!1;return Date.now()-t.timestamp<this.config.maxTokenAge}catch(e){return console.error("Error checking token validity:",e),!1}}getTokenInfo(e){try{return this.getStoredTokens().find(t=>t.token===e)||null}catch(e){return console.error("Error getting token info:",e),null}}getCleanupStats(){try{let e=this.getStoredTokens(),t=Date.now(),s=0,r=0,o=0,n=null,i=null;return e.forEach(e=>{let a=t-e.timestamp;e.used?o++:a>=this.config.maxTokenAge?r++:s++,(null===n||e.timestamp<n)&&(n=e.timestamp),(null===i||e.timestamp>i)&&(i=e.timestamp)}),{totalTokens:e.length,validTokens:s,expiredTokens:r,usedTokens:o,oldestToken:n,newestToken:i}}catch(e){return console.error("Error getting cleanup stats:",e),{totalTokens:0,validTokens:0,expiredTokens:0,usedTokens:0,oldestToken:null,newestToken:null}}}clearAllTokens(){try{this.setStoredTokens([]),console.log("All password reset tokens cleared")}catch(e){console.error("Error clearing all tokens:",e)}}getStoredTokens(){try{let e=localStorage.getItem(o);return e?JSON.parse(e):[]}catch(e){return console.error("Error reading stored tokens:",e),[]}}setStoredTokens(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(e){console.error("Error storing tokens:",e)}}constructor(e=r){this.cleanupTimer=null,this.config=e}}let i=new n,a={storeResetToken:(e,t)=>{i.storeToken(e,t)},markTokenUsed:e=>{i.markTokenAsUsed(e)},isTokenValid:e=>i.isTokenValid(e),getTokenInfo:e=>i.getTokenInfo(e),getStats:()=>i.getCleanupStats(),forceCleanup:()=>{i.performCleanup()},clearAll:()=>{i.clearAllTokens()}},l=()=>({storeToken:a.storeResetToken,markTokenUsed:a.markTokenUsed,isTokenValid:a.isTokenValid,getTokenInfo:a.getTokenInfo,getStats:a.getStats,forceCleanup:a.forceCleanup})},9406:(e,t,s)=>{s.d(t,{ZB:()=>i,qQ:()=>r,sC:()=>o,vV:()=>n}),s(3568);let r=e=>e?e.type&&e.message?{message:e.message,code:e.code||e.type,status_code:e.status_code}:e.message&&e.code?{message:e.message,code:e.code,status_code:e.status_code}:e instanceof Error?{message:e.message,code:"error"}:"string"==typeof e?{message:e,code:"string_error"}:{message:"An unexpected error occurred",code:"unknown_error"}:{message:"An unexpected error occurred",code:"unknown_error"},o=e=>r(e).message,n=(e,t,s)=>{console.error("Error logged:",{context:t,error:r(e),additionalData:s,timestamp:new Date().toISOString()})},i=async function(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let o=1;o<=s;o++)try{return await e()}catch(e){if(t=e instanceof Error?e:Error("Unknown error"),o===s)throw t;await new Promise(e=>setTimeout(e,r*o))}throw t}}}]);