exports.id=947,exports.ids=[947],exports.modules={7185:(a,b,c)=>{"use strict";c.d(b,{hR:()=>n,zh:()=>m,U5:()=>p,PL:()=>r,tl:()=>o,CP:()=>l,F7:()=>q,TJ:()=>i});var d=c(60687),e=c(43210),f=c(54864),g=c(16189),h=c(35880);let i=()=>{let a=(0,f.wA)(),b=(0,g.useRouter)(),{loading:c,error:i}=(0,f.d4)(a=>a.seller),[j,k]=(0,e.useState)({business_name:"",business_type:"INDIVIDUAL",tax_id:"",gstin:"",pan_number:"",description:"",address:"",city:"",state:"",country:"",postal_code:"",phone_number:"",email:"",website:""}),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(null),p=a=>{let{name:b,value:c}=a.target;k(a=>({...a,[b]:c}))},q=a=>{let{name:b,files:c}=a.target;c&&c.length>0&&("logo"===b?m(c[0]):"banner"===b&&o(c[0]))},r=async c=>{c.preventDefault();let d={...j,logo:l||void 0,banner:n||void 0},e=await a((0,h.xF)(d));h.xF.fulfilled.match(e)&&b.push("/seller/dashboard")};return(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Seller Registration"}),i&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:i}),(0,d.jsxs)("form",{onSubmit:r,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{className:"col-span-2",children:(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Business Information"})}),(0,d.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Name*"}),(0,d.jsx)("input",{type:"text",name:"business_name",value:j.business_name,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Type*"}),(0,d.jsxs)("select",{name:"business_type",value:j.business_type,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"INDIVIDUAL",children:"Individual"}),(0,d.jsx)("option",{value:"PARTNERSHIP",children:"Partnership"}),(0,d.jsx)("option",{value:"LLC",children:"Limited Liability Company"}),(0,d.jsx)("option",{value:"CORPORATION",children:"Corporation"}),(0,d.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tax ID"}),(0,d.jsx)("input",{type:"text",name:"tax_id",value:j.tax_id,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"GSTIN"}),(0,d.jsx)("input",{type:"text",name:"gstin",value:j.gstin,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PAN Number"}),(0,d.jsx)("input",{type:"text",name:"pan_number",value:j.pan_number,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Description"}),(0,d.jsx)("textarea",{name:"description",value:j.description,onChange:p,rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo"}),(0,d.jsx)("input",{type:"file",name:"logo",onChange:q,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner"}),(0,d.jsx)("input",{type:"file",name:"banner",onChange:q,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsx)("div",{className:"col-span-2",children:(0,d.jsx)("h3",{className:"text-lg font-medium mb-4 mt-4",children:"Contact Information"})}),(0,d.jsxs)("div",{className:"col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address*"}),(0,d.jsx)("textarea",{name:"address",value:j.address,onChange:p,required:!0,rows:2,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City*"}),(0,d.jsx)("input",{type:"text",name:"city",value:j.city,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State*"}),(0,d.jsx)("input",{type:"text",name:"state",value:j.state,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country*"}),(0,d.jsx)("input",{type:"text",name:"country",value:j.country,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code*"}),(0,d.jsx)("input",{type:"text",name:"postal_code",value:j.postal_code,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number*"}),(0,d.jsx)("input",{type:"tel",name:"phone_number",value:j.phone_number,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email*"}),(0,d.jsx)("input",{type:"email",name:"email",value:j.email,onChange:p,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website"}),(0,d.jsx)("input",{type:"url",name:"website",value:j.website,onChange:p,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsx)("div",{className:"mt-8",children:(0,d.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:c?"Submitting...":"Register as Seller"})})]})]})};var j=c(85814),k=c.n(j);let l=()=>{let a=(0,f.wA)(),{profile:b,analytics:c,loading:g}=(0,f.d4)(a=>a.seller);return((0,e.useEffect)(()=>{a((0,h.b7)()),a((0,h.IZ)())},[a]),g)?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):b?(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h2",{className:"text-2xl font-semibold mb-2",children:["Welcome, ",b.business_name,"!"]}),(0,d.jsx)("p",{className:"text-gray-600",children:"VERIFIED"===b.verification_status?(0,d.jsx)("span",{className:"text-green-600 font-medium",children:"Your seller account is verified."}):"PENDING"===b.verification_status?(0,d.jsx)("span",{className:"text-yellow-600 font-medium",children:"Your seller account is pending verification."}):(0,d.jsx)("span",{className:"text-red-600 font-medium",children:"Your seller account verification has issues."})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Sales"}),(0,d.jsx)("div",{className:"flex items-baseline",children:(0,d.jsxs)("span",{className:"text-3xl font-semibold",children:["₹",c?.total_sales.toLocaleString()||"0"]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Orders"}),(0,d.jsx)("div",{className:"flex items-baseline",children:(0,d.jsx)("span",{className:"text-3xl font-semibold",children:c?.total_orders||"0"})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-gray-500 text-sm font-medium mb-2",children:"Total Products"}),(0,d.jsx)("div",{className:"flex items-baseline",children:(0,d.jsx)("span",{className:"text-3xl font-semibold",children:c?.total_products||"0"})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Sales Overview"}),(0,d.jsx)("div",{className:"h-64 flex items-center justify-center",children:c?.sales_by_period?(0,d.jsx)("div",{className:"w-full h-full",children:(0,d.jsx)("div",{className:"flex h-full items-end justify-between",children:c.sales_by_period.map((a,b)=>(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"bg-blue-500 w-12",style:{height:`${a.amount/Math.max(...c.sales_by_period.map(a=>a.amount))*100}%`,minHeight:"10px"}}),(0,d.jsx)("span",{className:"text-xs mt-2",children:a.period})]},b))})}):(0,d.jsx)("p",{className:"text-gray-500",children:"No sales data available"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b",children:(0,d.jsx)("h3",{className:"text-lg font-medium",children:"Recent Orders"})}),(0,d.jsx)("div",{className:"divide-y",children:c?.recent_orders&&c.recent_orders.length>0?c.recent_orders.map(a=>(0,d.jsxs)("div",{className:"px-6 py-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"font-medium",children:a.order_number}),(0,d.jsxs)("span",{className:"text-gray-600",children:["₹",a.total_amount.toLocaleString()]})]}),(0,d.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,d.jsx)("span",{className:"text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString()}),(0,d.jsx)("span",{className:`text-sm ${"DELIVERED"===a.status?"text-green-600":"CANCELLED"===a.status?"text-red-600":"text-yellow-600"}`,children:a.status})]})]},a.id)):(0,d.jsx)("div",{className:"px-6 py-4 text-center text-gray-500",children:"No recent orders"})}),(0,d.jsx)("div",{className:"px-6 py-3 bg-gray-50",children:(0,d.jsx)(k(),{href:"/seller/orders",children:(0,d.jsx)("a",{className:"text-sm text-blue-600 hover:text-blue-800",children:"View all orders"})})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b",children:(0,d.jsx)("h3",{className:"text-lg font-medium",children:"Top Products"})}),(0,d.jsx)("div",{className:"divide-y",children:c?.top_products&&c.top_products.length>0?c.top_products.map(a=>(0,d.jsxs)("div",{className:"px-6 py-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"font-medium",children:a.name}),(0,d.jsxs)("span",{className:"text-gray-600",children:["₹",a.sales.toLocaleString()]})]}),(0,d.jsx)("div",{className:"flex justify-between mt-1",children:(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:[a.quantity," units sold"]})})]},a.id)):(0,d.jsx)("div",{className:"px-6 py-4 text-center text-gray-500",children:"No product data available"})}),(0,d.jsx)("div",{className:"px-6 py-3 bg-gray-50",children:(0,d.jsx)(k(),{href:"/seller/products",children:(0,d.jsx)("a",{className:"text-sm text-blue-600 hover:text-blue-800",children:"View all products"})})})]})]}),(0,d.jsxs)("div",{className:"mt-8",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Quick Actions"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsx)(k(),{href:"/seller/products/add",children:(0,d.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Add Product"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"List a new product for sale"})]})}),(0,d.jsx)(k(),{href:"/seller/kyc",children:(0,d.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,d.jsx)("h4",{className:"font-medium",children:"KYC Verification"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Complete your verification"})]})}),(0,d.jsx)(k(),{href:"/seller/bank-accounts",children:(0,d.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Bank Accounts"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Manage your payment methods"})]})}),(0,d.jsx)(k(),{href:"/seller/profile",children:(0,d.jsxs)("a",{className:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Edit Profile"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Update your business details"})]})})]})]})]}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Seller Profile Not Found"}),(0,d.jsx)("p",{className:"mb-6",children:"You need to register as a seller to access the dashboard."}),(0,d.jsx)(k(),{href:"/seller/register",children:(0,d.jsx)("a",{className:"bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700",children:"Register as Seller"})})]})},m=()=>{let a=(0,f.wA)(),{kycDocuments:b,loading:c,error:g}=(0,f.d4)(a=>a.seller),[i,j]=(0,e.useState)({document_type:"ID_PROOF",document_number:"",document_file:{},document_name:"",issue_date:"",expiry_date:""}),[k,l]=(0,e.useState)(null),[m,n]=(0,e.useState)("");(0,e.useEffect)(()=>{a((0,h.Y0)())},[a]);let o=a=>{let{name:b,value:c}=a.target;j(a=>({...a,[b]:c}))},p=async b=>{if(b.preventDefault(),!k)return;let c={...i,document_file:k},d=await a((0,h.CO)(c));h.CO.fulfilled.match(d)&&(n("Document uploaded successfully!"),j({document_type:"ID_PROOF",document_number:"",document_file:{},document_name:"",issue_date:"",expiry_date:""}),l(null),setTimeout(()=>{n("")},3e3))};return(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"KYC Verification"}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Upload KYC Document"}),g&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:g}),m&&(0,d.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:m}),(0,d.jsxs)("form",{onSubmit:p,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Type*"}),(0,d.jsxs)("select",{name:"document_type",value:i.document_type,onChange:o,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"ID_PROOF",children:"ID Proof"}),(0,d.jsx)("option",{value:"ADDRESS_PROOF",children:"Address Proof"}),(0,d.jsx)("option",{value:"BUSINESS_PROOF",children:"Business Proof"}),(0,d.jsx)("option",{value:"TAX_DOCUMENT",children:"Tax Document"}),(0,d.jsx)("option",{value:"BANK_STATEMENT",children:"Bank Statement"}),(0,d.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Name*"}),(0,d.jsx)("input",{type:"text",name:"document_name",value:i.document_name,onChange:o,required:!0,placeholder:"e.g., Aadhar Card, PAN Card",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document Number*"}),(0,d.jsx)("input",{type:"text",name:"document_number",value:i.document_number,onChange:o,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Document File*"}),(0,d.jsx)("input",{type:"file",name:"document_file",onChange:a=>{let{files:b}=a.target;b&&b.length>0&&l(b[0])},required:!0,accept:".pdf,.jpg,.jpeg,.png",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Accepted formats: PDF, JPG, JPEG, PNG (Max size: 5MB)"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Issue Date"}),(0,d.jsx)("input",{type:"date",name:"issue_date",value:i.issue_date,onChange:o,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date"}),(0,d.jsx)("input",{type:"date",name:"expiry_date",value:i.expiry_date,onChange:o,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)("button",{type:"submit",disabled:c||!k,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:c?"Uploading...":"Upload Document"})})]})]}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Uploaded Documents"}),0===b.length?(0,d.jsx)("p",{className:"text-gray-500",children:"No documents uploaded yet."}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Type"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Name"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Number"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded On"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.document_type_display}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.document_name}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.document_number}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${(a=>{switch(a){case"VERIFIED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"REJECTED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.verification_status)}`,children:a.verification_status_display}),a.verification_notes&&(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a.verification_notes})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString()})]},a.id))})]})})]})]})},n=()=>{let a=(0,f.wA)(),{bankAccounts:b,loading:c,error:g}=(0,f.d4)(a=>a.seller),[i,j]=(0,e.useState)({account_holder_name:"",bank_name:"",account_number:"",ifsc_code:"",branch_name:"",account_type:"SAVINGS",is_primary:!1}),[k,l]=(0,e.useState)(null),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(!1);(0,e.useEffect)(()=>{a((0,h.Dz)())},[a]);let q=a=>{let{name:b,value:c,type:d}=a.target,e="checked"in a.target&&a.target.checked;j(a=>({...a,[b]:"checkbox"===d?e:c}))},r=async b=>{b.preventDefault();let c={...i,verification_document:k||void 0},d=await a((0,h.rf)(c));h.rf.fulfilled.match(d)&&(n("Bank account added successfully!"),j({account_holder_name:"",bank_name:"",account_number:"",ifsc_code:"",branch_name:"",account_type:"SAVINGS",is_primary:!1}),l(null),p(!1),setTimeout(()=>{n("")},3e3))},s=async b=>{await a((0,h.gZ)(b))};return(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Bank Accounts"}),g&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:g}),m&&(0,d.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:m}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium",children:"Your Bank Accounts"}),(0,d.jsx)("button",{onClick:()=>p(!o),className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:o?"Cancel":"Add New Account"})]}),o&&(0,d.jsxs)("form",{onSubmit:r,className:"border-t pt-4 mt-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Holder Name*"}),(0,d.jsx)("input",{type:"text",name:"account_holder_name",value:i.account_holder_name,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bank Name*"}),(0,d.jsx)("input",{type:"text",name:"bank_name",value:i.bank_name,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Number*"}),(0,d.jsx)("input",{type:"text",name:"account_number",value:i.account_number,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"IFSC Code*"}),(0,d.jsx)("input",{type:"text",name:"ifsc_code",value:i.ifsc_code,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Branch Name*"}),(0,d.jsx)("input",{type:"text",name:"branch_name",value:i.branch_name,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Type*"}),(0,d.jsxs)("select",{name:"account_type",value:i.account_type,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"SAVINGS",children:"Savings"}),(0,d.jsx)("option",{value:"CURRENT",children:"Current/Checking"}),(0,d.jsx)("option",{value:"BUSINESS",children:"Business"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Verification Document"}),(0,d.jsx)("input",{type:"file",name:"verification_document",onChange:a=>{let{files:b}=a.target;b&&b.length>0&&l(b[0])},accept:".pdf,.jpg,.jpeg,.png",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a cancelled cheque or bank statement (PDF, JPG, JPEG, PNG)"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"is_primary",id:"is_primary",checked:i.is_primary,onChange:q,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"is_primary",className:"ml-2 block text-sm text-gray-700",children:"Set as primary account"})]})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)("button",{type:"submit",disabled:c,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:c?"Adding...":"Add Bank Account"})})]}),!o&&(0,d.jsx)("div",{className:"mt-4",children:0===b.length?(0,d.jsx)("p",{className:"text-gray-500",children:"No bank accounts added yet."}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Name"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Number"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Type"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[a.is_primary&&(0,d.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2 py-0.5 rounded",children:"Primary"}),a.bank_name]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"•••• "+a.account_number.slice(-4)}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.account_type_display}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${(a=>{switch(a){case"VERIFIED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";case"REJECTED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.verification_status)}`,children:a.verification_status_display})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:!a.is_primary&&(0,d.jsx)("button",{onClick:()=>s(a.id),disabled:c||"VERIFIED"!==a.verification_status,className:"text-blue-600 hover:text-blue-900 disabled:text-gray-400",children:"Set as Primary"})})]},a.id))})]})})})]}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Important Information"}),(0,d.jsxs)("ul",{className:"list-disc pl-5 space-y-2 text-gray-700",children:[(0,d.jsx)("li",{children:"Bank accounts need to be verified before they can be used for payouts."}),(0,d.jsx)("li",{children:"Verification typically takes 1-3 business days."}),(0,d.jsx)("li",{children:"For faster verification, upload a cancelled cheque or bank statement."}),(0,d.jsx)("li",{children:"Only verified bank accounts can be set as primary."}),(0,d.jsx)("li",{children:"All payouts will be sent to your primary bank account."})]})]})]})},o=()=>{let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)("all"),[i,j]=(0,e.useState)("");(0,e.useEffect)(()=>{let a=[{id:"1",name:"Wireless Bluetooth Headphones",price:1299,stock:45,status:"active",category:"Electronics",created_at:"2023-06-15T10:30:00Z",image:"https://via.placeholder.com/50"},{id:"2",name:"Premium Cotton T-Shirt",price:499,stock:120,status:"active",category:"Clothing",created_at:"2023-06-10T14:20:00Z",image:"https://via.placeholder.com/50"},{id:"3",name:"Stainless Steel Water Bottle",price:799,stock:0,status:"out_of_stock",category:"Home & Kitchen",created_at:"2023-05-25T09:15:00Z",image:"https://via.placeholder.com/50"},{id:"4",name:"Organic Face Wash",price:349,stock:78,status:"active",category:"Beauty",created_at:"2023-06-05T11:45:00Z",image:"https://via.placeholder.com/50"},{id:"5",name:"Fitness Tracker Watch",price:2499,stock:15,status:"active",category:"Electronics",created_at:"2023-06-12T16:30:00Z",image:"https://via.placeholder.com/50"}];f(!0),setTimeout(()=>{b(a),f(!1)},500)},[]);let l=a.filter(a=>{let b="all"===g||a.status===g,c=a.name.toLowerCase().includes(i.toLowerCase())||a.category.toLowerCase().includes(i.toLowerCase());return b&&c});return(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold",children:"Product Management"}),(0,d.jsx)(k(),{href:"/seller/products/add",children:(0,d.jsx)("a",{className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Add New Product"})})]}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>h("all"),className:`px-3 py-1 rounded-md ${"all"===g?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:"All Products"}),(0,d.jsx)("button",{onClick:()=>h("active"),className:`px-3 py-1 rounded-md ${"active"===g?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:"Active"}),(0,d.jsx)("button",{onClick:()=>h("out_of_stock"),className:`px-3 py-1 rounded-md ${"out_of_stock"===g?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:"Out of Stock"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",placeholder:"Search products...",value:i,onChange:a=>j(a.target.value),className:"w-full md:w-64 border border-gray-300 rounded-md pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),c?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===l.length?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsx)("p",{className:"text-gray-500",children:"No products found."})}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,d.jsx)("img",{className:"h-10 w-10 rounded-full",src:a.image,alt:a.name})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",a.id]})]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",a.price.toLocaleString()]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm text-gray-900",children:a.stock})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm text-gray-900",children:a.category})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===a.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===a.status?"Active":"Out of Stock"})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,d.jsx)(k(),{href:a.id?`/seller/products/edit/${a.id}`:"/seller/products",children:(0,d.jsx)("a",{className:"text-blue-600 hover:text-blue-900 mr-4",children:"Edit"})}),(0,d.jsx)("button",{className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},a.id))})]})})]})]})},p=()=>{let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)("all"),[i,j]=(0,e.useState)("");(0,e.useEffect)(()=>{let a=[{id:"1",order_number:"ORD-12345",customer_name:"Rahul Sharma",total_amount:2499,items_count:2,status:"DELIVERED",payment_status:"COMPLETED",created_at:"2023-06-15T10:30:00Z"},{id:"2",order_number:"ORD-12346",customer_name:"Priya Patel",total_amount:1299,items_count:1,status:"PROCESSING",payment_status:"COMPLETED",created_at:"2023-06-16T14:20:00Z"},{id:"3",order_number:"ORD-12347",customer_name:"Amit Kumar",total_amount:3499,items_count:3,status:"SHIPPED",payment_status:"COMPLETED",created_at:"2023-06-14T09:15:00Z"},{id:"4",order_number:"ORD-12348",customer_name:"Sneha Gupta",total_amount:799,items_count:1,status:"PENDING",payment_status:"PENDING",created_at:"2023-06-17T11:45:00Z"},{id:"5",order_number:"ORD-12349",customer_name:"Vikram Singh",total_amount:4999,items_count:2,status:"CANCELLED",payment_status:"REFUNDED",created_at:"2023-06-13T16:30:00Z"}];f(!0),setTimeout(()=>{b(a),f(!1)},500)},[]);let l=a.filter(a=>{let b="all"===g||a.status===g,c=a.order_number.toLowerCase().includes(i.toLowerCase())||a.customer_name.toLowerCase().includes(i.toLowerCase());return b&&c});return(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Order Management"}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2 md:pb-0",children:[(0,d.jsx)("button",{onClick:()=>h("all"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"all"===g?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:"All Orders"}),(0,d.jsx)("button",{onClick:()=>h("PENDING"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"PENDING"===g?"bg-gray-100 text-gray-800 border-2 border-gray-300":"bg-gray-100 text-gray-800"}`,children:"Pending"}),(0,d.jsx)("button",{onClick:()=>h("PROCESSING"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"PROCESSING"===g?"bg-yellow-100 text-yellow-800 border-2 border-yellow-300":"bg-gray-100 text-gray-800"}`,children:"Processing"}),(0,d.jsx)("button",{onClick:()=>h("SHIPPED"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"SHIPPED"===g?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-800"}`,children:"Shipped"}),(0,d.jsx)("button",{onClick:()=>h("DELIVERED"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"DELIVERED"===g?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-800"}`,children:"Delivered"}),(0,d.jsx)("button",{onClick:()=>h("CANCELLED"),className:`px-3 py-1 rounded-md whitespace-nowrap ${"CANCELLED"===g?"bg-red-100 text-red-800 border-2 border-red-300":"bg-gray-100 text-gray-800"}`,children:"Cancelled"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",placeholder:"Search orders...",value:i,onChange:a=>j(a.target.value),className:"w-full md:w-64 border border-gray-300 rounded-md pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),c?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===l.length?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsx)("p",{className:"text-gray-500",children:"No orders found."})}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.order_number}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:[a.items_count," items"]})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm text-gray-900",children:a.customer_name})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",a.total_amount.toLocaleString()]}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a.payment_status})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${(a=>{switch(a){case"DELIVERED":return"bg-green-100 text-green-800";case"PROCESSING":return"bg-yellow-100 text-yellow-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"PENDING":default:return"bg-gray-100 text-gray-800";case"CANCELLED":return"bg-red-100 text-red-800"}})(a.status)}`,children:a.status})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString()}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsx)(k(),{href:a.id?`/seller/orders/${a.id}`:"/seller/orders",children:(0,d.jsx)("a",{className:"text-blue-600 hover:text-blue-900",children:"View Details"})})})]},a.id))})]})})]})]})},q=()=>{let a=(0,f.wA)(),{profile:b,loading:c,error:g}=(0,f.d4)(a=>a.seller),[i,j]=(0,e.useState)({business_name:"",business_type:"",tax_id:"",gstin:"",pan_number:"",description:"",address:"",city:"",state:"",country:"",postal_code:"",phone_number:"",email:"",website:""}),[k,l]=(0,e.useState)(null),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)("");(0,e.useEffect)(()=>{a((0,h.b7)())},[a]),(0,e.useEffect)(()=>{b&&j({business_name:b.business_name,business_type:b.business_type,tax_id:b.tax_id,gstin:b.gstin,pan_number:b.pan_number,description:b.description,address:b.address,city:b.city,state:b.state,country:b.country,postal_code:b.postal_code,phone_number:b.phone_number,email:b.email,website:b.website})},[b]);let q=a=>{let{name:b,value:c}=a.target;j(a=>({...a,[b]:c}))},r=a=>{let{name:b,files:c}=a.target;c&&c.length>0&&("logo"===b?l(c[0]):"banner"===b&&n(c[0]))},s=async b=>{b.preventDefault();let c={...i,logo:k?URL.createObjectURL(k):void 0,banner:m?URL.createObjectURL(m):void 0},d=await a((0,h.a4)(c));h.a4.fulfilled.match(d)&&(p("Profile updated successfully!"),setTimeout(()=>{p("")},3e3))};return c&&!b?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):b?(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Seller Profile"}),g&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:g}),o&&(0,d.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:o}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center mb-8 space-y-4 md:space-y-0 md:space-x-6",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:b.logo?(0,d.jsx)("img",{src:b.logo,alt:b.business_name,className:"h-24 w-24 rounded-full object-cover border-2 border-gray-200"}):(0,d.jsx)("div",{className:"h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-500 text-2xl font-semibold",children:b.business_name.charAt(0)})})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-semibold",children:b.business_name}),(0,d.jsxs)("div",{className:"flex items-center mt-1",children:[(0,d.jsx)("span",{className:`inline-block h-3 w-3 rounded-full mr-2 ${"VERIFIED"===b.verification_status?"bg-green-500":"PENDING"===b.verification_status?"bg-yellow-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:b.verification_status_display})]}),(0,d.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Joined on ",new Date(b.created_at).toLocaleDateString()]})]})]}),(0,d.jsxs)("form",{onSubmit:s,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{className:"col-span-2",children:(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Business Information"})}),(0,d.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Name*"}),(0,d.jsx)("input",{type:"text",name:"business_name",value:i.business_name,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"col-span-2 md:col-span-1",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Type*"}),(0,d.jsxs)("select",{name:"business_type",value:i.business_type,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"INDIVIDUAL",children:"Individual"}),(0,d.jsx)("option",{value:"PARTNERSHIP",children:"Partnership"}),(0,d.jsx)("option",{value:"LLC",children:"Limited Liability Company"}),(0,d.jsx)("option",{value:"CORPORATION",children:"Corporation"}),(0,d.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tax ID"}),(0,d.jsx)("input",{type:"text",name:"tax_id",value:i.tax_id,onChange:q,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"GSTIN"}),(0,d.jsx)("input",{type:"text",name:"gstin",value:i.gstin,onChange:q,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PAN Number"}),(0,d.jsx)("input",{type:"text",name:"pan_number",value:i.pan_number,onChange:q,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Business Description"}),(0,d.jsx)("textarea",{name:"description",value:i.description,onChange:q,rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo"}),b.logo&&(0,d.jsx)("div",{className:"mb-2",children:(0,d.jsx)("img",{src:b.logo,alt:"Current Logo",className:"h-12 w-12 object-cover rounded-md"})}),(0,d.jsx)("input",{type:"file",name:"logo",onChange:r,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner"}),b.banner&&(0,d.jsx)("div",{className:"mb-2",children:(0,d.jsx)("img",{src:b.banner,alt:"Current Banner",className:"h-12 w-32 object-cover rounded-md"})}),(0,d.jsx)("input",{type:"file",name:"banner",onChange:r,accept:"image/*",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsx)("div",{className:"col-span-2",children:(0,d.jsx)("h3",{className:"text-lg font-medium mb-4 mt-4",children:"Contact Information"})}),(0,d.jsxs)("div",{className:"col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address*"}),(0,d.jsx)("textarea",{name:"address",value:i.address,onChange:q,required:!0,rows:2,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City*"}),(0,d.jsx)("input",{type:"text",name:"city",value:i.city,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State*"}),(0,d.jsx)("input",{type:"text",name:"state",value:i.state,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country*"}),(0,d.jsx)("input",{type:"text",name:"country",value:i.country,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code*"}),(0,d.jsx)("input",{type:"text",name:"postal_code",value:i.postal_code,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number*"}),(0,d.jsx)("input",{type:"tel",name:"phone_number",value:i.phone_number,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email*"}),(0,d.jsx)("input",{type:"email",name:"email",value:i.email,onChange:q,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website"}),(0,d.jsx)("input",{type:"url",name:"website",value:i.website,onChange:q,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsx)("div",{className:"mt-8",children:(0,d.jsx)("button",{type:"submit",disabled:c,className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300",children:c?"Saving...":"Save Changes"})})]})]})]}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Seller Profile Not Found"}),(0,d.jsx)("p",{children:"You need to register as a seller first."})]})},r=()=>{let a=(0,f.wA)(),{payouts:b,loading:c,error:g}=(0,f.d4)(a=>a.seller);return(0,e.useEffect)(()=>{a((0,h.Ty)())},[a]),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Payout History"}),g&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:g}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,d.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,d.jsx)("h3",{className:"text-lg font-medium",children:"Your Payouts"})}),c?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===b.length?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsx)("p",{className:"text-gray-500",children:"No payout history available."})}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction ID"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fee"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.transaction_id})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",a.amount.toLocaleString()]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-gray-900",children:["₹",a.transaction_fee.toLocaleString()]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${(a=>{switch(a){case"COMPLETED":return"bg-green-100 text-green-800";case"PROCESSING":return"bg-yellow-100 text-yellow-800";case"PENDING":default:return"bg-gray-100 text-gray-800";case"FAILED":return"bg-red-100 text-red-800"}})(a.status)}`,children:a.status_display})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.payout_date).toLocaleDateString()})]},a.id))})]})})]}),(0,d.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mt-8",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Payout Information"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Payout Schedule"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Payouts are processed every 7 days"})]}),(0,d.jsx)("div",{className:"mt-2 md:mt-0",children:(0,d.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"Weekly"})})]}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Transaction Fee"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Fee charged on each payout"})]}),(0,d.jsx)("div",{className:"mt-2 md:mt-0",children:(0,d.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"2%"})})]}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Minimum Payout Amount"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Minimum balance required for payout"})]}),(0,d.jsx)("div",{className:"mt-2 md:mt-0",children:(0,d.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:"₹1,000"})})]})]})]})]})}},13312:(a,b,c)=>{"use strict";c.d(b,{SellerLayout:()=>u});var d=c(60687);c(43210);var e=c(85814),f=c.n(e),g=c(16189),h=c(54864),i=c(46266),j=c(79437),k=c(24125),l=c(32192),m=c(19080),n=c(28561),o=c(62688);let p=(0,o.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),q=(0,o.A)("file-check",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]),r=(0,o.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var s=c(23928);let t=[{name:"Dashboard",path:k.B0.DASHBOARD,icon:l.A},{name:"Products",path:k.B0.PRODUCTS,icon:m.A},{name:"Orders",path:k.B0.ORDERS,icon:n.A},{name:"Profile",path:k.B0.PROFILE,icon:p},{name:"KYC Verification",path:k.B0.KYC,icon:q},{name:"Bank Accounts",path:k.B0.BANK_ACCOUNTS,icon:r},{name:"Payouts",path:k.B0.PAYOUTS,icon:s.A}];function u({children:a}){let b=(0,g.usePathname)(),{profile:c}=(0,h.d4)(a=>a.seller),e=(0,j.s)(b);return(0,d.jsxs)("div",{className:"flex min-h-screen bg-gray-100",children:[(0,d.jsxs)("div",{className:"w-64 bg-white shadow-md",children:[(0,d.jsxs)("div",{className:"p-4 border-b",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"Seller Panel"}),c&&(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:c.business_name})]}),(0,d.jsx)("nav",{className:"mt-4",children:(0,d.jsx)("ul",{children:t.map(a=>(0,d.jsx)("li",{children:(0,d.jsxs)(f(),{href:a.path||"/seller",className:`flex items-center px-4 py-2 ${b===a.path?"bg-blue-500 text-white":"text-gray-700 hover:bg-gray-100"}`,children:[(0,d.jsx)(a.icon,{className:`mr-2 h-5 w-5 ${b===a.path?"text-white":"text-gray-500"}`}),a.name]})},a.path))})})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center",children:[(0,d.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:t.find(a=>b===a.path)?.name||"Seller Panel"}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[c&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:`inline-block h-3 w-3 rounded-full mr-2 ${"VERIFIED"===c.verification_status?"bg-green-500":"PENDING"===c.verification_status?"bg-yellow-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:c.verification_status_display})]}),(0,d.jsx)(f(),{href:"/",className:"text-sm text-blue-600 hover:text-blue-800",children:"Back to Store"})]})]})}),(0,d.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(i.Q,{items:e})}),a]})]})]})}},19080:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},23928:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27345:(a,b,c)=>{Promise.resolve().then(c.bind(c,13312))},28017:(a,b,c)=>{Promise.resolve().then(c.bind(c,73914))},28561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},32192:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},42075:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413);c(61120);var e=c(73914);function f({children:a}){return(0,d.jsx)(e.SellerLayout,{children:a})}},46266:(a,b,c)=>{"use strict";c.d(b,{Q:()=>h});var d=c(60687);c(43210);var e=c(85814),f=c.n(e);let g=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function h({items:a}){return a&&0!==a.length?(0,d.jsx)("nav",{"aria-label":"Breadcrumb",className:"py-2",children:(0,d.jsx)("ol",{className:"flex items-center space-x-1 text-sm",children:a.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-center",children:[b>0&&(0,d.jsx)(g,{className:"h-4 w-4 text-gray-400 mx-1","aria-hidden":"true"}),a.isCurrent?(0,d.jsx)("span",{className:"font-medium text-gray-700","aria-current":"page",children:a.label}):(0,d.jsx)(f(),{href:a.href||"/",className:"text-gray-500 hover:text-gray-700 hover:underline",children:a.label})]},a.href))})}):null}},62688:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},73914:(a,b,c)=>{"use strict";c.d(b,{SellerLayout:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call SellerLayout() from the server but SellerLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\components\\layout\\SellerLayout.tsx","SellerLayout")},79437:(a,b,c)=>{"use strict";c.d(b,{s:()=>e});var d=c(24125);function e(a){let b=[{label:"Home",href:"/"}];if("/"===a)return b[0].isCurrent=!0,b;let c=a.split("/").filter(Boolean),e="";return c.forEach((a,f)=>{var g,h;e+=`/${a}`;let i=(g=a,h=e,d.Ss[h]?d.Ss[h]:g.startsWith("[")&&g.endsWith("]")?g.replace(/^\[|\]$/g,"").replace(/-/g," "):g.charAt(0).toUpperCase()+g.slice(1).replace(/-/g," "));b.push({label:i,href:e,isCurrent:f===c.length-1})}),b}}};