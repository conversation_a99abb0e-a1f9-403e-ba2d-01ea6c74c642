# Staging Environment Configuration for MySQL Database Integration

# Database Configuration
DB_ENGINE=django.db.backends.mysql
DB_NAME=ecommerce_staging
DB_USER=ecommerce_staging_user
DB_PASSWORD=
DB_HOST=mysql-staging.internal
DB_PORT=3306

# Read Replica Configuration (Optional for staging)
DB_READ_HOST=mysql-staging.internal
DB_READ_USER=ecommerce_staging_user
DB_READ_PASSWORD=

# SSL Configuration
DB_SSL_CA=/etc/mysql/ssl/ca-cert.pem
DB_SSL_CERT=/etc/mysql/ssl/client-cert.pem
DB_SSL_KEY=/etc/mysql/ssl/client-key.pem

# Connection Pool Settings
DB_CONN_MAX_AGE=1800
DB_CONN_HEALTH_CHECKS=true
DB_POOL_SIZE=10
DB_POOL_MAX_OVERFLOW=15
DB_POOL_RECYCLE=1800
DB_POOL_PRE_PING=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE_FULL=0 3 * * *
BACKUP_SCHEDULE_INCREMENTAL=0 */6 * * *
BACKUP_RETENTION_DAYS=14
BACKUP_ENCRYPTION_KEY=
BACKUP_STORAGE_PATH=/var/backups/mysql
BACKUP_S3_BUCKET=ecommerce-db-backups-staging
BACKUP_S3_REGION=us-east-1

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_INTERVAL=120
MONITORING_ALERT_EMAIL=<EMAIL>
MONITORING_SLACK_WEBHOOK=

# Performance Thresholds (More relaxed for staging)
ALERT_CPU_THRESHOLD=85
ALERT_MEMORY_THRESHOLD=90
ALERT_DISK_THRESHOLD=95
ALERT_CONNECTION_THRESHOLD=85
ALERT_SLOW_QUERY_THRESHOLD=10
ALERT_REPLICATION_LAG_THRESHOLD=600

# Security Configuration
SECURITY_AUDIT_ENABLED=true
SECURITY_ENCRYPTION_ENABLED=false
SECURITY_SSL_REQUIRED=false
SECURITY_FAILED_LOGIN_THRESHOLD=10
SECURITY_LOCKOUT_DURATION=180

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_SLOW_QUERIES=true
LOG_GENERAL_QUERIES=true
LOG_RETENTION_DAYS=14
LOG_MAX_SIZE=50M

# Replication Configuration (Disabled for staging)
REPLICATION_ENABLED=false
REPLICATION_USER=replication
REPLICATION_PASSWORD=
REPLICATION_BINLOG_FORMAT=ROW
REPLICATION_EXPIRE_LOGS_DAYS=3

# Maintenance Configuration
MAINTENANCE_ENABLED=true
MAINTENANCE_SCHEDULE=0 4 * * 0
MAINTENANCE_OPTIMIZE_TABLES=true
MAINTENANCE_ANALYZE_TABLES=true
MAINTENANCE_CLEANUP_LOGS=true

# Application Configuration
DJANGO_SETTINGS_MODULE=ecommerce_project.settings.staging
DEBUG=true
ALLOWED_HOSTS=staging.ecommerce.com,staging-api.ecommerce.com
SECRET_KEY=

# Cache Configuration
CACHE_BACKEND=redis
CACHE_LOCATION=redis://redis-staging.internal:6379/1
CACHE_TIMEOUT=1800

# Celery Configuration
CELERY_BROKER_URL=redis://redis-staging.internal:6379/0
CELERY_RESULT_BACKEND=redis://redis-staging.internal:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=UTC

# Email Configuration (Use test email service)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=
EMAIL_USE_TLS=false
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# Static Files Configuration
STATIC_URL=/static/
STATIC_ROOT=/var/www/ecommerce-staging/static/
MEDIA_URL=/media/
MEDIA_ROOT=/var/www/ecommerce-staging/media/

# AWS Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=ecommerce-static-staging
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=staging-cdn.ecommerce.com

# Sentry Configuration
SENTRY_DSN=
SENTRY_ENVIRONMENT=staging
SENTRY_TRACES_SAMPLE_RATE=0.5