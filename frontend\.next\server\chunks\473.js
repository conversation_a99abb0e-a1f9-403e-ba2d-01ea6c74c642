exports.id=473,exports.ids=[473],exports.modules={9776:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687);let e={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"};function f({size:a="md",className:b=""}){return(0,d.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${e[a]} ${b}`})}},11860:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19080:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},24853:(a,b,c)=>{Promise.resolve().then(c.bind(c,72701))},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32192:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},35029:(a,b,c)=>{"use strict";c.d(b,{i:()=>f});var d=c(17327);let e="/api/analytics",f={async getDashboardMetrics(a,b){let c=new URLSearchParams;a&&c.append("date_from",a),b&&c.append("date_to",b);let f=await d.uE.get(`${e}/dashboard_metrics/?${c.toString()}`);if(!f.success||!f.data)throw Error(f.error?.message||"Failed to fetch dashboard metrics");return f.data},async getSalesReport(a){let b=new URLSearchParams;a&&Object.entries(a).forEach(([a,c])=>{void 0!==c&&b.append(a,c.toString())});let c=await d.uE.get(`${e}/sales_report/?${b.toString()}`);if(!c.success||!c.data)throw Error(c.error?.message||"Failed to fetch sales report");return c.data},async getTopSellingProducts(a,b,c=10){let f=new URLSearchParams;a&&f.append("date_from",a),b&&f.append("date_to",b),f.append("limit",c.toString());let g=await d.uE.get(`${e}/top_selling_products/?${f.toString()}`);if(!g.success||!g.data)throw Error(g.error?.message||"Failed to fetch top selling products");return g.data},async getCustomerAnalyticsSummary(){let a=await d.uE.get(`${e}/customer_analytics_summary/`);if(!a.success||!a.data)throw Error(a.error?.message||"Failed to fetch customer analytics summary");return a.data},async getStockMaintenanceReport(){let a=await d.uE.get(`${e}/stock_maintenance_report/`);if(!a.success||!a.data)throw Error(a.error?.message||"Failed to fetch stock maintenance report");return a.data},async getSystemHealth(a=24){let b=await d.uE.get(`${e}/system_health/?hours=${a}`);if(!b.success||!b.data)throw Error(b.error?.message||"Failed to fetch system health");return b.data},async getContentAnalytics(a,b){let c=new URLSearchParams;a&&c.append("date_from",a),b&&c.append("date_to",b);let e=await d.uE.get(`/api/content/content_management/performance_summary/?${c.toString()}`);if(!e.success||!e.data)throw Error(e.error?.message||"Failed to fetch content analytics");return e.data},async exportReport(a){let b=await d.uE.post(`${e}/export_report/`,a);if(!b.success||!b.data)throw Error(b.error?.message||"Failed to export report");return b.data},async getExportHistory(){let a=await d.uE.get(`${e}/export_history/`);if(!a.success||!a.data)throw Error(a.error?.message||"Failed to fetch export history");return a.data},async downloadExport(a){let b=await d.uE.get(`${e}/download_export/?export_id=${a}`,{responseType:"blob"});if(!b.success||!b.data)throw Error(b.error?.message||"Failed to download export");let c=window.URL.createObjectURL(new Blob([b.data])),f=document.createElement("a");f.href=c,f.setAttribute("download",`report_${a}.csv`),document.body.appendChild(f),f.click(),f.remove(),window.URL.revokeObjectURL(c)},async generateDailyReports(a){let b=await d.uE.post(`${e}/generate_daily_reports/`,a?{date:a}:{});if(!b.success||!b.data)throw Error(b.error?.message||"Failed to generate daily reports");return b.data}}},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41953:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687),e=c(43210);c(35029);var f=c(9776);function g({dateRange:a}){let[b,c]=(0,e.useState)(null),[g,h]=(0,e.useState)(!0);if(g)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)(f.A,{})});if(!b||!b.daily_breakdown.length)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No sales data available for the selected period"});let i=Math.max(...b.daily_breakdown.map(a=>a.revenue));return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"h-64 flex items-end space-x-2 overflow-x-auto",children:b.daily_breakdown.map((a,b)=>{let c=a.revenue/i*100;return(0,d.jsx)("div",{className:"flex flex-col items-center min-w-0 flex-1",children:(0,d.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-8 bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600",style:{height:`${c}%`},title:`${a.day}: $${a.revenue.toLocaleString()}`}),(0,d.jsx)("div",{className:"text-xs text-gray-500 mt-2 transform -rotate-45 origin-left",children:new Date(a.day).toLocaleDateString("en-US",{month:"short",day:"numeric"})})]})},b)})}),(0,d.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,d.jsxs)("span",{children:["Revenue: $",b.summary.total_revenue.toLocaleString()]}),(0,d.jsxs)("span",{children:["Orders: ",b.summary.total_orders.toLocaleString()]}),(0,d.jsxs)("span",{children:["Avg: $",b.summary.average_order_value.toFixed(2)]})]})]})}},46266:(a,b,c)=>{"use strict";c.d(b,{Q:()=>h});var d=c(60687);c(43210);var e=c(85814),f=c.n(e);let g=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function h({items:a}){return a&&0!==a.length?(0,d.jsx)("nav",{"aria-label":"Breadcrumb",className:"py-2",children:(0,d.jsx)("ol",{className:"flex items-center space-x-1 text-sm",children:a.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-center",children:[b>0&&(0,d.jsx)(g,{className:"h-4 w-4 text-gray-400 mx-1","aria-hidden":"true"}),a.isCurrent?(0,d.jsx)("span",{className:"font-medium text-gray-700","aria-current":"page",children:a.label}):(0,d.jsx)(f(),{href:a.href||"/",className:"text-gray-500 hover:text-gray-700 hover:underline",children:a.label})]},a.href))})}):null}},53504:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687);let e=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function f({from:a,to:b,onChange:c}){let f=a=>{let b=new Date;c(new Date(b.getTime()-24*a*36e5).toISOString().split("T")[0],b.toISOString().split("T")[0])};return(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("button",{onClick:()=>f(7),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"7D"}),(0,d.jsx)("button",{onClick:()=>f(30),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"30D"}),(0,d.jsx)("button",{onClick:()=>f(90),className:"px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"90D"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 border border-gray-300 rounded-md px-3 py-2 bg-white",children:[(0,d.jsx)(e,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"date",value:a,onChange:a=>{c(a.target.value,b)},className:"text-sm border-none outline-none bg-transparent"}),(0,d.jsx)("span",{className:"text-gray-400",children:"to"}),(0,d.jsx)("input",{type:"date",value:b,onChange:b=>{c(a,b.target.value)},className:"text-sm border-none outline-none bg-transparent"})]})]})}},58559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},62688:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},72701:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>B});var d=c(60687),e=c(43210),f=c(83033),g=c(85814),h=c.n(g),i=c(16189),j=c(32192),k=c(62688);let l=(0,k.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var m=c(28561),n=c(19080),o=c(41312);let p=(0,k.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),q=(0,k.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var r=c(58559);let s=(0,k.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),t=(0,k.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=c(11860);let v=(0,k.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var w=c(46266),x=c(79437),y=c(24125);let z=[{name:"Dashboard",href:y.F$.DASHBOARD,icon:j.A},{name:"Analytics",href:y.F$.ANALYTICS,icon:l},{name:"Orders",href:y.F$.ORDERS,icon:m.A},{name:"Products",href:y.F$.PRODUCTS,icon:n.A},{name:"Customers",href:y.F$.CUSTOMERS,icon:o.A},{name:"Content",href:y.F$.CONTENT,icon:p},{name:"Reports",href:y.F$.REPORTS,icon:q},{name:"System Health",href:y.F$.SYSTEM,icon:r.A},{name:"Notifications",href:y.F$.NOTIFICATIONS,icon:s},{name:"Settings",href:y.F$.SETTINGS,icon:t}];function A({children:a}){let[b,c]=(0,e.useState)(!1),f=(0,i.usePathname)(),g=(0,x.s)(f);return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsxs)("div",{className:`fixed inset-0 z-40 lg:hidden ${b?"":"hidden"}`,children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>c(!1)}),(0,d.jsxs)("div",{className:"relative flex w-full max-w-xs flex-1 flex-col bg-white",children:[(0,d.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,d.jsx)("button",{type:"button",className:"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>c(!1),children:(0,d.jsx)(u.A,{className:"h-6 w-6 text-white"})})}),(0,d.jsxs)("div",{className:"h-0 flex-1 overflow-y-auto pt-5 pb-4",children:[(0,d.jsx)("div",{className:"flex flex-shrink-0 items-center px-4",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Admin Panel"})}),(0,d.jsx)("nav",{className:"mt-5 space-y-1 px-2",children:z.map(a=>{let b=f===a.href;return(0,d.jsxs)(h(),{href:a.href||"/admin",className:`group flex items-center px-2 py-2 text-base font-medium rounded-md ${b?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>c(!1),children:[(0,d.jsx)(a.icon,{className:`mr-4 h-6 w-6 flex-shrink-0 ${b?"text-blue-500":"text-gray-400 group-hover:text-gray-500"}`}),a.name]},a.name)})})]})]})]}),(0,d.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,d.jsx)("div",{className:"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200",children:(0,d.jsxs)("div",{className:"flex flex-1 flex-col overflow-y-auto pt-5 pb-4",children:[(0,d.jsx)("div",{className:"flex flex-shrink-0 items-center px-4",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Admin Panel"})}),(0,d.jsx)("nav",{className:"mt-5 flex-1 space-y-1 px-2",children:z.map(a=>{let b=f===a.href;return(0,d.jsxs)(h(),{href:a.href||"/admin",className:`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${b?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,d.jsx)(a.icon,{className:`mr-3 h-5 w-5 flex-shrink-0 ${b?"text-blue-500":"text-gray-400 group-hover:text-gray-500"}`}),a.name]},a.name)})})]})})}),(0,d.jsxs)("div",{className:"lg:pl-64",children:[(0,d.jsx)("div",{className:"sticky top-0 z-10 bg-white border-b border-gray-200 pl-1 pt-1 sm:pl-3 sm:pt-3 lg:hidden",children:(0,d.jsx)("button",{type:"button",className:"-ml-0.5 -mt-0.5 inline-flex h-12 w-12 items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>c(!0),children:(0,d.jsx)(v,{className:"h-6 w-6"})})}),(0,d.jsx)("main",{className:"flex-1",children:(0,d.jsx)("div",{className:"py-6",children:(0,d.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(w.Q,{items:g})}),a]})})})]})]})}function B({children:a}){return(0,d.jsx)(f.OV,{allowedUserTypes:["admin"],children:(0,d.jsx)(A,{children:a})})}},79437:(a,b,c)=>{"use strict";c.d(b,{s:()=>e});var d=c(24125);function e(a){let b=[{label:"Home",href:"/"}];if("/"===a)return b[0].isCurrent=!0,b;let c=a.split("/").filter(Boolean),e="";return c.forEach((a,f)=>{var g,h;e+=`/${a}`;let i=(g=a,h=e,d.Ss[h]?d.Ss[h]:g.startsWith("[")&&g.endsWith("]")?g.replace(/^\[|\]$/g,"").replace(/-/g," "):g.charAt(0).toUpperCase()+g.slice(1).replace(/-/g," "));b.push({label:i,href:e,isCurrent:f===c.length-1})}),b}},82997:(a,b,c)=>{Promise.resolve().then(c.bind(c,99111))},93409:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210);c(35029);var f=c(9776);let g={new:"#3b82f6",active:"#10b981",at_risk:"#f59e0b",dormant:"#6b7280",churned:"#ef4444",vip:"#8b5cf6"},h={new:"New",active:"Active",at_risk:"At Risk",dormant:"Dormant",churned:"Churned",vip:"VIP"};function i(){let[a,b]=(0,e.useState)(null),[c,i]=(0,e.useState)(!0);if(c)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)(f.A,{})});if(!a||!a.lifecycle_distribution.length)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No customer lifecycle data available"});let j=a.lifecycle_distribution.reduce((a,b)=>a+b.count,0),k=Math.max(...a.lifecycle_distribution.map(a=>a.count));return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"space-y-3",children:a.lifecycle_distribution.map(a=>{let b=j>0?a.count/j*100:0,c=k>0?a.count/k*100:0;return(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{className:"font-medium text-gray-700",children:h[a.lifecycle_stage]||a.lifecycle_stage}),(0,d.jsxs)("span",{className:"text-gray-500",children:[a.count," (",b.toFixed(1),"%)"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,d.jsx)("div",{className:"h-3 rounded-full transition-all duration-300",style:{width:`${c}%`,backgroundColor:g[a.lifecycle_stage]||"#6b7280"}})})]},a.lifecycle_stage)})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:j}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Total Customers"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.lifecycle_distribution.find(a=>"active"===a.lifecycle_stage)?.count||0}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Active Customers"})]})]}),a.top_customers.length>0&&(0,d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Top Customers by Lifetime Value"}),(0,d.jsx)("div",{className:"space-y-2",children:a.top_customers.slice(0,5).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-gray-400",children:["#",b+1]}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.email}),(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${"vip"===a.lifecycle_stage?"bg-purple-100 text-purple-800":"active"===a.lifecycle_stage?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:h[a.lifecycle_stage]||a.lifecycle_stage})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"font-medium text-gray-900",children:["$",a.lifetime_value.toLocaleString()]}),(0,d.jsxs)("div",{className:"text-gray-500",children:[a.total_orders," orders"]})]})]},a.customer_id))})]})]})}},99111:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Local_ecom\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\app\\admin\\layout.tsx","default")}};