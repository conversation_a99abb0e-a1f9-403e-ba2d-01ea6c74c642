(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{1050:(e,s,t)=>{"use strict";t.d(s,{q:()=>i});var a=t(5155),r=t(6874),l=t.n(r),n=t(2115),c=t(3741);function o(e){let{product:s}=e,[t,r]=(0,n.useState)(!1),[o,i]=(0,n.useState)(!1),d=s.originalPrice?Math.round((s.originalPrice-s.price)/s.originalPrice*100):s.discount||0,u=e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",maximumFractionDigits:0}).format(e);return(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg hover:shadow-lg transition-shadow duration-200 group",children:[(0,a.jsxs)(l(),{href:s.id?"/products/".concat(s.id):"/products",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),r(!t)},className:"absolute top-2 right-2 z-10 p-2 rounded-full bg-white shadow-md hover:bg-gray-50 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5 ".concat(t?"text-red-500 fill-current":"text-gray-400"),fill:t?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),d>0&&(0,a.jsxs)("div",{className:"absolute top-2 left-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded",children:[d,"% off"]}),(0,a.jsx)("div",{className:"aspect-square p-4 flex items-center justify-center bg-gray-50",children:o?(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,a.jsx)("svg",{className:"w-16 h-16",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}):(0,a.jsx)("img",{src:s.image,alt:s.name,className:"w-full h-full object-contain group-hover:scale-105 transition-transform duration-200",onError:()=>i(!0)})})]}),(0,a.jsxs)("div",{className:"p-4",children:[s.brand&&(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:s.brand}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors",children:s.name}),s.features&&s.features.length>0&&(0,a.jsx)("ul",{className:"text-xs text-gray-600 mb-2 space-y-1",children:s.features.slice(0,2).map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-1 h-1 bg-gray-400 rounded-full mr-2"}),e]},s))}),(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("div",{className:"flex items-center mr-2",children:(e=>{let s=[],t=Math.floor(e);for(let e=0;e<t;e++)s.push((0,a.jsx)("svg",{className:"w-4 h-4 text-yellow-400 fill-current",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})},e));e%1!=0&&s.push((0,a.jsxs)("svg",{className:"w-4 h-4 text-yellow-400",viewBox:"0 0 20 20",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"half-fill",children:[(0,a.jsx)("stop",{offset:"50%",stopColor:"currentColor"}),(0,a.jsx)("stop",{offset:"50%",stopColor:"transparent"})]})}),(0,a.jsx)("path",{fill:"url(#half-fill)",d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})]},"half"));let r=5-Math.ceil(e);for(let e=0;e<r;e++)s.push((0,a.jsx)("svg",{className:"w-4 h-4 text-gray-300",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"})},"empty-".concat(e)));return s})(s.rating)}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["(",s.reviewCount.toLocaleString(),")"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:u(s.price)}),s.originalPrice&&(0,a.jsx)("span",{className:"text-sm text-gray-500 line-through",children:u(s.originalPrice)}),d>0&&(0,a.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[d,"% off"]})]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[s.freeDelivery&&(0,a.jsxs)("div",{className:"flex items-center text-xs text-green-600",children:[(0,a.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Free Delivery"]}),s.exchangeOffer&&(0,a.jsxs)("div",{className:"flex items-center text-xs text-blue-600",children:[(0,a.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})}),"Exchange Offer"]})]})]})]}),(0,a.jsx)("div",{className:"px-4 pb-4",children:(0,a.jsx)(c.$,{size:"sm",className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium",onClick:e=>{e.preventDefault(),console.log("Added to cart:",s.id)},children:"Add to Cart"})})]})}function i(e){let{products:s,loading:t=!1}=e;return t?(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:Array.from({length:20}).map((e,s)=>(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg animate-pulse",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-200 rounded-t-lg"}),(0,a.jsxs)("div",{className:"p-4 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded"})]})]},s))}):0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"})})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:s.map(e=>(0,a.jsx)(o,{product:e},e.id))})}},3977:(e,s,t)=>{Promise.resolve().then(t.bind(t,5601))},5601:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(5155),r=t(5695),l=t(2115),n=t(2302),c=t(7141);function o(e){let{placeholder:s="Search for products...",className:t="",onSearch:o,autoFocus:i=!1,initialValue:d="",categoryContext:u,brandContext:m,maxSuggestions:x=5}=e,[h,g]=(0,l.useState)(d),[p,f]=(0,l.useState)([]),[j,b]=(0,l.useState)([]),[y,v]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[k,_]=(0,l.useState)(null),S=function(e,s){let[t,a]=(0,l.useState)(e);return(0,l.useEffect)(()=>{let s=setTimeout(()=>{a(e)},300);return()=>{clearTimeout(s)}},[e,300]),t}(h,300),L=(0,r.useRouter)(),C=(0,l.useRef)(null);return(0,l.useEffect)(()=>{if(S.length<2){f([]),b([]),_(null);return}(async()=>{v(!0),_(null);try{let e=new URLSearchParams({q:S,limit:x.toString()});u&&e.append("category",u),m&&e.append("brand",m);let s=await n.uE.get("".concat(c.Sn.SEARCH.SUGGESTIONS,"?").concat(e.toString()));s.success&&s.data?(f(s.data.suggestions||[]),b(s.data.products||[])):s.error&&(_(s.error.message),f([]),b([]))}catch(e){console.error("Error fetching search suggestions:",e),_("Failed to fetch suggestions. Please try again."),f([]),b([])}finally{v(!1)}})()},[S,u,m,x]),(0,l.useEffect)(()=>{if("undefined"==typeof document)return;let e=e=>{C.current&&!C.current.contains(e.target)&&w(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsxs)("div",{className:"relative ".concat(t),ref:C,children:[(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),h.trim()&&(o?o(h):L.push("/products?search=".concat(encodeURIComponent(h))),w(!1))},className:"w-full",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,a.jsx)("input",{type:"text",value:h,onChange:e=>{g(e.target.value),w(!0)},onFocus:()=>w(!0),onKeyDown:e=>{"Escape"===e.key&&w(!1)},placeholder:s,autoFocus:i,className:"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Search","aria-expanded":N,"aria-autocomplete":"list",role:"combobox"}),h&&(0,a.jsx)("button",{type:"button",onClick:()=>{g(""),f([]),b([]),_(null)},className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":"Clear search",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":"Search",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),N&&h.length>=2&&(0,a.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto",role:"listbox",children:y?(0,a.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"}),"Loading suggestions..."]}):k?(0,a.jsxs)("div",{className:"p-4 text-center text-red-500",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),k]}):(0,a.jsx)(a.Fragment,{children:0===p.length&&0===j.length?(0,a.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No suggestions found"}):(0,a.jsxs)(a.Fragment,{children:[p.length>0&&(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-1",children:"Suggestions"}),(0,a.jsx)("ul",{children:p.map((e,s)=>(0,a.jsx)("li",{role:"option",children:(0,a.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",onClick:()=>{g(e),o?o(e):L.push("/products?search=".concat(encodeURIComponent(e))),w(!1)},children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,a.jsx)("span",{dangerouslySetInnerHTML:{__html:e.replace(RegExp("(".concat(h,")"),"gi"),'<span class="font-semibold text-blue-600">$1</span>')}})]})})},s))})]}),j.length>0&&(0,a.jsxs)("div",{className:"p-2 ".concat(p.length>0?"border-t border-gray-200":""),children:[(0,a.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-1",children:"Products"}),(0,a.jsx)("ul",{children:j.map(e=>(0,a.jsx)("li",{role:"option",children:(0,a.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",onClick:()=>{L.push("/products/".concat(e.slug)),w(!1)},children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.image?(0,a.jsx)("div",{className:"h-12 w-12 relative mr-3 flex-shrink-0",children:(0,a.jsx)("img",{src:e.image,alt:e.name,className:"h-full w-full object-cover rounded",loading:"lazy"})}):(0,a.jsx)("div",{className:"h-12 w-12 bg-gray-200 mr-3 flex-shrink-0 rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-xs",children:"No image"})}),(0,a.jsxs)("div",{className:"flex-grow min-w-0",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:(0,a.jsx)("span",{dangerouslySetInnerHTML:{__html:e.name.replace(RegExp("(".concat(h,")"),"gi"),'<span class="font-semibold text-blue-600">$1</span>')}})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,a.jsxs)("span",{className:"font-medium",children:["$",e.price.toFixed(2)]}),e.category&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"mx-1",children:"•"}),(0,a.jsx)("span",{className:"truncate",children:e.category})]})]})]})]})})},e.id))})]})]})})})]})}function i(e){let{className:s="",onFilterChange:t,initialFilters:c={}}=e,[o,i]=(0,l.useState)({}),[d,u]=(0,l.useState)(c),[m,x]=(0,l.useState)(!1),[h,g]=(0,l.useState)(null),[p,f]=(0,l.useState)([null,null]),j=(0,r.useRouter)(),b=(0,r.useSearchParams)();(0,l.useEffect)(()=>{y()},[]),(0,l.useEffect)(()=>{let e={},s=b.get("category");s&&(e.category=s);let t=b.get("brand");t&&(e.brand=t);let a=b.get("min_price"),r=b.get("max_price");(a||r)&&(f([a?parseFloat(a):null,r?parseFloat(r):null]),e.price_range=[a?parseFloat(a):null,r?parseFloat(r):null]),"true"===b.get("discount_only")&&(e.discount_only=!0),"true"===b.get("is_featured")&&(e.is_featured=!0),u(e)},[b]);let y=async()=>{x(!0),g(null);try{var e,s;let t=b.get("category");if(t){let s=await n.uE.get("/categories/".concat(t,"/filters/"));s.success&&s.data?i({categories:[{name:s.data.category.name,count:s.data.total_products}],brands:s.data.brands,price_ranges:s.data.price_ranges}):g((null==(e=s.error)?void 0:e.message)||"Failed to load filter options")}else{let e=await n.uE.get("/categories/");e.success&&e.data?i({categories:e.data.data.map(e=>({name:e.name,count:e.product_count})),brands:[],price_ranges:[{from:null,to:100,count:0,label:"Under $100"},{from:100,to:500,count:0,label:"$100 - $500"},{from:500,to:1e3,count:0,label:"$500 - $1000"},{from:1e3,to:null,count:0,label:"$1000+"}]}):g((null==(s=e.error)?void 0:s.message)||"Failed to load filter options")}}catch(e){console.error("Error fetching filter options:",e),g("Failed to load filter options. Please try again.")}finally{x(!1)}},v=e=>{f(e);let s={...d};s.price_range=e,N(s)},N=e=>{if(u(e),t)t(e);else{let s=new URLSearchParams(window.location.search);if(e.category?s.set("category",e.category):s.delete("category"),e.brand?s.set("brand",e.brand):s.delete("brand"),e.price_range){let[t,a]=e.price_range;null!==t?s.set("min_price",t.toString()):s.delete("min_price"),null!==a?s.set("max_price",a.toString()):s.delete("max_price")}else s.delete("min_price"),s.delete("max_price");e.discount_only?s.set("discount_only","true"):s.delete("discount_only"),e.is_featured?s.set("is_featured","true"):s.delete("is_featured");let t=b.get("search");t&&s.set("search",t);let a=s.toString();j.push("/products".concat(a?"?".concat(a):""))}},w=Object.keys(d).length>0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Filters"}),w&&(0,a.jsx)("button",{onClick:()=>{if(u({}),f([null,null]),t)t({});else{let e=b.get("search");e?j.push("/products?search=".concat(encodeURIComponent(e))):j.push("/products")}},className:"text-sm text-blue-600 hover:text-blue-800",children:"Clear All"})]}),m?(0,a.jsxs)("div",{className:"py-4 text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"Loading filters..."})]}):h?(0,a.jsxs)("div",{className:"py-4 text-center text-red-500",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),h]}):(0,a.jsxs)(a.Fragment,{children:[o.categories&&o.categories.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Categories"}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:o.categories.map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"category-".concat(e.name),checked:d.category===e.name,onChange:()=>(e=>{let s={...d};s.category===e?delete s.category:s.category=e,N(s)})(e.name),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsxs)("label",{htmlFor:"category-".concat(e.name),className:"ml-2 text-sm text-gray-700",children:[e.name," ",(0,a.jsxs)("span",{className:"text-gray-500",children:["(",e.count,")"]})]})]},e.name))})]}),o.brands&&o.brands.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Brands"}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:o.brands.map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"brand-".concat(e.name),checked:d.brand===e.name,onChange:()=>(e=>{let s={...d};s.brand===e?delete s.brand:s.brand=e,N(s)})(e.name),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsxs)("label",{htmlFor:"brand-".concat(e.name),className:"ml-2 text-sm text-gray-700",children:[e.name," ",(0,a.jsxs)("span",{className:"text-gray-500",children:["(",e.count,")"]})]})]},e.name))})]}),o.price_ranges&&o.price_ranges.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,a.jsxs)("div",{className:"space-y-2",children:[o.price_ranges.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",id:"price-range-".concat(s),name:"price-range",checked:p[0]===e.from&&p[1]===e.to,onChange:()=>v([e.from,e.to]),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsxs)("label",{htmlFor:"price-range-".concat(s),className:"ml-2 text-sm text-gray-700",children:[e.label," ",(0,a.jsxs)("span",{className:"text-gray-500",children:["(",e.count,")"]})]})]},s)),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:null!==p[0]?p[0]:"",onChange:e=>{v([e.target.value?parseFloat(e.target.value):null,p[1]])},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded",min:"0"}),(0,a.jsx)("span",{className:"text-gray-500",children:"to"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:null!==p[1]?p[1]:"",onChange:e=>{let s=e.target.value?parseFloat(e.target.value):null;v([p[0],s])},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded",min:"0"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Additional Filters"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"discount-only",checked:!!d.discount_only,onChange:e=>(e=>{let s={...d};e?s.discount_only=!0:delete s.discount_only,N(s)})(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"discount-only",className:"ml-2 text-sm text-gray-700",children:"Discounted Items Only"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"featured-only",checked:!!d.is_featured,onChange:e=>(e=>{let s={...d};e?s.is_featured=!0:delete s.is_featured,N(s)})(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"featured-only",className:"ml-2 text-sm text-gray-700",children:"Featured Items Only"})]})]})]})]})]})}var d=t(1050);function u(e){let{pagination:s,onPageChange:t}=e,{current_page:r,total_pages:l}=s;return l<=1?null:(0,a.jsx)("nav",{className:"flex justify-center mt-8",children:(0,a.jsxs)("ul",{className:"flex items-center space-x-1",children:[(0,a.jsx)("li",{children:(0,a.jsx)("button",{onClick:()=>t(r-1),disabled:1===r,className:"px-3 py-2 rounded-md text-sm ".concat(1===r?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"aria-label":"Previous page",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})}),(()=>{let e=[];e.push(1);let s=Math.max(2,r-Math.floor(2.5)),t=Math.min(l-1,s+5-2);s<=2&&(s=2,t=Math.min(l-1,5)),t>=l-1&&(t=l-1,s=Math.max(2,l-5+1)),s>2&&e.push("ellipsis-start");for(let a=s;a<=t;a++)e.push(a);return t<l-1&&e.push("ellipsis-end"),l>1&&e.push(l),e})().map((e,s)=>(0,a.jsx)("li",{children:"ellipsis-start"===e||"ellipsis-end"===e?(0,a.jsx)("span",{className:"px-3 py-2 text-gray-500",children:"..."}):(0,a.jsx)("button",{onClick:()=>t(e),className:"px-3 py-2 rounded-md text-sm ".concat(r===e?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"),children:e})},"page-".concat(e,"-").concat(s))),(0,a.jsx)("li",{children:(0,a.jsx)("button",{onClick:()=>t(r+1),disabled:r===l,className:"px-3 py-2 rounded-md text-sm ".concat(r===l?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"aria-label":"Next page",children:(0,a.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})})]})})}function m(e){let{className:s="",initialQuery:t="",initialFilters:o={}}=e,[i,m]=(0,l.useState)([]),[x,h]=(0,l.useState)(!1),[g,p]=(0,l.useState)(null),[f,j]=(0,l.useState)(0),[b,y]=(0,l.useState)(1),[v,N]=(0,l.useState)(20),[w,k]=(0,l.useState)(1),[_,S]=(0,l.useState)("relevance"),[L,C]=(0,l.useState)(t),[F,M]=(0,l.useState)(o),R=(0,r.useRouter)(),E=(0,r.useSearchParams)();(0,l.useEffect)(()=>{let e=E.get("search")||"";C(e);let s=E.get("page");s?y(parseInt(s,10)):y(1);let t=E.get("sort_by");t?S(t):S(e?"relevance":"created_at");let a={},r=E.get("category");r&&(a.category=r);let l=E.get("brand");l&&(a.brand=l);let n=E.get("min_price"),c=E.get("max_price");(n||c)&&(a.price_range=[n?parseFloat(n):null,c?parseFloat(c):null]),"true"===E.get("discount_only")&&(a.discount_only=!0),"true"===E.get("is_featured")&&(a.is_featured=!0),M(a),P(e,a,t||(e?"relevance":"created_at"),s?parseInt(s,10):1)},[E]);let P=async(e,s,t,a)=>{h(!0),p(null);try{let r=new URLSearchParams;if(e&&r.append("q",e),r.append("page",a.toString()),r.append("page_size",v.toString()),r.append("sort_by",t),s.category&&r.append("category",s.category),s.brand&&r.append("brand",s.brand),s.price_range){let[e,t]=s.price_range;null!==e&&r.append("min_price",e.toString()),null!==t&&r.append("max_price",t.toString())}s.discount_only&&r.append("discount_only","true"),s.is_featured&&r.append("is_featured","true");let l=await n.uE.get("".concat(c.Sn.SEARCH.PRODUCTS,"?").concat(r.toString()));l.success&&l.data?(m(l.data.results||[]),j(l.data.count||0),y(l.data.page||1),k(l.data.num_pages||1)):l.error&&(p(l.error.message),m([]),j(0))}catch(e){console.error("Error fetching search results:",e),p("Failed to load search results. Please try again."),m([]),j(0)}finally{h(!1)}};return(0,a.jsxs)("div",{className:s,children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[(0,a.jsxs)("div",{className:"mb-4 sm:mb-0",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:L?'Search results for "'.concat(L,'"'):"All Products"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-1",children:x?"Loading...":"".concat(f," products found")})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("label",{htmlFor:"sort-by",className:"text-sm text-gray-600 mr-2",children:"Sort by:"}),(0,a.jsxs)("select",{id:"sort-by",value:_,onChange:e=>(e=>{S(e);{let s=new URLSearchParams(window.location.search);s.set("sort_by",e),s.delete("page"),R.push("/products?".concat(s.toString()))}})(e.target.value),className:"text-sm border border-gray-300 rounded-md py-1 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:x,children:[L&&(0,a.jsx)("option",{value:"relevance",children:"Relevance"}),(0,a.jsx)("option",{value:"price_asc",children:"Price: Low to High"}),(0,a.jsx)("option",{value:"price_desc",children:"Price: High to Low"}),(0,a.jsx)("option",{value:"created_at",children:"Newest First"}),(0,a.jsx)("option",{value:"-created_at",children:"Oldest First"}),(0,a.jsx)("option",{value:"discount",children:"Biggest Discount"})]})]})]}),x?(0,a.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"Loading search results..."})]}):g?(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 text-center text-red-600",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 inline-block mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),g]}):0===i.length?(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-8 text-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-500",children:L?"We couldn't find any products matching \"".concat(L,'". Try using different keywords or filters.'):"No products match the selected filters. Try changing your filter options."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.q,{products:i}),w>1&&(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(u,{pagination:{current_page:b,total_pages:w,count:f,page_size:v,next:b<w?"?page=".concat(b+1):null,previous:b>1?"?page=".concat(b-1):null},onPageChange:e=>{y(e);{let s=new URLSearchParams(window.location.search);s.set("page",e.toString()),R.push("/products?".concat(s.toString()))}}})})]})]})}function x(){let e=(0,r.useSearchParams)().get("search")||"";return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(o,{initialValue:e,autoFocus:!e,className:"max-w-3xl mx-auto"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,a.jsx)("div",{className:"w-full md:w-64 flex-shrink-0",children:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading filters..."}),children:(0,a.jsx)(i,{})})}),(0,a.jsx)("div",{className:"flex-grow",children:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading results..."}),children:(0,a.jsx)(m,{initialQuery:e})})})]})]})}function h(){return(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(x,{})})}}},e=>{e.O(0,[3464,7244,2125,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=3977)),_N_E=e.O()}]);