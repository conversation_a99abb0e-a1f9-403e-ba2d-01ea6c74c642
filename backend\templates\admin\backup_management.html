{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Backup Management{% endblock %}

{% block extrahead %}
<style>
    .backup-controls {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .backup-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007cba;
        display: block;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    .btn {
        padding: 10px 20px;
        margin: 5px;
        background-color: #007cba;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn:hover {
        background-color: #005a87;
    }
    
    .btn-success {
        background-color: #28a745;
    }
    
    .btn-success:hover {
        background-color: #218838;
    }
    
    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-warning:hover {
        background-color: #e0a800;
    }
    
    .btn-danger {
        background-color: #dc3545;
    }
    
    .btn-danger:hover {
        background-color: #c82333;
    }
    
    .backup-form {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        align-items: end;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
    }
    
    .form-group select,
    .form-group input {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .progress-container {
        display: none;
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    
    .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #007cba;
        width: 0%;
        transition: width 0.3s ease;
    }
    
    .alert {
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    
    .config-section {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .config-section h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007cba;
        padding-bottom: 10px;
    }
    
    .config-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .config-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<h1>Backup Management</h1>

{% if error %}
<div class="alert alert-danger">
    <strong>Error:</strong> {{ error }}
</div>
{% endif %}

<!-- Backup Statistics -->
<div class="backup-stats">
    <div class="stat-card">
        <span class="stat-number">{{ total_backups }}</span>
        <div class="stat-label">Total Backups</div>
    </div>
    <div class="stat-card">
        <span class="stat-number">{{ full_backups }}</span>
        <div class="stat-label">Full Backups</div>
    </div>
    <div class="stat-card">
        <span class="stat-number">{{ incremental_backups }}</span>
        <div class="stat-label">Incremental Backups</div>
    </div>
    <div class="stat-card">
        <span class="stat-number">{{ total_size_gb }}</span>
        <div class="stat-label">Total Size (GB)</div>
    </div>
</div>

<!-- Backup Controls -->
<div class="backup-controls">
    <h3>Create New Backup</h3>
    <form id="backupForm" class="backup-form">
        <div class="form-group">
            <label for="backupType">Backup Type:</label>
            <select id="backupType" name="backup_type">
                <option value="full">Full Backup</option>
                <option value="incremental">Incremental Backup</option>
            </select>
        </div>
        <div class="form-group">
            <label for="databaseAlias">Database:</label>
            <select id="databaseAlias" name="database_alias">
                <option value="default">Default</option>
                {% for db_alias in databases %}
                <option value="{{ db_alias }}">{{ db_alias }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-success">Create Backup</button>
        </div>
    </form>
    
    <div id="progressContainer" class="progress-container">
        <div>Creating backup...</div>
        <div class="progress-bar">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div id="progressText">0%</div>
    </div>
    
    <div id="backupResult"></div>
</div>

<!-- Backup Configuration -->
<div class="config-section">
    <h3>Backup Configuration</h3>
    {% if backup_config %}
    <div class="config-item">
        <span><strong>Retention Period:</strong></span>
        <span>{{ backup_config.retention_days }} days</span>
    </div>
    <div class="config-item">
        <span><strong>Backup Directory:</strong></span>
        <span>{{ backup_config.backup_dir }}</span>
    </div>
    <div class="config-item">
        <span><strong>Compression:</strong></span>
        <span>{% if backup_config.compression_enabled %}Enabled{% else %}Disabled{% endif %}</span>
    </div>
    {% endif %}
    
    <div style="margin-top: 15px;">
        <button onclick="testBackupSystem()" class="btn btn-warning">Test Backup System</button>
        <button onclick="cleanupOldBackups()" class="btn btn-danger">Cleanup Old Backups</button>
    </div>
</div>

<!-- Latest Backup Info -->
{% if latest_backup %}
<div class="config-section">
    <h3>Latest Backup</h3>
    <div class="config-item">
        <span><strong>Backup ID:</strong></span>
        <span>{{ latest_backup.backup_id }}</span>
    </div>
    <div class="config-item">
        <span><strong>Type:</strong></span>
        <span>{{ latest_backup.backup_type|title }}</span>
    </div>
    <div class="config-item">
        <span><strong>Created:</strong></span>
        <span>{{ latest_backup.timestamp|date:"Y-m-d H:i:s" }}</span>
    </div>
    <div class="config-item">
        <span><strong>Size:</strong></span>
        <span>{{ latest_backup.file_size|filesizeformat }}</span>
    </div>
    
    <div style="margin-top: 15px;">
        <button onclick="verifyBackup('{{ latest_backup.backup_id }}')" class="btn">Verify Integrity</button>
        <button onclick="showRestoreDialog('{{ latest_backup.backup_id }}')" class="btn btn-warning">Restore</button>
    </div>
</div>
{% endif %}

<!-- Restore Dialog -->
<div id="restoreDialog" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border: 2px solid #ddd; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 1000;">
    <h3>Restore Backup</h3>
    <form id="restoreForm">
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="restoreDatabase">Target Database:</label>
            <select id="restoreDatabase" name="database_alias">
                <option value="default">Default</option>
            </select>
        </div>
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="targetDatabaseName">Target Database Name (optional):</label>
            <input type="text" id="targetDatabaseName" name="target_database" placeholder="Leave empty to use original database">
        </div>
        <div style="text-align: right;">
            <button type="button" onclick="closeRestoreDialog()" class="btn">Cancel</button>
            <button type="submit" class="btn btn-danger">Restore</button>
        </div>
    </form>
</div>

<div id="overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;" onclick="closeRestoreDialog()"></div>

<script>
let currentBackupId = null;

document.getElementById('backupForm').addEventListener('submit', function(e) {
    e.preventDefault();
    createBackup();
});

document.getElementById('restoreForm').addEventListener('submit', function(e) {
    e.preventDefault();
    restoreBackup();
});

function createBackup() {
    const formData = new FormData(document.getElementById('backupForm'));
    const data = {
        backup_type: formData.get('backup_type'),
        database_alias: formData.get('database_alias')
    };
    
    showProgress();
    
    fetch('{% url "admin:create_backup_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideProgress();
        if (data.success) {
            showResult('success', data.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            showResult('error', data.error || 'Backup creation failed');
        }
    })
    .catch(error => {
        hideProgress();
        showResult('error', 'Network error: ' + error.message);
    });
}

function verifyBackup(backupId) {
    const data = { backup_id: backupId };
    
    fetch('{% url "admin:verify_backup_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
        } else {
            alert('Verification failed: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    });
}

function showRestoreDialog(backupId) {
    currentBackupId = backupId;
    document.getElementById('restoreDialog').style.display = 'block';
    document.getElementById('overlay').style.display = 'block';
}

function closeRestoreDialog() {
    document.getElementById('restoreDialog').style.display = 'none';
    document.getElementById('overlay').style.display = 'none';
    currentBackupId = null;
}

function restoreBackup() {
    if (!currentBackupId) return;
    
    const formData = new FormData(document.getElementById('restoreForm'));
    const data = {
        backup_id: currentBackupId,
        database_alias: formData.get('database_alias'),
        target_database: formData.get('target_database') || null
    };
    
    if (!confirm('Are you sure you want to restore this backup? This will overwrite existing data.')) {
        return;
    }
    
    closeRestoreDialog();
    showProgress();
    
    fetch('{% url "admin:restore_backup_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideProgress();
        if (data.success) {
            showResult('success', data.message);
        } else {
            showResult('error', data.error || 'Restore failed');
        }
    })
    .catch(error => {
        hideProgress();
        showResult('error', 'Network error: ' + error.message);
    });
}

function testBackupSystem() {
    alert('Backup system test functionality would be implemented here.');
}

function cleanupOldBackups() {
    if (confirm('Are you sure you want to cleanup old backups? This action cannot be undone.')) {
        alert('Cleanup functionality would be implemented here.');
    }
}

function showProgress() {
    document.getElementById('progressContainer').style.display = 'block';
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        document.getElementById('progressFill').style.width = progress + '%';
        document.getElementById('progressText').textContent = progress + '%';
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 500);
}

function hideProgress() {
    document.getElementById('progressContainer').style.display = 'none';
    document.getElementById('progressFill').style.width = '0%';
    document.getElementById('progressText').textContent = '0%';
}

function showResult(type, message) {
    const resultDiv = document.getElementById('backupResult');
    resultDiv.className = 'alert alert-' + (type === 'success' ? 'success' : 'danger');
    resultDiv.innerHTML = '<strong>' + (type === 'success' ? 'Success:' : 'Error:') + '</strong> ' + message;
    
    setTimeout(() => {
        resultDiv.innerHTML = '';
        resultDiv.className = '';
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}