"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4775],{511:(e,s,r)=>{r.d(s,{z:()=>x});var t=r(5155),l=r(5695),a=r(6322),i=r(9519),n=r(3741),o=r(7141),c=r(3568);function x(e){let{variant:s="ghost",size:r="sm",className:x="",children:h="Logout",redirectTo:d=o.bw.HOME}=e,m=(0,a.jL)(),u=(0,l.useRouter)(),{loading:j}=(0,a.GV)(e=>e.auth),v=async()=>{try{await m((0,i.y4)()).unwrap(),c.Ay.success("Logged out successfully"),u.push(d)}catch(e){c.Ay.error("Logout failed"),u.push(d)}};return(0,t.jsx)(n.$,{variant:s,size:r,className:x,onClick:v,loading:j,children:h})}},4775:(e,s,r)=>{r.d(s,{P:()=>d});var t=r(5155),l=r(6874),a=r.n(l),i=r(2115),n=r(6322),o=r(511),c=r(7141);function x(){var e;let[s,r]=(0,i.useState)(!1),[l,x]=(0,i.useState)(""),{isAuthenticated:h,user:d}=(0,n.GV)(e=>e.auth),{itemCount:m}=(0,n.GV)(e=>e.cart);return(0,t.jsxs)("header",{className:"bg-blue-600 shadow-lg",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsxs)(a(),{href:c.bw.HOME,className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"text-white text-2xl font-bold",children:"FlipMart"}),(0,t.jsxs)("div",{className:"text-yellow-300 text-xs italic",children:["Explore ",(0,t.jsx)("span",{className:"text-yellow-200",children:"Plus"})]})]})}),(0,t.jsx)("div",{className:"flex-1 max-w-2xl mx-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search for products, brands and more",value:l,onChange:e=>x(e.target.value),className:"w-full px-4 py-2 pl-4 pr-12 text-gray-900 bg-white border-0 rounded-sm focus:outline-none focus:ring-2 focus:ring-yellow-400"}),(0,t.jsx)("button",{className:"absolute right-0 top-0 h-full px-4 bg-yellow-400 hover:bg-yellow-500 rounded-r-sm",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsx)(a(),{href:"/seller/register",className:"text-white hover:text-yellow-300 text-sm font-medium hidden md:block",children:"Become a Seller"}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("button",{className:"flex items-center space-x-1 text-white hover:text-yellow-300 text-sm font-medium",children:[(0,t.jsx)("span",{children:"More"}),(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),(0,t.jsxs)(a(),{href:c.bw.CART,className:"relative flex items-center space-x-1 text-white hover:text-yellow-300 p-2",children:[(0,t.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"})}),(0,t.jsx)("span",{className:"text-sm font-medium hidden md:block",children:"Cart"}),m>0&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:m})]}),h?(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("button",{onClick:()=>r(!s),className:"flex items-center space-x-2 text-white hover:text-yellow-300 px-3 py-2 text-sm font-medium",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xs text-gray-700 font-semibold",children:null==d||null==(e=d.username)?void 0:e.charAt(0).toUpperCase()})}),(0,t.jsx)("span",{className:"hidden md:block",children:null==d?void 0:d.username}),(0,t.jsx)("svg",{className:"h-4 w-4 transition-transform ".concat(s?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),s&&(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg py-2 z-50 border",children:[(0,t.jsx)("div",{className:"px-4 py-2 border-b border-gray-100",children:(0,t.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["Hello ",null==d?void 0:d.username]})}),(0,t.jsxs)(a(),{href:c.bw.PROFILE,className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"My Profile"]}),(0,t.jsxs)(a(),{href:c.bw.ORDERS,className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2"})}),"My Orders"]}),(0,t.jsxs)(a(),{href:"/wishlist",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),"Wishlist"]}),(0,t.jsxs)(a(),{href:"/rewards",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),"Rewards"]}),(null==d?void 0:d.user_type)==="seller"&&(0,t.jsxs)(a(),{href:c.bw.SELLER,className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),"Seller Dashboard"]}),(null==d?void 0:d.user_type)==="admin"&&(0,t.jsxs)(a(),{href:c.bw.ADMIN,className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>r(!1),children:[(0,t.jsxs)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Admin Panel"]}),(0,t.jsx)("div",{className:"border-t border-gray-100 mt-2",children:(0,t.jsx)("div",{className:"px-4 py-2",children:(0,t.jsxs)(o.z,{variant:"ghost",size:"sm",className:"w-full justify-start text-left text-red-600 hover:text-red-700 hover:bg-red-50 flex items-center",children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Sign Out"]})})})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(a(),{href:c.bw.LOGIN,className:"text-white hover:text-yellow-300 px-3 py-2 text-sm font-medium",children:"Login"}),(0,t.jsx)(a(),{href:c.bw.REGISTER,className:"bg-white text-blue-600 hover:bg-gray-100 px-4 py-2 rounded text-sm font-medium",children:"Sign Up"})]})]})]})}),(0,t.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center space-x-8 h-10 text-sm",children:[(0,t.jsx)(a(),{href:"/electronics",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Electronics"}),(0,t.jsx)(a(),{href:"/fashion",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Fashion"}),(0,t.jsx)(a(),{href:"/home",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Home & Kitchen"}),(0,t.jsx)(a(),{href:"/books",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Books"}),(0,t.jsx)(a(),{href:"/sports",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Sports"}),(0,t.jsx)(a(),{href:"/beauty",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Beauty"}),(0,t.jsx)(a(),{href:"/grocery",className:"text-gray-700 hover:text-blue-600 font-medium",children:"Grocery"})]})})})]})}function h(){return(0,t.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"FlipMart"}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"Your one-stop shop for all your needs. Quality products, great prices, and excellent service."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Twitter"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297z"})})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-white tracking-wider uppercase mb-4",children:"Quick Links"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(a(),{href:c.bw.PRODUCTS,className:"text-gray-300 hover:text-white transition-colors",children:"Products"})}),(0,t.jsx)("li",{children:(0,t.jsx)(a(),{href:c.bw.CART,className:"text-gray-300 hover:text-white transition-colors",children:"Cart"})}),(0,t.jsx)("li",{children:(0,t.jsx)(a(),{href:c.Hp.ORDERS,className:"text-gray-300 hover:text-white transition-colors",children:"Orders"})}),(0,t.jsx)("li",{children:(0,t.jsx)(a(),{href:"/deals",className:"text-gray-300 hover:text-white transition-colors",children:"Deals"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-white tracking-wider uppercase mb-4",children:"Support"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Help Center"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Contact Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Terms of Service"})})]})]})]}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-700",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 FlipMart. All rights reserved."}),(0,t.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Become a Seller"}),(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Advertise"}),(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gift Cards"})]})]})})]})})}function d(e){let{children:s}=e;return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,t.jsx)(x,{}),(0,t.jsx)("main",{className:"flex-1",children:s}),(0,t.jsx)(h,{})]})}}}]);