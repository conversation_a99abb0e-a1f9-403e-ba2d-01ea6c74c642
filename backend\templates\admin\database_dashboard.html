{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Database Administration Dashboard{% endblock %}

{% block extrahead %}
<style>
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .dashboard-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-card h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007cba;
        padding-bottom: 10px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-healthy { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-error { background-color: #dc3545; }
    
    .metric-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .metric-row:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        font-weight: bold;
        color: #666;
    }
    
    .metric-value {
        color: #333;
    }
    
    .alert-item {
        padding: 10px;
        margin: 5px 0;
        border-left: 4px solid #ffc107;
        background-color: #fff3cd;
        border-radius: 4px;
    }
    
    .alert-critical {
        border-left-color: #dc3545;
        background-color: #f8d7da;
    }
    
    .alert-high {
        border-left-color: #fd7e14;
        background-color: #ffeaa7;
    }
    
    .btn-group {
        margin: 10px 0;
    }
    
    .btn-group .btn {
        margin-right: 10px;
        padding: 8px 16px;
        background-color: #007cba;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-group .btn:hover {
        background-color: #005a87;
    }
    
    .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 5px 0;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #007cba;
        transition: width 0.3s ease;
    }
    
    .progress-warning { background-color: #ffc107; }
    .progress-danger { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<h1>Database Administration Dashboard</h1>

{% if error %}
<div class="alert alert-danger">
    <strong>Error:</strong> {{ error }}
</div>
{% endif %}

<div class="btn-group">
    <a href="{% url 'admin:health_check' %}" class="btn">Health Check</a>
    <a href="{% url 'admin:backup_management' %}" class="btn">Backup Management</a>
    <a href="{% url 'admin:user_management' %}" class="btn">User Management</a>
    <a href="{% url 'admin:performance_report' %}" class="btn">Performance Report</a>
    <button onclick="refreshDashboard()" class="btn">Refresh Dashboard</button>
</div>

<div class="dashboard-grid">
    <!-- Database Status Card -->
    <div class="dashboard-card">
        <h3>Database Status</h3>
        <div class="metric-row">
            <span class="metric-label">Total Databases:</span>
            <span class="metric-value">{{ total_databases }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Healthy Databases:</span>
            <span class="metric-value">{{ healthy_databases }}</span>
        </div>
        
        {% for db_alias, status in db_status.items %}
        <div class="metric-row">
            <span class="metric-label">
                <span class="status-indicator status-{{ status }}"></span>
                {{ db_alias }}:
            </span>
            <span class="metric-value">{{ status|title }}</span>
        </div>
        {% endfor %}
    </div>
    
    <!-- Backup Status Card -->
    <div class="dashboard-card">
        <h3>Backup Status</h3>
        {% if backup_status.error %}
        <div class="alert alert-danger">{{ backup_status.error }}</div>
        {% else %}
        <div class="metric-row">
            <span class="metric-label">Total Backups:</span>
            <span class="metric-value">{{ backup_status.total_backups|default:"0" }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Full Backups:</span>
            <span class="metric-value">{{ backup_status.full_backups|default:"0" }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Incremental Backups:</span>
            <span class="metric-value">{{ backup_status.incremental_backups|default:"0" }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Total Size:</span>
            <span class="metric-value">{{ backup_status.total_size_gb|default:"0" }} GB</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Latest Full Backup:</span>
            <span class="metric-value">
                {% if backup_status.recent_full_backup %}
                    <span class="status-indicator status-healthy"></span>Recent
                {% else %}
                    <span class="status-indicator status-warning"></span>Overdue
                {% endif %}
            </span>
        </div>
        {% endif %}
    </div>
    
    <!-- System Metrics Card -->
    <div class="dashboard-card">
        <h3>System Metrics</h3>
        {% if system_metrics.error %}
        <div class="alert alert-warning">{{ system_metrics.error }}</div>
        {% else %}
        <div class="metric-row">
            <span class="metric-label">CPU Usage:</span>
            <div class="progress-bar">
                <div class="progress-fill {% if system_metrics.cpu_percent > 80 %}progress-danger{% elif system_metrics.cpu_percent > 60 %}progress-warning{% endif %}" 
                     style="width: {{ system_metrics.cpu_percent }}%"></div>
            </div>
            <span class="metric-value">{{ system_metrics.cpu_percent }}%</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Memory Usage:</span>
            <div class="progress-bar">
                <div class="progress-fill {% if system_metrics.memory_percent > 85 %}progress-danger{% elif system_metrics.memory_percent > 70 %}progress-warning{% endif %}" 
                     style="width: {{ system_metrics.memory_percent }}%"></div>
            </div>
            <span class="metric-value">{{ system_metrics.memory_percent }}%</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Disk Usage:</span>
            <div class="progress-bar">
                <div class="progress-fill {% if system_metrics.disk_percent > 90 %}progress-danger{% elif system_metrics.disk_percent > 80 %}progress-warning{% endif %}" 
                     style="width: {{ system_metrics.disk_percent }}%"></div>
            </div>
            <span class="metric-value">{{ system_metrics.disk_percent }}%</span>
        </div>
        {% endif %}
    </div>
    
    <!-- Recent Alerts Card -->
    <div class="dashboard-card">
        <h3>Recent Alerts</h3>
        {% if recent_alerts %}
        {% for alert in recent_alerts %}
        <div class="alert-item {% if alert.severity == 'CRITICAL' %}alert-critical{% elif alert.severity == 'HIGH' %}alert-high{% endif %}">
            <strong>{{ alert.event_type }}</strong> - {{ alert.severity }}<br>
            <small>{{ alert.timestamp|date:"Y-m-d H:i:s" }}</small><br>
            {{ alert.description|truncatechars:100 }}
        </div>
        {% endfor %}
        {% else %}
        <p>No recent alerts</p>
        {% endif %}
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if user is still on the page
    if (document.visibilityState === 'visible') {
        refreshDashboard();
    }
}, 30000);
</script>
{% endblock %}