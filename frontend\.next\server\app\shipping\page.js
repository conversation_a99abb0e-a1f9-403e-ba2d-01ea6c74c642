(()=>{var a={};a.id=721,a.ids=[721],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2643:(a,b,c)=>{"use strict";c.d(b,{$:()=>e,A:()=>f});var d=c(60687);c(43210);let e=({variant:a="primary",size:b="md",loading:c=!1,disabled:e,children:f,className:g="",...h})=>(0,d.jsxs)("button",{className:`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 touch-manipulation select-none ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300",outline:"border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400 disabled:border-gray-200",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-blue-500 disabled:text-gray-400 disabled:hover:bg-transparent"}[a]} ${{sm:"px-3 py-2 text-sm min-h-[36px]",md:"px-4 py-2 text-sm min-h-[44px]",lg:"px-6 py-3 text-base min-h-[48px]"}[b]} disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none ${g}`,disabled:e||c,...h,children:[c&&(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"}),f]}),f=e},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26443:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["shipping",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,41321)),"C:\\Local_ecom\\frontend\\src\\app\\shipping\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Local_ecom\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Local_ecom\\frontend\\src\\app\\shipping\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/shipping/page",pathname:"/shipping",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/shipping/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35324:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q});var d=c(60687),e=c(43210),f=c(54864),g=c(2643);function h({children:a,className:b="",...c}){return(0,d.jsx)("div",{className:`bg-white rounded-lg border border-gray-200 shadow-sm ${b}`,...c,children:a})}c(51907),c(63547),c(53808),c(37590);var i=c(17558);let j=({onSelect:a,selectedSlotId:b,pincode:c})=>{let j=(0,f.wA)(),{deliverySlots:k,selectedDeliverySlot:l,loading:m,error:n}=(0,f.d4)(a=>a.shipping),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)([]),s=b||l?.id;return((0,e.useEffect)(()=>{if(c){let a=[];for(let b=0;b<7;b++){let c=new Date;c.setDate(c.getDate()+b),a.push(c.toISOString().split("T")[0])}r(a),p(a[0]),j((0,i.Fn)({delivery_date:a[0],pin_code:c}))}},[j,c]),(0,e.useEffect)(()=>{o&&c&&j((0,i.Fn)({delivery_date:o,pin_code:c}))},[j,o,c]),c)?m?(0,d.jsx)("div",{className:"text-center py-4",children:"Loading delivery slots..."}):n?(0,d.jsxs)("div",{className:"text-center py-4 text-red-500",children:["Error loading delivery slots: ",n]}):(0,d.jsxs)("div",{className:"delivery-slot-selector",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Select Delivery Date"}),(0,d.jsx)("div",{className:"flex overflow-x-auto space-x-2 pb-2 mb-4",children:q.map(a=>(0,d.jsx)(g.A,{onClick:()=>p(a),variant:o===a?"primary":"outline",className:"whitespace-nowrap",children:new Date(a).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})},a))}),(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Select Delivery Time"}),0===k.length?(0,d.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No delivery slots available for this date."}):(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:k.map(b=>{let c=s===b.id,e=b.is_active&&(b.available_capacity||0)>0;return(0,d.jsx)("div",{className:`cursor-pointer transition-all ${!e?"opacity-50 cursor-not-allowed":c?"border-2 border-blue-500 bg-blue-50":"hover:border-gray-300"}`,onClick:()=>e&&(b=>{j((0,i.it)(b)),a&&a(b)})(b),children:(0,d.jsxs)(h,{children:[(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:b.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:(a=>`${a.start_time} - ${a.end_time}`)(b)}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:b.day_of_week_display})]}),(0,d.jsx)("div",{className:"text-right",children:b.additional_fee>0?(0,d.jsxs)("span",{className:"text-sm font-medium text-orange-600",children:["+₹",b.additional_fee]}):(0,d.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"Free"})})]}),(0,d.jsxs)("div",{className:"mt-3 flex justify-between items-center",children:[(0,d.jsx)("div",{className:"text-xs text-gray-500",children:b.available_capacity?`${b.available_capacity} slots left`:"Limited slots"}),e?c?(0,d.jsxs)("span",{className:"text-xs text-blue-600 font-medium flex items-center",children:[(0,d.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Selected"]}):null:(0,d.jsx)("span",{className:"text-xs text-red-600 font-medium",children:"Not available"})]})]})},b.id)})})]}):(0,d.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Please enter a pincode to view available delivery slots."})},k=({addresses:a,onAddressSelect:b,onAddNewAddress:c,onEditAddress:g,className:h=""})=>{let j=(0,f.wA)(),{selectedShippingAddress:k,serviceableAreas:l,loading:m,error:n}=(0,f.d4)(a=>a.shipping),[o,p]=(0,e.useState)(null),[q,r]=(0,e.useState)({});(0,e.useEffect)(()=>{let b=a.find(a=>a.is_default)||a[0];b&&!o&&s(b)},[a]),(0,e.useEffect)(()=>()=>{j((0,i.ET)())},[j]);let s=async a=>{p(a);let c=(a=>({first_name:a.first_name,last_name:a.last_name,company:a.company,address_line_1:a.address_line_1,address_line_2:a.address_line_2,city:a.city,state:a.state,postal_code:a.postal_code,country:a.country,phone:a.phone}))(a);if(j((0,i.e6)(c)),b?.(c),a.postal_code){r(b=>({...b,[a.id]:{serviceable:!1,loading:!0}}));try{let b=await j((0,i.i8)({pin_code:a.postal_code}));i.i8.fulfilled.match(b)?r(b=>({...b,[a.id]:{serviceable:!0,loading:!1}})):r(c=>({...c,[a.id]:{serviceable:!1,loading:!1,error:b.payload}}))}catch(b){r(b=>({...b,[a.id]:{serviceable:!1,loading:!1,error:"Failed to check serviceability"}}))}}};return a.length?(0,d.jsx)("div",{className:`shipping-address-manager ${h}`,children:(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Delivery Address"}),(0,d.jsxs)("button",{onClick:c,className:"text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add New"]})]}),(0,d.jsx)("div",{className:"space-y-3",children:a.map(a=>{let b=o?.id===a.id,c=q[a.id];return(0,d.jsx)("div",{className:`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${b?"border-blue-500 bg-blue-50 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,onClick:()=>s(a),children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-start flex-1",children:[(0,d.jsx)("div",{className:`w-4 h-4 rounded-full border-2 mr-3 mt-1 flex-shrink-0 ${b?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:b&&(0,d.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("div",{className:"text-gray-600 mr-2",children:(a=>{switch(a){case"HOME":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})});case"WORK":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2zm4-3a1 1 0 00-1 1v1h2V4a1 1 0 00-1-1zm-3 4a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})});default:return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})})}})(a.type)}),(0,d.jsxs)("span",{className:"font-medium text-gray-900",children:[a.first_name," ",a.last_name]}),a.is_default&&(0,d.jsx)("span",{className:"ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:"Default"})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-1",children:(a=>[a.address_line_1,a.address_line_2,a.city,a.state,a.postal_code].filter(Boolean).join(", "))(a)}),a.phone&&(0,d.jsxs)("p",{className:"text-gray-500 text-sm",children:["Phone: ",a.phone]}),c&&(0,d.jsx)("div",{className:"mt-2",children:c.loading?(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full mr-2"}),"Checking serviceability..."]}):c.serviceable?(0,d.jsxs)("div",{className:"flex items-center text-sm text-green-600",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Delivery available"]}):(0,d.jsxs)("div",{className:"flex items-center text-sm text-red-600",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),c.error||"Not serviceable"]})})]})]}),(0,d.jsx)("button",{onClick:b=>{b.stopPropagation(),g?.(a)},className:"text-gray-400 hover:text-gray-600 ml-2",children:(0,d.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})]})},a.id)})}),n&&(0,d.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,d.jsx)("p",{className:"text-red-700 text-sm",children:n})]})})]})}):(0,d.jsx)("div",{className:`shipping-address-manager ${h}`,children:(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Delivery Address"}),(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsxs)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"No delivery addresses found"}),(0,d.jsx)("button",{onClick:c,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Add New Address"})]})]})})},l=({shipment:a,className:b=""})=>{let c=(a,b=!1)=>{let c=`w-6 h-6 ${b?"text-white":"text-gray-400"}`;switch(a){case"PENDING":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})});case"PROCESSING":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})});case"SHIPPED":case"IN_TRANSIT":return(0,d.jsxs)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:[(0,d.jsx)("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),(0,d.jsx)("path",{d:"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707L16 7.586A1 1 0 0015.414 7H14z"})]});case"OUT_FOR_DELIVERY":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})});case"DELIVERED":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"FAILED_DELIVERY":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"RETURNED":case"CANCELLED":return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return(0,d.jsx)("svg",{className:c,fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm-1-4a1 1 0 112 0 1 1 0 01-2 0zm1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}},e=(a,b=!1)=>{if(b)switch(a){case"DELIVERED":return"bg-green-500";case"SHIPPED":case"IN_TRANSIT":case"OUT_FOR_DELIVERY":return"bg-blue-500";case"PROCESSING":return"bg-yellow-500";case"CANCELLED":case"RETURNED":return"bg-red-500";case"FAILED_DELIVERY":return"bg-orange-500";default:return"bg-gray-500"}return"bg-gray-300"},f=(()=>{let b=[];a.tracking_updates&&a.tracking_updates.length>0&&a.tracking_updates.forEach(a=>{b.push({status:a.status,status_display:a.status_display,description:a.description,location:a.location,timestamp:a.timestamp,isFromTracking:!0})});let c=new Set(b.map(a=>a.status));return c.has("PENDING")||b.push({status:"PENDING",status_display:"Order Placed",description:"Your order has been placed and is being prepared for shipment.",timestamp:a.created_at,isFromTracking:!1}),a.shipped_at&&!c.has("SHIPPED")&&b.push({status:"SHIPPED",status_display:"Shipped",description:"Your order has been shipped and is on its way.",timestamp:a.shipped_at,isFromTracking:!1}),a.delivered_at&&!c.has("DELIVERED")&&b.push({status:"DELIVERED",status_display:"Delivered",description:"Your order has been successfully delivered.",timestamp:a.delivered_at,isFromTracking:!1}),b.sort((a,b)=>new Date(b.timestamp).getTime()-new Date(a.timestamp).getTime())})(),g=a.status,h=["PENDING","PROCESSING","SHIPPED","IN_TRANSIT","OUT_FOR_DELIVERY","DELIVERED"],i=h.indexOf(g);return(0,d.jsxs)("div",{className:`tracking-timeline ${b}`,children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Tracking Timeline"}),(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:h.map((a,b)=>{let f=b<=i,j=a===g;return(0,d.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,d.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${f?e(a,!0):"bg-gray-300"} ${j?"ring-4 ring-blue-200":""}`,children:c(a,f)}),(0,d.jsx)("div",{className:"mt-2 text-center",children:(0,d.jsx)("p",{className:`text-xs font-medium ${f?"text-gray-900":"text-gray-500"}`,children:(a=>({PENDING:"Order Placed",PROCESSING:"Processing",SHIPPED:"Shipped",IN_TRANSIT:"In Transit",OUT_FOR_DELIVERY:"Out for Delivery",DELIVERED:"Delivered",FAILED_DELIVERY:"Delivery Failed",RETURNED:"Returned",CANCELLED:"Cancelled"})[a]||a)(a)})}),b<h.length-1&&(0,d.jsx)("div",{className:`absolute h-0.5 w-full mt-5 ${b<i?"bg-blue-500":"bg-gray-300"}`,style:{left:"50%",right:"-50%",zIndex:-1}})]},a)})})}),f.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-4",children:"Detailed History"}),(0,d.jsx)("div",{className:"space-y-4",children:f.map((a,b)=>{let{date:g,time:h}=(a=>{let b=new Date(a);return{date:b.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),time:b.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})}})(a.timestamp),i=0===b;return(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsxs)("div",{className:"flex-shrink-0 mr-4",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${i?e(a.status,!0):"bg-gray-300"}`,children:c(a.status,i)}),b<f.length-1&&(0,d.jsx)("div",{className:"w-0.5 h-8 bg-gray-300 mx-auto mt-2"})]}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("p",{className:`text-sm font-medium ${i?"text-gray-900":"text-gray-700"}`,children:a.status_display}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"text-xs text-gray-500",children:g}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:h})]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description}),a.location&&(0,d.jsxs)("p",{className:"text-xs text-gray-500 mt-1 flex items-center",children:[(0,d.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),a.location]})]})]},`${a.timestamp}-${a.status}`)})})]}),0===f.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,d.jsx)("p",{className:"text-gray-600",children:"No tracking information available yet"})]})]})},m=({orderId:a,trackingNumber:b,showSearch:c=!0,className:g=""})=>{let h=(0,f.wA)(),{currentShipment:j,shipments:k,loading:m,error:n}=(0,f.d4)(a=>a.shipping),[o,p]=(0,e.useState)(b||""),[q,r]=(0,e.useState)("tracking");(0,e.useEffect)(()=>{if(b)s(b);else if(a){let b=k.find(b=>b.order===a);b?h((0,i.MI)(b)):h((0,i.VZ)())}return()=>{h((0,i.ET)())}},[h,b,a]),(0,e.useEffect)(()=>{if(a&&k.length>0){let b=k.find(b=>b.order===a);b&&h((0,i.MI)(b))}},[h,a,k]);let s=async a=>{if(a.trim())try{await h((0,i.Ox)(a.trim())).unwrap()}catch(a){console.error("Failed to track shipment:",a)}};return(0,d.jsxs)("div",{className:`order-tracking-interface ${g}`,children:[c&&(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Track Your Order"}),(0,d.jsx)("form",{onSubmit:a=>{a.preventDefault(),o.trim()&&s(o.trim())},className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{htmlFor:"search-query",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Tracking Number or Order ID"}),(0,d.jsx)("input",{id:"search-query",type:"text",value:o,onChange:a=>p(a.target.value),placeholder:"e.g., TRK123456789 or ORD123456",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsx)("button",{type:"submit",disabled:m||!o.trim(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:m?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Tracking..."]}):"Track"})})]})})]}),n&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,d.jsx)("p",{className:"text-red-700",children:n})]})}),j&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Shipment Details"}),(0,d.jsxs)("div",{className:`px-3 py-1 rounded-full text-sm font-medium flex items-center ${(a=>{switch(a){case"DELIVERED":return"text-green-600 bg-green-100";case"SHIPPED":case"IN_TRANSIT":case"OUT_FOR_DELIVERY":return"text-blue-600 bg-blue-100";case"PROCESSING":return"text-yellow-600 bg-yellow-100";case"CANCELLED":case"RETURNED":return"text-red-600 bg-red-100";case"FAILED_DELIVERY":return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}})(j.status)}`,children:[(a=>{switch(a){case"DELIVERED":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"SHIPPED":case"IN_TRANSIT":return(0,d.jsxs)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,d.jsx)("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),(0,d.jsx)("path",{d:"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707L16 7.586A1 1 0 0015.414 7H14z"})]});case"OUT_FOR_DELIVERY":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})});case"PROCESSING":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})});case"CANCELLED":case"RETURNED":return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}})(j.status),(0,d.jsx)("span",{className:"ml-2",children:j.status_display})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tracking Information"}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Tracking Number:"}),(0,d.jsx)("span",{className:"font-medium",children:j.tracking_number})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Shipping Partner:"}),(0,d.jsx)("span",{className:"font-medium",children:j.shipping_partner_name})]}),j.estimated_delivery_date&&(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Estimated Delivery:"}),(0,d.jsx)("span",{className:"font-medium",children:new Date(j.estimated_delivery_date).toLocaleDateString()})]}),j.delivery_slot_display&&(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Delivery Slot:"}),(0,d.jsx)("span",{className:"font-medium",children:j.delivery_slot_display})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Delivery Address"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:(0,d.jsx)("p",{children:(a=>"string"==typeof a?a:[a.address_line_1,a.address_line_2,a.city,a.state,a.postal_code].filter(Boolean).join(", "))(j.shipping_address)})})]})]}),j.shipping_cost>0&&(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Shipping Cost:"}),(0,d.jsxs)("span",{className:"font-medium text-lg",children:["₹",j.shipping_cost]})]})})]}),(0,d.jsx)(l,{shipment:j,className:"bg-white border border-gray-200 rounded-lg p-6"})]}),m&&!j&&(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading tracking information..."})]})}),!m&&!j&&!n&&c&&(0,d.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,d.jsx)("p",{className:"text-gray-600",children:"Enter a tracking number or order ID to view shipment details"})]})})]})},n=({sourcePinCode:a="",destinationPinCode:b="",weight:c=0,dimensions:g={},onRateSelect:h,className:j=""})=>{let k=(0,f.wA)(),{shippingRates:l,loading:m,error:n}=(0,f.d4)(a=>a.shipping),[o,p]=(0,e.useState)({source_pin_code:a,destination_pin_code:b,weight:c,dimensions:g}),[q,r]=(0,e.useState)(null),[s,t]=(0,e.useState)(!1);(0,e.useEffect)(()=>{p(d=>({...d,source_pin_code:a,destination_pin_code:b,weight:c,dimensions:g}))},[a,b,c,g]),(0,e.useEffect)(()=>()=>{k((0,i.ET)())},[k]);let u=(a,b)=>{p(c=>({...c,[a]:b}))},v=(a,b)=>{p(c=>({...c,dimensions:{...c.dimensions,[a]:b}}))},w=async a=>{if(a.preventDefault(),o.source_pin_code&&o.destination_pin_code&&o.weight)try{await k((0,i.kn)(o)).unwrap(),r(null)}catch(a){console.error("Failed to calculate shipping rates:",a)}},x=o.source_pin_code&&o.destination_pin_code&&o.weight>0;return(0,d.jsx)("div",{className:`shipping-cost-calculator ${j}`,children:(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Calculate Shipping Cost"}),(0,d.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"source-pin",className:"block text-sm font-medium text-gray-700 mb-1",children:"Source Pin Code"}),(0,d.jsx)("input",{id:"source-pin",type:"text",value:o.source_pin_code,onChange:a=>u("source_pin_code",a.target.value),placeholder:"e.g., 110001",maxLength:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"destination-pin",className:"block text-sm font-medium text-gray-700 mb-1",children:"Destination Pin Code"}),(0,d.jsx)("input",{id:"destination-pin",type:"text",value:o.destination_pin_code,onChange:a=>u("destination_pin_code",a.target.value),placeholder:"e.g., 400001",maxLength:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"weight",className:"block text-sm font-medium text-gray-700 mb-1",children:"Weight (kg)"}),(0,d.jsx)("input",{id:"weight",type:"number",step:"0.1",min:"0.1",value:o.weight,onChange:a=>u("weight",parseFloat(a.target.value)||0),placeholder:"e.g., 1.5",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsx)("div",{children:(0,d.jsxs)("button",{type:"button",onClick:()=>t(!s),className:"text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center",children:[(0,d.jsx)("svg",{className:`w-4 h-4 mr-1 transition-transform ${s?"rotate-90":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),"Advanced Options"]})}),s&&(0,d.jsxs)("div",{className:"space-y-4 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Package Dimensions (cm)"}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"length",className:"block text-sm font-medium text-gray-700 mb-1",children:"Length"}),(0,d.jsx)("input",{id:"length",type:"number",step:"0.1",min:"0",value:o.dimensions?.length||"",onChange:a=>v("length",parseFloat(a.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"width",className:"block text-sm font-medium text-gray-700 mb-1",children:"Width"}),(0,d.jsx)("input",{id:"width",type:"number",step:"0.1",min:"0",value:o.dimensions?.width||"",onChange:a=>v("width",parseFloat(a.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"height",className:"block text-sm font-medium text-gray-700 mb-1",children:"Height"}),(0,d.jsx)("input",{id:"height",type:"number",step:"0.1",min:"0",value:o.dimensions?.height||"",onChange:a=>v("height",parseFloat(a.target.value)||0),placeholder:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)("button",{type:"submit",disabled:!x||m,className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:m?(0,d.jsxs)("div",{className:"flex items-center justify-center",children:[(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Calculating..."]}):"Calculate Rates"}),l.length>0&&(0,d.jsx)("button",{type:"button",onClick:()=>{k((0,i.Zb)()),r(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Clear"})]})]}),n&&(0,d.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,d.jsx)("p",{className:"text-red-700 text-sm",children:n})]})}),l.length>0&&(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-4",children:"Available Shipping Options"}),(0,d.jsx)("div",{className:"space-y-3",children:l.map((a,b)=>(0,d.jsx)("div",{className:`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${q?.shipping_partner.id===a.shipping_partner.id?"border-blue-500 bg-blue-50 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,onClick:()=>(a=>{r(a),h?.(a)})(a),children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`w-4 h-4 rounded-full border-2 mr-3 ${q?.shipping_partner.id===a.shipping_partner.id?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:q?.shipping_partner.id===a.shipping_partner.id&&(0,d.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.shipping_partner.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Delivery: ",(a=>a.estimated_delivery_days?`${a.estimated_delivery_days} days`:a.min_delivery_days&&a.max_delivery_days?`${a.min_delivery_days}-${a.max_delivery_days} days`:"Not specified")(a)]}),a.is_dynamic_rate&&(0,d.jsx)("span",{className:"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-1",children:"Live Rate"})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["₹",a.rate.toFixed(2)]}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"+ taxes"})]})]})},`${a.shipping_partner.id}-${b}`))}),q&&(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Selected Option:"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[q.shipping_partner.name," - ₹",q.rate.toFixed(2)]})]}),(0,d.jsx)("button",{onClick:()=>r(null),className:"text-sm text-red-600 hover:text-red-700 font-medium",children:"Clear Selection"})]})})]}),!m&&0===l.length&&!n&&x&&(0,d.jsx)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-yellow-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),(0,d.jsx)("p",{className:"text-yellow-700",children:'Click "Calculate Rates" to see available shipping options'})]})})]})})},o=({pinCode:a="",onServiceabilityCheck:b,className:c=""})=>{let g=(0,f.wA)(),{serviceableAreas:h,loading:j,error:k}=(0,f.d4)(a=>a.shipping),[l,m]=(0,e.useState)(a),[n,o]=(0,e.useState)(""),[p,q]=(0,e.useState)(null);(0,e.useEffect)(()=>{m(a),a&&a!==n&&r(a)},[a]),(0,e.useEffect)(()=>()=>{g((0,i.ET)())},[g]);let r=async a=>{let c=a||l;if(c&&6===c.length){o(c);try{let a=await g((0,i.i8)({pin_code:c})).unwrap();if(a){let d={serviceable:a.serviceable,areas:a.areas||[],pinCode:c};q(d),b?.(a.serviceable,a.areas)}}catch(a){q({serviceable:!1,areas:[],pinCode:c}),b?.(!1,[])}}};return(0,d.jsx)("div",{className:`serviceability-checker ${c}`,children:(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Check Delivery Availability"}),(0,d.jsx)("form",{onSubmit:a=>{a.preventDefault(),r()},className:"space-y-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"pin-code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Pin Code"}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)("input",{id:"pin-code",type:"text",value:l,onChange:a=>{m(a.target.value.replace(/\D/g,"").slice(0,6))},placeholder:"e.g., 110001",maxLength:6,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,d.jsx)("button",{type:"submit",disabled:j||6!==l.length,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:j?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Checking..."]}):"Check"})]}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pin code to check delivery availability"})]})}),p&&p.pinCode===l&&(0,d.jsx)("div",{className:"mt-6",children:p.serviceable?(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("h4",{className:"text-green-800 font-medium mb-2",children:["Great! We deliver to ",p.pinCode]}),p.areas.length>0&&(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("p",{className:"text-green-700 text-sm mb-3",children:"Available delivery options:"}),p.areas.map((a,b)=>(0,d.jsx)("div",{className:"bg-white border border-green-200 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"font-medium text-gray-900",children:[a.city,", ",a.state]}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.country})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-600",children:(a=>a.min_delivery_days===a.max_delivery_days?`${a.min_delivery_days} day${a.min_delivery_days>1?"s":""}`:`${a.min_delivery_days}-${a.max_delivery_days} days`)(a)}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"delivery time"})]})]})},a.id))]})]})]})}):(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("svg",{className:"w-6 h-6 text-red-500 mr-3 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("h4",{className:"text-red-800 font-medium mb-2",children:["Sorry, we don't deliver to ",p.pinCode]}),(0,d.jsx)("p",{className:"text-red-700 text-sm",children:"This pin code is currently not in our delivery network. Please try a different pin code or contact our support team for assistance."})]})]})})}),k&&(0,d.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,d.jsx)("p",{className:"text-red-700 text-sm",children:k})]})}),(0,d.jsxs)("div",{className:"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-blue-800 font-medium mb-2 flex items-center",children:[(0,d.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Quick Tips"]}),(0,d.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,d.jsx)("li",{children:"• Pin codes are 6-digit numbers (e.g., 110001 for New Delhi)"}),(0,d.jsx)("li",{children:"• Delivery times may vary based on location and shipping partner"}),(0,d.jsx)("li",{children:"• Some areas may have additional delivery charges"}),(0,d.jsx)("li",{children:"• Contact support if your area is not serviceable"})]})]})]})})},p=[{id:"1",type:"HOME",first_name:"John",last_name:"Doe",address_line_1:"123 Main Street",address_line_2:"Apt 4B",city:"New Delhi",state:"Delhi",postal_code:"110001",country:"India",phone:"+91 9876543210",is_default:!0},{id:"2",type:"WORK",first_name:"John",last_name:"Doe",address_line_1:"456 Business Park",city:"Gurgaon",state:"Haryana",postal_code:"122001",country:"India",phone:"+91 9876543210",is_default:!1}],q=()=>{let[a,b]=(0,e.useState)("delivery"),[c,f]=(0,e.useState)("110001");return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Shipping & Delivery"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage your delivery preferences, track orders, and calculate shipping costs"})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm mb-6",children:(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"delivery",label:"Delivery Options",icon:"\uD83D\uDE9A"},{id:"tracking",label:"Order Tracking",icon:"\uD83D\uDCE6"},{id:"calculator",label:"Shipping Calculator",icon:"\uD83D\uDCB0"},{id:"serviceability",label:"Check Serviceability",icon:"\uD83D\uDCCD"}].map(c=>(0,d.jsxs)("button",{onClick:()=>b(c.id),className:`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${a===c.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,d.jsx)("span",{className:"mr-2",children:c.icon}),c.label]},c.id))})})}),(0,d.jsxs)("div",{className:"space-y-6",children:["delivery"===a&&(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{children:(0,d.jsx)(k,{addresses:p,onAddressSelect:a=>{a&&f(a.postal_code)},onAddNewAddress:()=>alert("Add new address functionality would be implemented here"),onEditAddress:a=>alert(`Edit address functionality for ${a.id} would be implemented here`)})}),(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,d.jsx)(j,{pincode:c,onSelect:a=>{console.log("Selected delivery slot:",a)}})})})]}),"tracking"===a&&(0,d.jsx)("div",{children:(0,d.jsx)(m,{showSearch:!0,className:"bg-white"})}),"calculator"===a&&(0,d.jsx)("div",{className:"max-w-4xl",children:(0,d.jsx)(n,{sourcePinCode:"110001",destinationPinCode:c,weight:1.5,onRateSelect:a=>{console.log("Selected shipping rate:",a)}})}),"serviceability"===a&&(0,d.jsx)("div",{className:"max-w-2xl",children:(0,d.jsx)(o,{pinCode:c,onServiceabilityCheck:(a,b)=>{console.log("Serviceability result:",{serviceable:a,areas:b})}})})]}),(0,d.jsxs)("div",{className:"mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Shipping Information"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Delivery Options"}),(0,d.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Standard Delivery (3-5 days)"}),(0,d.jsx)("li",{children:"• Express Delivery (1-2 days)"}),(0,d.jsx)("li",{children:"• Same Day Delivery (selected areas)"}),(0,d.jsx)("li",{children:"• Scheduled Delivery Slots"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Shipping Partners"}),(0,d.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Shiprocket"}),(0,d.jsx)("li",{children:"• Delhivery"}),(0,d.jsx)("li",{children:"• Blue Dart"}),(0,d.jsx)("li",{children:"• DTDC"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Payment Options"}),(0,d.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Cash on Delivery"}),(0,d.jsx)("li",{children:"• Prepaid Orders"}),(0,d.jsx)("li",{children:"• Digital Wallets"}),(0,d.jsx)("li",{children:"• UPI Payments"})]})]})]})]})]})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41321:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Local_ecom\\\\frontend\\\\src\\\\app\\\\shipping\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Local_ecom\\frontend\\src\\app\\shipping\\page.tsx","default")},51907:(a,b,c)=>{"use strict";c.d(b,{p:()=>e});var d=c(60687);function e({label:a,error:b,helperText:c,className:e="",id:f,...g}){let h=f||`input-${Math.random().toString(36).substr(2,9)}`,i=b?`${h}-error`:void 0,j=c?`${h}-helper`:void 0,k=`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors touch-manipulation ${b?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400"} disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed disabled:border-gray-200 ${e}`;return(0,d.jsxs)("div",{className:"space-y-1",children:[a&&(0,d.jsx)("label",{htmlFor:h,className:"block text-sm font-medium text-gray-700",children:a}),(0,d.jsx)("input",{id:h,className:k,"aria-invalid":b?"true":"false","aria-describedby":[i,j].filter(Boolean).join(" ")||void 0,...g}),b&&(0,d.jsx)("p",{id:i,className:"text-sm text-red-600",role:"alert",children:b}),c&&!b&&(0,d.jsx)("p",{id:j,className:"text-sm text-gray-500",children:c})]})}c(43210)},54361:(a,b,c)=>{Promise.resolve().then(c.bind(c,41321))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63547:(a,b,c)=>{"use strict";c.d(b,{R:()=>e});var d=c(60687);function e({size:a="md",text:b,className:c=""}){return(0,d.jsx)("div",{className:`flex items-center justify-center ${c}`,children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsxs)("svg",{className:`animate-spin ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[a]} text-blue-600`,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),b&&(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b})]})})}c(43210)},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90809:(a,b,c)=>{Promise.resolve().then(c.bind(c,35324))},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,503,998],()=>b(b.s=26443));module.exports=c})();