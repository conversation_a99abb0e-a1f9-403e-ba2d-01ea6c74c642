{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Security Dashboard{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .security-dashboard {
        padding: 20px;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .dashboard-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-card h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007cba;
        padding-bottom: 10px;
    }
    
    .metric-value {
        font-size: 2em;
        font-weight: bold;
        color: #007cba;
    }
    
    .severity-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        color: white;
        font-size: 12px;
        font-weight: bold;
        margin-right: 5px;
    }
    
    .severity-critical { background-color: #dc3545; }
    .severity-high { background-color: #fd7e14; }
    .severity-medium { background-color: #ffc107; color: #000; }
    .severity-low { background-color: #28a745; }
    
    .activity-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .activity-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
        font-size: 14px;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .timestamp {
        color: #666;
        font-size: 12px;
    }
    
    .export-buttons {
        margin: 20px 0;
    }
    
    .export-buttons .button {
        margin-right: 10px;
    }
    
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    
    .stats-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .stats-table th,
    .stats-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .stats-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="security-dashboard">
    <h1>Database Security Dashboard</h1>
    
    {% if error %}
        <div class="alert alert-danger">
            <strong>Error:</strong> {{ error }}
        </div>
    {% else %}
        <!-- Summary Cards -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <h3>Open Security Events</h3>
                <div class="metric-value">{{ total_open_events|default:0 }}</div>
                <div style="margin-top: 10px;">
                    {% for severity, count in open_events.items %}
                        <span class="severity-badge severity-{{ severity|lower }}">
                            {{ severity }}: {{ count }}
                        </span>
                    {% empty %}
                        <span style="color: #28a745;">No open security events</span>
                    {% endfor %}
                </div>
            </div>
            
            <div class="dashboard-card">
                <h3>Failed Login Attempts (24h)</h3>
                {% if failed_logins %}
                    <div class="activity-list">
                        {% for user, ip, attempts in failed_logins %}
                            <div class="activity-item">
                                <strong>{{ user }}</strong> from {{ ip }}
                                <span style="color: #dc3545; font-weight: bold;">{{ attempts }} attempts</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div style="color: #28a745;">No suspicious login activity</div>
                {% endif %}
            </div>
            
            <div class="dashboard-card">
                <h3>Top Database Users (24h)</h3>
                {% if top_users %}
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Activity Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user, count in top_users %}
                                <tr>
                                    <td>{{ user }}</td>
                                    <td>{{ count }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div>No database activity recorded</div>
                {% endif %}
            </div>
            
            <div class="dashboard-card">
                <h3>Recent Suspicious Activities</h3>
                {% if suspicious_activities %}
                    <div class="activity-list">
                        {% for timestamp, event_type, user, ip, description in suspicious_activities %}
                            <div class="activity-item">
                                <div><strong>{{ event_type }}</strong></div>
                                <div>{{ description|truncatechars:80 }}</div>
                                <div>User: {{ user }} | IP: {{ ip }}</div>
                                <div class="timestamp">{{ timestamp|date:"Y-m-d H:i:s" }}</div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div style="color: #28a745;">No recent suspicious activities</div>
                {% endif %}
            </div>
        </div>
        
        <!-- Export and Report Buttons -->
        <div class="export-buttons">
            <h3>Reports and Exports</h3>
            <a href="{% url 'admin:export_audit_logs' %}" class="button">Export Audit Logs</a>
            <a href="{% url 'admin:security_report' %}" class="button">Security Summary Report</a>
            <button onclick="refreshDashboard()" class="button">Refresh Dashboard</button>
        </div>
        
        <!-- Quick Actions -->
        <div class="dashboard-card">
            <h3>Quick Actions</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="{% url 'admin:core_auditlogproxy_changelist' %}" class="button">View All Audit Logs</a>
                <a href="{% url 'admin:core_securityeventproxy_changelist' %}" class="button">View Security Events</a>
                <button onclick="runSecurityScan()" class="button">Run Security Scan</button>
                <button onclick="testDatabaseSecurity()" class="button">Test Database Security</button>
            </div>
        </div>
    {% endif %}
</div>

<script>
function refreshDashboard() {
    window.location.reload();
}

function runSecurityScan() {
    if (confirm('Run a comprehensive security scan? This may take a few minutes.')) {
        fetch('/admin/run-security-scan/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Security scan completed successfully. Check security events for results.');
                refreshDashboard();
            } else {
                alert('Security scan failed: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error running security scan: ' + error);
        });
    }
}

function testDatabaseSecurity() {
    if (confirm('Test database security configuration?')) {
        fetch('/admin/test-database-security/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Database security test completed. Results:\n' + JSON.stringify(data.results, null, 2));
            } else {
                alert('Database security test failed: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error testing database security: ' + error);
        });
    }
}

function showAuditDetails(auditId) {
    // This would open a modal or navigate to detailed view
    window.open('/admin/audit-log-detail/' + auditId + '/', '_blank');
}

function resolveSecurityEvent(eventId) {
    if (confirm('Mark this security event as resolved?')) {
        fetch('/admin/resolve-security-event/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({event_id: eventId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Security event marked as resolved.');
                refreshDashboard();
            } else {
                alert('Failed to resolve security event: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error resolving security event: ' + error);
        });
    }
}

// Auto-refresh dashboard every 5 minutes
setInterval(refreshDashboard, 300000);
</script>
{% endblock %}