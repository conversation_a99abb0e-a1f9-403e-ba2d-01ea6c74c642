# MySQL Production Configuration
# This file should be placed at /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql
skip-external-locking

# Network Settings
bind-address = 0.0.0.0
max_connections = 500
max_connect_errors = 1000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800
max_allowed_packet = 64M
thread_cache_size = 50
sort_buffer_size = 4M
bulk_insert_buffer_size = 16M
tmp_table_size = 32M
max_heap_table_size = 32M

# MyISAM Settings
myisam_recover_options = BACKUP
key_buffer_size = 128M
table_open_cache = 400
myisam_sort_buffer_size = 512M
concurrent_insert = 2
read_buffer_size = 2M
read_rnd_buffer_size = 1M

# InnoDB Settings
innodb_buffer_pool_size = 4G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 512M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 50
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_io_capacity_max = 2000
innodb_thread_concurrency = 0
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_purge_threads = 4
innodb_page_cleaners = 4

# Query Cache Settings
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M
query_cache_min_res_unit = 2k

# Logging Settings
general_log = 1
general_log_file = /var/log/mysql/mysql.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1
log_slow_slave_statements = 1
log_throttle_queries_not_using_indexes = 10
expire_logs_days = 10
max_binlog_size = 100M

# Binary Logging (for replication and point-in-time recovery)
server-id = 1
log-bin = mysql-bin
binlog_format = ROW
binlog_do_db = ecommerce_prod
binlog_cache_size = 1M
sync_binlog = 1

# Security Settings
ssl-ca = /etc/mysql/ssl/ca-cert.pem
ssl-cert = /etc/mysql/ssl/server-cert.pem
ssl-key = /etc/mysql/ssl/server-key.pem
require_secure_transport = ON
local_infile = 0
secure_file_priv = /var/lib/mysql-files/

# Character Set and Collation
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# Performance Schema
performance_schema = ON
performance_schema_max_table_instances = 400
performance_schema_max_table_handles = 4000

# Error Logging
log-error = /var/log/mysql/error.log
log_warnings = 2

# Replication Settings (if setting up replication)
# relay-log = mysql-relay-bin
# relay-log-index = mysql-relay-bin.index
# relay-log-info-file = mysql-relay-bin.info
# master-info-repository = TABLE
# relay-log-info-repository = TABLE
# relay-log-recovery = ON

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
ssl-ca = /etc/mysql/ssl/ca-cert.pem
ssl-cert = /etc/mysql/ssl/client-cert.pem
ssl-key = /etc/mysql/ssl/client-key.pem

[mysqldump]
quick
quote-names
max_allowed_packet = 64M
single-transaction
routines
triggers

[mysql_safe]
log-error = /var/log/mysql/error.log
pid-file = /var/run/mysqld/mysqld.pid

[mysqladmin]
ssl-ca = /etc/mysql/ssl/ca-cert.pem
ssl-cert = /etc/mysql/ssl/client-cert.pem
ssl-key = /etc/mysql/ssl/client-key.pem