"use strict";exports.id=595,exports.ids=[595],exports.modules={43595:(a,b,c)=>{c.d(b,{F:()=>o});var d=c(60687),e=c(76180),f=c.n(e),g=c(43210),h=c(27605),i=c(2643),j=c(84924),k=c(88920),l=c(92748),m=c(4356),n=c(46003);let o=({token:a,onSuccess:b,onBackToLogin:c})=>{let[e,o]=(0,g.useState)(!1),[r,s]=(0,g.useState)(!1),[t,u]=(0,g.useState)(null),[v,w]=(0,g.useState)(null),[x,y]=(0,g.useState)(!0),{register:z,handleSubmit:A,formState:{errors:B},watch:C,reset:D}=(0,h.mN)(),E=C("password"),{markTokenUsed:F,isTokenValid:G}=(0,m.xf)(),{onPasswordResetSuccess:H,onPasswordResetFailure:I}=(0,n.mO)();(0,g.useEffect)(()=>{let b=async()=>{try{y(!0);let b=await j.Z.validateResetToken(a);b.success&&b.data?.valid?w(!0):(w(!1),u({message:b.data?.expired?"This password reset link has expired. Please request a new one.":"This password reset link is invalid. Please request a new one.",code:b.data?.expired?"token_expired":"token_invalid",canRetry:!1}))}catch(c){let b=(0,k.qQ)(c);(0,l.tb)(c,"validateToken",{token:a.substring(0,8)+"..."}),(0,l.n0)("validate",{token:a,success:!1,errorCode:b.code}),w(!1),u({message:(0,l.E1)(c),code:b.code,canRetry:(0,l.lX)(b.code)})}finally{y(!1)}};a?b():(w(!1),y(!1))},[a]);let J=async c=>{o(!0),u(null);try{let d=await j.Z.resetPassword(a,c.password);if(d.success)(0,l.n0)("reset",{token:a,success:!0}),F(a),H("",a),s(!0),b?.();else{let b=(0,k.qQ)(d.error);(0,l.tb)(d.error,"onSubmit",{token:a.substring(0,8)+"..."}),(0,l.n0)("reset",{token:a,success:!1,errorCode:b.code}),I("",b.code,a),u({message:(0,l.E1)(d.error),code:b.code,canRetry:(0,l.lX)(b.code)})}}catch(c){let b=(0,k.qQ)(c);(0,l.tb)(c,"onSubmit",{token:a.substring(0,8)+"..."}),(0,l.n0)("reset",{token:a,success:!1,errorCode:b.code}),u({message:(0,l.E1)(c),code:b.code,canRetry:(0,l.lX)(b.code)})}finally{o(!1)}},K=async()=>{if(!1===v){y(!0),u(null);try{let b=await j.Z.validateResetToken(a);b.success&&b.data?.valid?w(!0):(w(!1),u({message:b.data?.expired?"This password reset link has expired. Please request a new one.":"This password reset link is invalid. Please request a new one.",code:b.data?.expired?"token_expired":"token_invalid",canRetry:!1}))}catch(c){let b=(0,k.qQ)(c);(0,l.tb)(c,"handleRetry",{token:a.substring(0,8)+"..."}),u({message:(0,l.E1)(c),code:b.code,canRetry:(0,l.lX)(b.code)})}finally{y(!1)}}};return x?(0,d.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",textAlign:"center"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #2196f3",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto 24px auto"},className:"jsx-7a90291f0de70b21"}),(0,d.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"Validating Reset Link"}),(0,d.jsx)("p",{style:{color:"#757575",fontSize:"14px"},className:"jsx-7a90291f0de70b21",children:"Please wait while we verify your password reset link..."}),(0,d.jsx)(f(),{id:"7a90291f0de70b21",children:"@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}"})]}):r?(0,d.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,d.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,d.jsx)("div",{style:{width:"80px",height:"80px",backgroundColor:"#4caf50",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px auto",boxShadow:"0 4px 16px rgba(76, 175, 80, 0.3)"},children:(0,d.jsx)("svg",{style:{width:"40px",height:"40px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,d.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"12px"},children:"Password Reset Successful!"}),(0,d.jsx)("p",{style:{color:"#757575",fontSize:"16px",lineHeight:"1.6",marginBottom:"24px"},children:"Your password has been successfully updated. You can now log in with your new password."})]}),(0,d.jsx)(i.$,{onClick:c,style:{width:"100%",backgroundColor:"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600"},children:"Continue to Login"})]}):!1===v?(0,d.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,d.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,d.jsx)("div",{style:{width:"80px",height:"80px",backgroundColor:"#f44336",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px auto",boxShadow:"0 4px 16px rgba(244, 67, 54, 0.3)"},children:(0,d.jsx)("svg",{style:{width:"40px",height:"40px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"12px"},children:"Invalid Reset Link"}),(0,d.jsx)("p",{style:{color:"#757575",fontSize:"16px",lineHeight:"1.6",marginBottom:"24px"},children:t?.message||"This password reset link is invalid or has expired."})]}),t?.canRetry&&(0,d.jsx)(i.$,{onClick:K,variant:"outline",style:{width:"100%",marginBottom:"16px"},children:"Try Again"}),(0,d.jsx)(i.$,{onClick:c,style:{width:"100%",backgroundColor:"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600"},children:"Back to Login"})]}):(0,d.jsxs)("div",{style:{maxWidth:"400px",margin:"0 auto",padding:"32px",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},children:[(0,d.jsxs)("div",{style:{textAlign:"center",marginBottom:"32px"},children:[(0,d.jsx)("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#212121",marginBottom:"8px"},children:"Reset Your Password"}),(0,d.jsx)("p",{style:{color:"#757575",fontSize:"14px"},children:"Enter your new password below. Make sure it's strong and secure."})]}),(0,d.jsxs)("form",{onSubmit:A(J),className:"jsx-7a90291f0de70b21",children:[(0,d.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"New Password"}),(0,d.jsx)("input",{type:"password",...z("password",{required:"Password is required",validate:a=>a.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])/.test(a)?/(?=.*[A-Z])/.test(a)?/(?=.*\d)/.test(a)?!!/(?=.*[@$!%*?&])/.test(a)||"Password must contain at least one special character (@$!%*?&)":"Password must contain at least one number":"Password must contain at least one uppercase letter":"Password must contain at least one lowercase letter"}),style:{width:"100%",padding:"14px 16px",border:`2px solid ${B.password?"#f44336":"#e0e0e0"}`,borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s, box-shadow 0.2s",boxSizing:"border-box",backgroundColor:B.password?"#ffeaea":"white"},placeholder:"Enter your new password",disabled:e,onFocus:a=>{B.password||(a.target.style.borderColor="#2196f3",a.target.style.boxShadow="0 0 0 3px rgba(33, 150, 243, 0.1)")},onBlur:a=>{B.password||(a.target.style.borderColor="#e0e0e0",a.target.style.boxShadow="none")},className:"jsx-7a90291f0de70b21"}),B.password&&(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginTop:"8px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("svg",{style:{width:"16px",height:"16px",color:"#f44336",flexShrink:0},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})}),(0,d.jsx)("p",{style:{color:"#f44336",fontSize:"13px",margin:0,fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:B.password.message})]}),E&&(0,d.jsxs)("div",{style:{marginTop:"12px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("div",{style:{fontSize:"12px",fontWeight:"500",color:"#757575",marginBottom:"6px"},className:"jsx-7a90291f0de70b21",children:"Password Strength"}),(0,d.jsx)("div",{style:{display:"flex",gap:"4px",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:[1,2,3,4].map(a=>{let b=p(E);return(0,d.jsx)("div",{style:{flex:1,height:"4px",borderRadius:"2px",backgroundColor:b>=a?1===b?"#f44336":2===b?"#ff9800":3===b?"#2196f3":"#4caf50":"#e0e0e0"},className:"jsx-7a90291f0de70b21"},a)})}),(0,d.jsx)("div",{style:{fontSize:"11px",color:"#757575"},className:"jsx-7a90291f0de70b21",children:q(p(E))})]})]}),(0,d.jsxs)("div",{style:{marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#212121",marginBottom:"8px"},className:"jsx-7a90291f0de70b21",children:"Confirm New Password"}),(0,d.jsx)("input",{type:"password",...z("confirmPassword",{required:"Please confirm your password",validate:a=>a===E||"Passwords do not match"}),style:{width:"100%",padding:"14px 16px",border:`2px solid ${B.confirmPassword?"#f44336":"#e0e0e0"}`,borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s, box-shadow 0.2s",boxSizing:"border-box",backgroundColor:B.confirmPassword?"#ffeaea":"white"},placeholder:"Confirm your new password",disabled:e,onFocus:a=>{B.confirmPassword||(a.target.style.borderColor="#2196f3",a.target.style.boxShadow="0 0 0 3px rgba(33, 150, 243, 0.1)")},onBlur:a=>{B.confirmPassword||(a.target.style.borderColor="#e0e0e0",a.target.style.boxShadow="none")},className:"jsx-7a90291f0de70b21"}),B.confirmPassword&&(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginTop:"8px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("svg",{style:{width:"16px",height:"16px",color:"#f44336",flexShrink:0},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})}),(0,d.jsx)("p",{style:{color:"#f44336",fontSize:"13px",margin:0,fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:B.confirmPassword.message})]})]}),t&&(0,d.jsx)("div",{style:{backgroundColor:"network_error"===t.code?"#fff3cd":"#ffebee",border:`1px solid ${"network_error"===t.code?"#ffeaa7":"#ffcdd2"}`,borderRadius:"8px",padding:"16px",marginBottom:"24px"},className:"jsx-7a90291f0de70b21",children:(0,d.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("div",{style:{width:"20px",height:"20px",flexShrink:0,marginTop:"2px"},className:"jsx-7a90291f0de70b21",children:(0,d.jsx)("svg",{style:{width:"20px",height:"20px",color:"network_error"===t.code?"#856404":"#c62828"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"jsx-7a90291f0de70b21",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",className:"jsx-7a90291f0de70b21"})})}),(0,d.jsxs)("div",{style:{flex:1},className:"jsx-7a90291f0de70b21",children:[(0,d.jsx)("p",{style:{color:"network_error"===t.code?"#856404":"#c62828",fontSize:"14px",margin:"0 0 8px 0",fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:t.message}),t.canRetry&&(0,d.jsx)("button",{onClick:K,disabled:e,style:{background:"none",border:"none",color:"network_error"===t.code?"#856404":"#c62828",fontSize:"13px",cursor:e?"not-allowed":"pointer",textDecoration:"underline",padding:"0",fontWeight:"500"},className:"jsx-7a90291f0de70b21",children:e?"Retrying...":"Try Again"})]})]})}),(0,d.jsxs)(i.$,{type:"submit",disabled:e,style:{width:"100%",marginBottom:"16px",backgroundColor:e?"#90caf9":"#2196f3",color:"white",padding:"14px",fontSize:"16px",fontWeight:"600",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",cursor:e?"not-allowed":"pointer",transition:"background-color 0.2s ease"},children:[e&&(0,d.jsx)("div",{style:{width:"16px",height:"16px",border:"2px solid transparent",borderTop:"2px solid white",borderRadius:"50%",animation:"spin 1s linear infinite"},className:"jsx-7a90291f0de70b21"}),e?"Resetting Password...":"Reset Password"]}),(0,d.jsx)(f(),{id:"7a90291f0de70b21",children:"@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}"}),(0,d.jsx)("div",{style:{textAlign:"center"},className:"jsx-7a90291f0de70b21",children:(0,d.jsx)("button",{type:"button",onClick:c,style:{background:"none",border:"none",color:"#2196f3",fontSize:"14px",cursor:"pointer",textDecoration:"underline"},className:"jsx-7a90291f0de70b21",children:"Back to Login"})})]})]})},p=a=>{let b=0;return a.length>=8&&b++,/(?=.*[a-z])/.test(a)&&b++,/(?=.*[A-Z])/.test(a)&&b++,/(?=.*\d)/.test(a)&&b++,/(?=.*[@$!%*?&])/.test(a)&&b++,Math.min(b,4)},q=a=>{switch(a){case 0:case 1:return"Weak - Add more characters and variety";case 2:return"Fair - Add uppercase, numbers, or symbols";case 3:return"Good - Consider adding more variety";case 4:return"Strong - Great password!";default:return""}}}};