"use strict";exports.id=677,exports.ids=[677],exports.modules={2643:(a,b,c)=>{c.d(b,{$:()=>e,A:()=>f});var d=c(60687);c(43210);let e=({variant:a="primary",size:b="md",loading:c=!1,disabled:e,children:f,className:g="",...h})=>(0,d.jsxs)("button",{className:`inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 touch-manipulation select-none ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300",outline:"border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400 disabled:border-gray-200",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-blue-500 disabled:text-gray-400 disabled:hover:bg-transparent"}[a]} ${{sm:"px-3 py-2 text-sm min-h-[36px]",md:"px-4 py-2 text-sm min-h-[44px]",lg:"px-6 py-3 text-base min-h-[48px]"}[b]} disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none ${g}`,disabled:e||c,...h,children:[c&&(0,d.jsx)("div",{className:"animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"}),f]}),f=e},4356:(a,b,c)=>{c.d(b,{vC:()=>g,xf:()=>h});let d={maxTokenAge:36e5,cleanupInterval:9e5,maxStoredTokens:100};class e{constructor(a=d){this.cleanupTimer=null,this.config=a}startCleanup(){this.cleanupTimer&&this.stopCleanup(),this.cleanupTimer=setInterval(()=>{this.performCleanup()},this.config.cleanupInterval),this.performCleanup()}stopCleanup(){this.cleanupTimer&&(clearInterval(this.cleanupTimer),this.cleanupTimer=null)}performCleanup(){try{let a=this.getStoredTokens(),b=Date.now(),c=a.filter(a=>b-a.timestamp<this.config.maxTokenAge&&!a.used).sort((a,b)=>b.timestamp-a.timestamp).slice(0,this.config.maxStoredTokens);this.setStoredTokens(c);let d=a.length-c.length;d>0&&console.log(`Token cleanup: Removed ${d} expired/used tokens`)}catch(a){console.error("Error during token cleanup:",a)}}storeToken(a,b){try{let c=this.getStoredTokens().filter(b=>b.token!==a),d={token:a,timestamp:Date.now(),used:!1,email:b};c.push(d);let e=c.sort((a,b)=>b.timestamp-a.timestamp).slice(0,this.config.maxStoredTokens);this.setStoredTokens(e)}catch(a){console.error("Error storing token:",a)}}markTokenAsUsed(a){try{let b=this.getStoredTokens(),c=b.find(b=>b.token===a);c&&(c.used=!0,this.setStoredTokens(b))}catch(a){console.error("Error marking token as used:",a)}}isTokenValid(a){try{let b=this.getStoredTokens().find(b=>b.token===a);if(!b||b.used)return!1;return Date.now()-b.timestamp<this.config.maxTokenAge}catch(a){return console.error("Error checking token validity:",a),!1}}getTokenInfo(a){try{return this.getStoredTokens().find(b=>b.token===a)||null}catch(a){return console.error("Error getting token info:",a),null}}getCleanupStats(){try{let a=this.getStoredTokens(),b=Date.now(),c=0,d=0,e=0,f=null,g=null;return a.forEach(a=>{let h=b-a.timestamp;a.used?e++:h>=this.config.maxTokenAge?d++:c++,(null===f||a.timestamp<f)&&(f=a.timestamp),(null===g||a.timestamp>g)&&(g=a.timestamp)}),{totalTokens:a.length,validTokens:c,expiredTokens:d,usedTokens:e,oldestToken:f,newestToken:g}}catch(a){return console.error("Error getting cleanup stats:",a),{totalTokens:0,validTokens:0,expiredTokens:0,usedTokens:0,oldestToken:null,newestToken:null}}}clearAllTokens(){try{this.setStoredTokens([]),console.log("All password reset tokens cleared")}catch(a){console.error("Error clearing all tokens:",a)}}getStoredTokens(){return[]}setStoredTokens(a){}}let f=new e,g={storeResetToken:(a,b)=>{f.storeToken(a,b)},markTokenUsed:a=>{f.markTokenAsUsed(a)},isTokenValid:a=>f.isTokenValid(a),getTokenInfo:a=>f.getTokenInfo(a),getStats:()=>f.getCleanupStats(),forceCleanup:()=>{f.performCleanup()},clearAll:()=>{f.clearAllTokens()}},h=()=>({storeToken:g.storeResetToken,markTokenUsed:g.markTokenUsed,isTokenValid:g.isTokenValid,getTokenInfo:g.getTokenInfo,getStats:g.getStats,forceCleanup:g.forceCleanup})},16959:(a,b,c)=>{c.r(b),c.d(b,{SUSPICIOUS_PATTERNS:()=>d,getPerformanceMetrics:()=>n,getSecurityMetrics:()=>i,logSecurityEvent:()=>g,recordPerformanceMetric:()=>m,shouldBlockIP:()=>j,withPerformanceMonitoring:()=>o});let d={RAPID_RESET_REQUESTS:{type:"rapid_requests",threshold:5,timeWindow:3e5,description:"Multiple password reset requests in short time"},INVALID_TOKEN_ATTEMPTS:{type:"invalid_tokens",threshold:10,timeWindow:6e5,description:"Multiple invalid token validation attempts"},EMAIL_ENUMERATION:{type:"email_enumeration",threshold:20,timeWindow:9e5,description:"Potential email enumeration attack"},BRUTE_FORCE_TOKENS:{type:"brute_force",threshold:50,timeWindow:18e5,description:"Potential brute force token attack"}};class e{addEvent(a){this.events.push(a),this.events.length>this.maxEvents&&(this.events=this.events.slice(-this.maxEvents)),this.checkSuspiciousActivity(a)}getRecentEvents(a=36e5){let b=Date.now()-a;return this.events.filter(a=>new Date(a.timestamp).getTime()>b)}getEventsByType(a,b=36e5){return this.getRecentEvents(b).filter(b=>b.event===a)}getEventsByIP(a,b=36e5){return this.getRecentEvents(b).filter(b=>b.ipAddress===a)}checkSuspiciousActivity(a){let b=a.ipAddress;b&&Object.entries(d).forEach(([a,c])=>{let d=this.getEventsByIP(b,c.timeWindow),e=this.filterEventsByPattern(d,c);e.length>=c.threshold&&this.triggerSuspiciousActivityAlert(a,c,b,e)})}filterEventsByPattern(a,b){switch(b.type){case"rapid_requests":return a.filter(a=>"password_reset_request"===a.event);case"invalid_tokens":return a.filter(a=>"token_validation"===a.event&&!1===a.details.success);case"email_enumeration":return a.filter(a=>"password_reset_request"===a.event);case"brute_force":return a.filter(a=>"token_validation"===a.event&&!1===a.details.success);default:return[]}}triggerSuspiciousActivityAlert(a,b,c,d){let e={type:"suspicious_activity",event:"pattern_detected",timestamp:new Date().toISOString(),severity:"high",details:{pattern:a,description:b.description,threshold:b.threshold,actualCount:d.length,timeWindow:b.timeWindow,affectedIP:c,eventSample:d.slice(0,5)},ipAddress:c};this.addEvent(e),this.notifyAdministrators(e)}notifyAdministrators(a){}constructor(){this.events=[],this.maxEvents=1e4}}let f=new e,g=(a,b,c="low",d={},e)=>{let g={type:a,event:b,timestamp:new Date().toISOString(),severity:c,details:{...d,...h(d)},userAgent:e?.userAgent,ipAddress:e?.ipAddress,sessionId:e?.sessionId};f.addEvent(g)},h=a=>{let b={...a};if(b.email&&"string"==typeof b.email){let[a,c]=b.email.split("@");b.email=`${a.substring(0,2)}***@${c}`}return b.token&&"string"==typeof b.token&&(b.token=`${b.token.substring(0,8)}...`),delete b.password,delete b.newPassword,delete b.confirmPassword,b},i=(a=36e5)=>{let b=f.getRecentEvents(a),c={totalEvents:b.length,eventsByType:{},eventsBySeverity:{},suspiciousActivityCount:0,topIPs:{},recentAlerts:[]};return b.forEach(a=>{c.eventsByType[a.event]=(c.eventsByType[a.event]||0)+1,c.eventsBySeverity[a.severity]=(c.eventsBySeverity[a.severity]||0)+1,"suspicious_activity"===a.type&&(c.suspiciousActivityCount++,c.recentAlerts.push(a)),a.ipAddress&&(c.topIPs[a.ipAddress]=(c.topIPs[a.ipAddress]||0)+1)}),c.recentAlerts.sort((a,b)=>new Date(b.timestamp).getTime()-new Date(a.timestamp).getTime()),c},j=a=>f.getEventsByIP(a,18e5).filter(a=>"high"===a.severity||"critical"===a.severity).length>0;class k{recordMetric(a){this.metrics.push(a),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics))}getAverageResponseTime(a,b=36e5){let c=Date.now()-b,d=this.metrics.filter(b=>b.operation===a&&new Date(b.timestamp).getTime()>c);return 0===d.length?0:d.reduce((a,b)=>a+b.duration,0)/d.length}getSuccessRate(a,b=36e5){let c=Date.now()-b,d=this.metrics.filter(b=>b.operation===a&&new Date(b.timestamp).getTime()>c);return 0===d.length?0:d.filter(a=>a.success).length/d.length*100}constructor(){this.metrics=[],this.maxMetrics=5e3}}let l=new k,m=(a,b,c,d)=>{let e=Date.now()-b;l.recordMetric({operation:a,duration:e,timestamp:new Date().toISOString(),success:c,details:d}),e>5e3&&g("password_reset","slow_operation","medium",{operation:a,duration:e,success:c,...d})},n=()=>["email_send","token_validation","password_reset"].reduce((a,b)=>(a[b]={averageResponseTime:l.getAverageResponseTime(b),successRate:l.getSuccessRate(b)},a),{}),o=async(a,b,c)=>{let d=Date.now(),e=!1;try{let a=await b();return e=!0,a}catch(a){throw e=!1,a}finally{m(a,d,e,c)}}},46003:(a,b,c)=>{c.d(b,{mO:()=>g});var d=c(16959),e=c(4356);let f={onPasswordResetSuccess:(a,b)=>{e.vC.markTokenUsed(b),(0,d.logSecurityEvent)("password_reset","password_reset_completed","low",{email:a.substring(0,3)+"***@"+a.split("@")[1],success:!0,timestamp:new Date().toISOString()})},onPasswordResetRequest:a=>{(0,d.logSecurityEvent)("password_reset","password_reset_requested","low",{email:a.substring(0,3)+"***@"+a.split("@")[1],timestamp:new Date().toISOString()})},onPasswordResetFailure:(a,b,c)=>{(0,d.logSecurityEvent)("password_reset","password_reset_failed","medium",{email:a?a.substring(0,3)+"***@"+a.split("@")[1]:void 0,errorCode:b,token:c?c.substring(0,8)+"...":void 0,timestamp:new Date().toISOString()})},getPostLoginRedirect:()=>"/",setPostLoginRedirect:a=>{},isSessionValid:()=>!1,clearAuthData:()=>{},getCurrentUser:()=>null,updateUserPassword:a=>{},isPasswordResetAvailable:()=>!1,getPasswordResetStats:()=>({tokenStats:e.vC.getStats(),isSystemAvailable:f.isPasswordResetAvailable(),currentUser:f.getCurrentUser(),sessionValid:f.isSessionValid()})},g=()=>({onPasswordResetSuccess:f.onPasswordResetSuccess,onPasswordResetRequest:f.onPasswordResetRequest,onPasswordResetFailure:f.onPasswordResetFailure,isSessionValid:f.isSessionValid,getCurrentUser:f.getCurrentUser,clearAuthData:f.clearAuthData,getPostLoginRedirect:f.getPostLoginRedirect,setPostLoginRedirect:f.setPostLoginRedirect})},84924:(a,b,c)=>{c.d(b,{Z:()=>g});var d=c(17327),e=c(7791),f=c(16959);let g={requestPasswordReset:async a=>(0,f.withPerformanceMonitoring)("password_reset_request",async()=>{try{return await d.uE.post(e.Sn.AUTH.FORGOT_PASSWORD,{email:a})}catch(a){return{success:!1,error:{message:"Failed to request password reset",code:"request_failed",status_code:500}}}},{email:a.substring(0,3)+"***"}),validateResetToken:async a=>(0,f.withPerformanceMonitoring)("token_validation",async()=>{try{return await d.uE.get(e.Sn.AUTH.VALIDATE_RESET_TOKEN(a))}catch(a){return{success:!1,error:{message:"Failed to validate reset token",code:"validation_failed",status_code:500}}}},{token:a.substring(0,8)+"..."}),resetPassword:async(a,b)=>(0,f.withPerformanceMonitoring)("password_reset",async()=>{try{return await d.uE.post(e.Sn.AUTH.RESET_PASSWORD,{token:a,password:b})}catch(a){return{success:!1,error:{message:"Failed to reset password",code:"reset_failed",status_code:500}}}},{token:a.substring(0,8)+"..."})}},88920:(a,b,c)=>{c(37590)},92748:(a,b,c)=>{c.d(b,{E1:()=>h,lX:()=>g,n0:()=>j,tb:()=>i});var d=c(88920);let e={TOKEN_INVALID:"token_invalid",TOKEN_EXPIRED:"token_expired",TOKEN_NOT_FOUND:"token_not_found",TOKEN_ALREADY_USED:"token_already_used",EMAIL_NOT_FOUND:"email_not_found",EMAIL_SEND_FAILED:"email_send_failed",EMAIL_INVALID_FORMAT:"email_invalid_format",PASSWORD_TOO_WEAK:"password_too_weak",PASSWORD_MISMATCH:"password_mismatch",PASSWORD_SAME_AS_OLD:"password_same_as_old",RATE_LIMIT_EXCEEDED:"rate_limit_exceeded",TOO_MANY_ATTEMPTS:"too_many_attempts",NETWORK_ERROR:"network_error",SERVER_ERROR:"server_error",VALIDATION_ERROR:"validation_error"},f={[e.TOKEN_INVALID]:"This password reset link is invalid. Please request a new one.",[e.TOKEN_EXPIRED]:"This password reset link has expired. Please request a new one.",[e.TOKEN_NOT_FOUND]:"This password reset link is not valid. Please request a new one.",[e.TOKEN_ALREADY_USED]:"This password reset link has already been used. Please request a new one if needed.",[e.EMAIL_NOT_FOUND]:"If an account with this email exists, you will receive a password reset link.",[e.EMAIL_SEND_FAILED]:"We encountered an issue sending the reset email. Please try again.",[e.EMAIL_INVALID_FORMAT]:"Please enter a valid email address.",[e.PASSWORD_TOO_WEAK]:"Password must be at least 8 characters with uppercase, lowercase, number, and special character.",[e.PASSWORD_MISMATCH]:"Passwords do not match. Please try again.",[e.PASSWORD_SAME_AS_OLD]:"New password must be different from your current password.",[e.RATE_LIMIT_EXCEEDED]:"Too many password reset requests. Please wait before trying again.",[e.TOO_MANY_ATTEMPTS]:"Too many failed attempts. Please wait 15 minutes before trying again.",[e.NETWORK_ERROR]:"Unable to connect to the server. Please check your internet connection and try again.",[e.SERVER_ERROR]:"A server error occurred. Please try again later.",[e.VALIDATION_ERROR]:"Please check your input and try again."},g=a=>[e.NETWORK_ERROR,e.SERVER_ERROR,e.EMAIL_SEND_FAILED].includes(a),h=a=>{let b=(0,d.extractErrorInfo)(a);return b.code in f?f[b.code]:(0,d.getDisplayErrorMessage)(a)},i=(a,b,c)=>{(0,d.extractErrorInfo)(a),(0,d.logError)(a,`PasswordReset.${b}`)},j=(a,b)=>{let{logSecurityEvent:d}=c(16959);d("password_reset",`password_reset_${a}`,!1===b.success&&b.errorCode?"medium":"low",{success:b.success,errorCode:b.errorCode,email:b.email,token:b.token,...b.additionalData},{ipAddress:b.ipAddress,userAgent:b.userAgent})}}};