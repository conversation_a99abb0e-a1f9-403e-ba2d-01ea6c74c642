# Production Environment Configuration Template
# Copy this file to .env.production and fill in the actual values

# D<PERSON>go Settings
DJANGO_SETTINGS_MODULE=ecommerce_project.settings.production
SECRET_KEY=your_secret_key_here_change_this_in_production
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,your-server-ip

# Database Configuration
DB_NAME=ecommerce_prod
DB_USER=ecommerce_app
DB_PASSWORD=your_strong_password_here
DB_HOST=localhost
DB_PORT=3306

# Read Replica Configuration
DB_READ_USER=ecommerce_read
DB_READ_PASSWORD=your_read_password_here
DB_READ_HOST=localhost
DB_READ_PORT=3306

# Backup Configuration
BACKUP_DB_USER=ecommerce_backup
BACKUP_DB_PASSWORD=your_backup_password_here
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key_here
BACKUP_STORAGE_PATH=/var/backups/mysql
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key
BACKUP_S3_REGION=us-east-1

# SSL Configuration
SSL_CA_CERT=/etc/mysql/ssl/ca-cert.pem
SSL_CLIENT_CERT=/etc/mysql/ssl/client-cert.pem
SSL_CLIENT_KEY=/etc/mysql/ssl/client-key.pem

# Redis Configuration (if using)
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_URL=redis://localhost:6379/1

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/django/ecommerce.log
DB_LOG_FILE_PATH=/var/log/django/database.log

# Monitoring Configuration
MONITORING_USER=exporter
MONITORING_PASSWORD=monitoring_password
MONITORING_PORT=9104

# Application Specific Settings
MEDIA_ROOT=/var/www/media
STATIC_ROOT=/var/www/static
MEDIA_URL=/media/
STATIC_URL=/static/

# Third-party Service Configuration
# Add your third-party service configurations here
# STRIPE_PUBLIC_KEY=pk_live_...
# STRIPE_SECRET_KEY=sk_live_...
# PAYPAL_CLIENT_ID=...
# PAYPAL_CLIENT_SECRET=...

# Celery Configuration (if using)
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/3

# Sentry Configuration (if using)
# SENTRY_DSN=https://<EMAIL>/project-id