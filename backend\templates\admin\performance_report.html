{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Database Performance Report{% endblock %}

{% block extrahead %}
<style>
    .performance-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .performance-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .performance-card h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007cba;
        padding-bottom: 10px;
    }
    
    .metric-display {
        text-align: center;
        padding: 20px;
    }
    
    .metric-value {
        font-size: 2.5em;
        font-weight: bold;
        color: #007cba;
        display: block;
        margin-bottom: 10px;
    }
    
    .metric-label {
        color: #666;
        font-size: 0.9em;
    }
    
    .metric-trend {
        font-size: 0.8em;
        margin-top: 5px;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    .trend-stable {
        color: #6c757d;
    }
    
    .performance-chart {
        height: 200px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        margin: 15px 0;
    }
    
    .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #007cba;
        transition: width 0.3s ease;
    }
    
    .progress-excellent { background-color: #28a745; }
    .progress-good { background-color: #007cba; }
    .progress-warning { background-color: #ffc107; }
    .progress-danger { background-color: #dc3545; }
    
    .performance-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }
    
    .performance-table th,
    .performance-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .performance-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #333;
    }
    
    .performance-table tr:hover {
        background-color: #f5f5f5;
    }
    
    .btn {
        padding: 10px 20px;
        margin: 5px;
        background-color: #007cba;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn:hover {
        background-color: #005a87;
    }
    
    .btn-success {
        background-color: #28a745;
    }
    
    .btn-success:hover {
        background-color: #218838;
    }
    
    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-warning:hover {
        background-color: #e0a800;
    }
    
    .alert {
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    
    .time-range-selector {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .time-range-selector select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .recommendations-panel {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .recommendations-panel h3 {
        margin-top: 0;
        color: #856404;
    }
    
    .recommendation-item {
        padding: 10px;
        margin: 10px 0;
        background: white;
        border-left: 4px solid #ffc107;
        border-radius: 4px;
    }
    
    .recommendation-priority {
        font-weight: bold;
        color: #856404;
    }
    
    .recommendation-high {
        border-left-color: #dc3545;
    }
    
    .recommendation-medium {
        border-left-color: #ffc107;
    }
    
    .recommendation-low {
        border-left-color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<h1>Database Performance Report</h1>

{% if error %}
<div class="alert alert-danger">
    <strong>Error:</strong> {{ error }}
</div>
{% endif %}

<!-- Time Range Selector -->
<div class="time-range-selector">
    <label for="timeRange"><strong>Time Range:</strong></label>
    <select id="timeRange" onchange="updateTimeRange()">
        <option value="1h">Last Hour</option>
        <option value="24h" selected>Last 24 Hours</option>
        <option value="7d">Last 7 Days</option>
        <option value="30d">Last 30 Days</option>
    </select>
    
    <button onclick="refreshReport()" class="btn">Refresh Report</button>
    <button onclick="exportReport()" class="btn">Export Report</button>
    <button onclick="scheduleReport()" class="btn btn-warning">Schedule Report</button>
</div>

<!-- Key Performance Metrics -->
<div class="performance-grid">
    <!-- Query Performance -->
    <div class="performance-card">
        <h3>Query Performance</h3>
        <div class="metric-display">
            <span class="metric-value">{{ queries_per_second|default:"0" }}</span>
            <div class="metric-label">Queries per Second</div>
            <div class="metric-trend trend-stable">↔ Stable</div>
        </div>
        
        <div style="margin-top: 20px;">
            <div><strong>Slow Queries:</strong> {{ slow_queries|default:"0" }} ({{ slow_query_rate|default:"0" }}%)</div>
            <div class="progress-bar">
                <div class="progress-fill {% if slow_query_rate > 5 %}progress-danger{% elif slow_query_rate > 2 %}progress-warning{% else %}progress-good{% endif %}" 
                     style="width: {{ slow_query_rate|default:"0" }}%"></div>
            </div>
        </div>
    </div>
    
    <!-- Connection Usage -->
    <div class="performance-card">
        <h3>Connection Usage</h3>
        <div class="metric-display">
            <span class="metric-value">{{ connection_usage|default:"0" }}%</span>
            <div class="metric-label">Connection Pool Usage</div>
            <div class="metric-trend trend-up">↗ Increasing</div>
        </div>
        
        <div style="margin-top: 20px;">
            <div><strong>Active:</strong> {{ active_connections|default:"0" }} / {{ max_connections|default:"0" }}</div>
            <div class="progress-bar">
                <div class="progress-fill {% if connection_usage > 80 %}progress-danger{% elif connection_usage > 60 %}progress-warning{% else %}progress-good{% endif %}" 
                     style="width: {{ connection_usage|default:"0" }}%"></div>
            </div>
        </div>
    </div>
    
    <!-- Buffer Pool Hit Rate -->
    <div class="performance-card">
        <h3>Buffer Pool Performance</h3>
        <div class="metric-display">
            <span class="metric-value">{{ buffer_hit_rate|default:"0" }}%</span>
            <div class="metric-label">Buffer Pool Hit Rate</div>
            <div class="metric-trend trend-stable">↔ Stable</div>
        </div>
        
        <div style="margin-top: 20px;">
            <div class="progress-bar">
                <div class="progress-fill {% if buffer_hit_rate > 95 %}progress-excellent{% elif buffer_hit_rate > 90 %}progress-good{% elif buffer_hit_rate > 80 %}progress-warning{% else %}progress-danger{% endif %}" 
                     style="width: {{ buffer_hit_rate|default:"0" }}%"></div>
            </div>
            <small>Target: >95% for optimal performance</small>
        </div>
    </div>
    
    <!-- System Uptime -->
    <div class="performance-card">
        <h3>System Uptime</h3>
        <div class="metric-display">
            <span class="metric-value">{{ uptime_hours|default:"0" }}</span>
            <div class="metric-label">Hours</div>
            <div class="metric-trend trend-up">↗ Running</div>
        </div>
        
        <div style="margin-top: 20px;">
            <div><strong>Total Queries:</strong> {{ total_queries|default:"0"|floatformat:0 }}</div>
            <div><strong>Avg Response:</strong> 0.05s</div>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="performance-grid">
    <div class="performance-card">
        <h3>Query Response Time Trend</h3>
        <div class="performance-chart">
            Chart: Query response time over time
            <br><small>(Chart visualization would be implemented with a charting library)</small>
        </div>
    </div>
    
    <div class="performance-card">
        <h3>Connection Usage Trend</h3>
        <div class="performance-chart">
            Chart: Connection usage over time
            <br><small>(Chart visualization would be implemented with a charting library)</small>
        </div>
    </div>
</div>

<!-- Top Slow Queries -->
<div class="performance-card" style="margin-top: 20px;">
    <h3>Top Slow Queries</h3>
    <table class="performance-table">
        <thead>
            <tr>
                <th>Query Pattern</th>
                <th>Avg Time (s)</th>
                <th>Executions</th>
                <th>Total Time (s)</th>
                <th>Impact</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>SELECT * FROM products WHERE...</td>
                <td>2.34</td>
                <td>1,245</td>
                <td>2,913</td>
                <td><span style="color: #dc3545;">High</span></td>
            </tr>
            <tr>
                <td>UPDATE orders SET status...</td>
                <td>1.87</td>
                <td>892</td>
                <td>1,668</td>
                <td><span style="color: #ffc107;">Medium</span></td>
            </tr>
            <tr>
                <td>SELECT COUNT(*) FROM...</td>
                <td>1.23</td>
                <td>567</td>
                <td>697</td>
                <td><span style="color: #28a745;">Low</span></td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Performance Recommendations -->
<div class="recommendations-panel">
    <h3>Performance Recommendations</h3>
    
    <div class="recommendation-item recommendation-high">
        <div class="recommendation-priority">HIGH PRIORITY</div>
        <div><strong>Add Index on products.category_id</strong></div>
        <div>Query performance can be improved by 85% with proper indexing on frequently queried columns.</div>
    </div>
    
    <div class="recommendation-item recommendation-medium">
        <div class="recommendation-priority">MEDIUM PRIORITY</div>
        <div><strong>Optimize Buffer Pool Size</strong></div>
        <div>Consider increasing innodb_buffer_pool_size to 75% of available RAM for better caching.</div>
    </div>
    
    <div class="recommendation-item recommendation-low">
        <div class="recommendation-priority">LOW PRIORITY</div>
        <div><strong>Review Connection Pool Settings</strong></div>
        <div>Current connection usage is within acceptable limits but monitor for peak traffic periods.</div>
    </div>
</div>

<!-- Performance Summary -->
<div class="performance-card" style="margin-top: 20px;">
    <h3>Performance Summary</h3>
    <div class="alert alert-info">
        <strong>Overall Assessment:</strong> Database performance is good with some areas for optimization.
        Key metrics are within acceptable ranges, but addressing the high-priority recommendations
        could significantly improve query response times.
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
        <div style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <div style="font-size: 1.5em; font-weight: bold; color: #28a745;">85%</div>
            <div>Performance Score</div>
        </div>
        <div style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <div style="font-size: 1.5em; font-weight: bold; color: #007cba;">3</div>
            <div>Optimization Opportunities</div>
        </div>
        <div style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <div style="font-size: 1.5em; font-weight: bold; color: #ffc107;">1</div>
            <div>Critical Issues</div>
        </div>
        <div style="text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <div style="font-size: 1.5em; font-weight: bold; color: #6c757d;">24h</div>
            <div>Report Period</div>
        </div>
    </div>
</div>

<script>
function updateTimeRange() {
    const timeRange = document.getElementById('timeRange').value;
    // In a real implementation, this would trigger a data refresh
    console.log('Time range changed to:', timeRange);
}

function refreshReport() {
    // Show loading state
    document.body.style.cursor = 'wait';
    
    // Simulate refresh
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function exportReport() {
    window.open('{% url "admin:export_report_api" %}?type=performance&format=csv', '_blank');
}

function scheduleReport() {
    const schedule = prompt('Enter schedule (e.g., daily, weekly, monthly):');
    if (schedule) {
        alert(`Performance report scheduled: ${schedule}`);
        // In a real implementation, this would set up automated reporting
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshReport();
    }
}, 300000);
</script>
{% endblock %}