"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5493],{511:(e,s,r)=>{r.d(s,{z:()=>u});var t=r(5155),a=r(5695),l=r(6322),n=r(9519),o=r(3741),c=r(7141),i=r(3568);function u(e){let{variant:s="ghost",size:r="sm",className:u="",children:d="Logout",redirectTo:m=c.bw.HOME}=e,h=(0,l.jL)(),x=(0,a.useRouter)(),{loading:p}=(0,l.GV)(e=>e.auth),b=async()=>{try{await h((0,n.y4)()).unwrap(),i.Ay.success("Logged out successfully"),x.push(m)}catch(e){i.Ay.error("Logout failed"),x.push(m)}};return(0,t.jsx)(o.$,{variant:s,size:r,className:u,onClick:b,loading:p,children:d})}},2815:(e,s,r)=>{r.d(s,{R:()=>a});var t=r(5155);function a(e){let{size:s="md",text:r,className:a=""}=e;return(0,t.jsx)("div",{className:"flex items-center justify-center ".concat(a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("svg",{className:"animate-spin ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[s]," text-blue-600"),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r&&(0,t.jsx)("p",{className:"text-sm text-gray-600",children:r})]})})}r(2115)},3915:(e,s,r)=>{r.d(s,{p:()=>a});var t=r(5155);function a(e){let{label:s,error:r,helperText:a,className:l="",id:n,...o}=e,c=n||"input-".concat(Math.random().toString(36).substr(2,9)),i=r?"".concat(c,"-error"):void 0,u=a?"".concat(c,"-helper"):void 0,d="".concat("block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors touch-manipulation"," ").concat(r?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400"," ").concat("disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed disabled:border-gray-200"," ").concat(l);return(0,t.jsxs)("div",{className:"space-y-1",children:[s&&(0,t.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-700",children:s}),(0,t.jsx)("input",{id:c,className:d,"aria-invalid":r?"true":"false","aria-describedby":[i,u].filter(Boolean).join(" ")||void 0,...o}),r&&(0,t.jsx)("p",{id:i,className:"text-sm text-red-600",role:"alert",children:r}),a&&!r&&(0,t.jsx)("p",{id:u,className:"text-sm text-gray-500",children:a})]})}r(2115)},5493:(e,s,r)=>{r.d(s,{CU:()=>g,QB:()=>p,OV:()=>w,s4:()=>b});var t=r(5155),a=r(2115),l=r(5695),n=r(6322),o=r(9519),c=r(3741),i=r(3915),u=r(7141),d=r(6874),m=r.n(d),h=r(3568);function x(){let[e,s]=(0,a.useState)({email:"",password:""}),[r,d]=(0,a.useState)({}),x=(0,n.jL)(),p=(0,l.useRouter)(),b=(0,l.useSearchParams)(),{loading:f,error:y}=(0,n.GV)(e=>e.auth);(0,a.useEffect)(()=>{"password-reset-success"===b.get("message")&&h.Ay.success("Password reset successful! You can now log in with your new password.")},[b]);let w=e=>{let{name:t,value:a}=e.target;s(e=>({...e,[t]:a})),r[t]&&d(e=>({...e,[t]:""}))},g=async s=>{if(s.preventDefault(),(()=>{let s={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(s.email="Email is invalid"):s.email="Email is required",e.password||(s.password="Password is required"),d(s),0===Object.keys(s).length})())try{await x((0,o.Lx)(e)).unwrap(),h.Ay.success("Login successful!"),p.push(u.bw.HOME)}catch(e){h.Ay.error(e||"Login failed")}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(m(),{href:u.bw.REGISTER,className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:g,children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(i.p,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:w,error:r.email,placeholder:"Enter your email"}),(0,t.jsx)(i.p,{label:"Password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:e.password,onChange:w,error:r.password,placeholder:"Enter your password"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(m(),{href:u.bw.FORGOT_PASSWORD,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),y&&(0,t.jsx)("div",{className:"text-red-600 text-sm text-center",children:y}),(0,t.jsx)("div",{children:(0,t.jsx)(c.$,{type:"submit",loading:f,className:"w-full",size:"lg",children:"Sign in"})})]})]})})}function p(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{children:"Loading..."}),children:(0,t.jsx)(x,{})})}function b(){let[e,s]=(0,a.useState)({username:"",email:"",password:"",password_confirm:"",user_type:u.Cy.CUSTOMER,phone_number:""}),[r,d]=(0,a.useState)({}),x=(0,n.jL)(),p=(0,l.useRouter)(),{loading:b,error:f}=(0,n.GV)(e=>e.auth),y=e=>{let{name:t,value:a}=e.target;s(e=>({...e,[t]:a})),r[t]&&d(e=>({...e,[t]:""}))},w=async s=>{if(s.preventDefault(),(()=>{let s={},r=(e=>{let s=[];return e.length<u.oO.USERNAME_MIN_LENGTH&&s.push("Username must be at least ".concat(u.oO.USERNAME_MIN_LENGTH," characters long")),/^[a-zA-Z0-9_]+$/.test(e)||s.push("Username can only contain letters, numbers, and underscores"),s})(e.username);if(r.length>0&&(s.username=r[0]),e.email){let r;r=e.email,u.oO.EMAIL_REGEX.test(r)||(s.email="Please enter a valid email address")}else s.email="Email is required";let t=(e=>{let s=[];return e.length<u.oO.PASSWORD_MIN_LENGTH&&s.push("Password must be at least ".concat(u.oO.PASSWORD_MIN_LENGTH," characters long")),/(?=.*[a-z])/.test(e)||s.push("Password must contain at least one lowercase letter"),/(?=.*[A-Z])/.test(e)||s.push("Password must contain at least one uppercase letter"),/(?=.*\d)/.test(e)||s.push("Password must contain at least one number"),s})(e.password);t.length>0&&(s.password=t[0]);let a=e.password!==e.password_confirm?"Passwords do not match":null;return a&&(s.password_confirm=a),e.phone_number&&!/^[\+]?[1-9][\d]{0,15}$/.test(e.phone_number)&&(s.phone_number="Please enter a valid phone number"),d(s),0===Object.keys(s).length})())try{await x((0,o.DY)(e)).unwrap(),h.Ay.success("Registration successful! Welcome to our platform!"),p.push(u.bw.HOME)}catch(e){h.Ay.error(e||"Registration failed")}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(m(),{href:u.bw.LOGIN,className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:w,children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(i.p,{label:"Username",name:"username",type:"text",autoComplete:"username",required:!0,value:e.username,onChange:y,error:r.username,placeholder:"Choose a username",helperText:"Username must be at least 3 characters and contain only letters, numbers, and underscores"}),(0,t.jsx)(i.p,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:y,error:r.email,placeholder:"Enter your email"}),(0,t.jsx)(i.p,{label:"Password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:e.password,onChange:y,error:r.password,placeholder:"Create a password",helperText:"Password must be at least 8 characters with uppercase, lowercase, and number"}),(0,t.jsx)(i.p,{label:"Confirm Password",name:"password_confirm",type:"password",autoComplete:"new-password",required:!0,value:e.password_confirm,onChange:y,error:r.password_confirm,placeholder:"Confirm your password"}),(0,t.jsx)(i.p,{label:"Phone Number (Optional)",name:"phone_number",type:"tel",autoComplete:"tel",value:e.phone_number,onChange:y,error:r.phone_number,placeholder:"+**********"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("label",{htmlFor:"user_type",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,t.jsxs)("select",{id:"user_type",name:"user_type",value:e.user_type,onChange:y,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,t.jsx)("option",{value:u.Cy.CUSTOMER,children:"Customer"}),(0,t.jsx)("option",{value:u.Cy.SELLER,children:"Seller"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",(0,t.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Terms and Conditions"})," ","and"," ",(0,t.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),f&&(0,t.jsx)("div",{className:"text-red-600 text-sm text-center",children:f}),(0,t.jsx)("div",{children:(0,t.jsx)(c.$,{type:"submit",loading:b,className:"w-full",size:"lg",children:"Create Account"})})]})]})})}r(511);var f=r(2815);function y(e){let{children:s,requireAuth:r=!1,requireGuest:c=!1,allowedUserTypes:i=[],redirectTo:d,fallback:m}=e,h=(0,n.jL)(),x=(0,l.useRouter)(),{user:p,isAuthenticated:b,loading:y}=(0,n.GV)(e=>e.auth);if((0,a.useEffect)(()=>{h((0,o.Nu)())},[h]),y)return m||(0,t.jsx)(f.R,{});if(r&&!b){let e=d||u.bw.LOGIN;return x.push(e),m||(0,t.jsx)(f.R,{})}if(c&&b){let e=d||u.bw.HOME;return x.push(e),m||(0,t.jsx)(f.R,{})}if(r&&b&&i.length>0&&p&&!i.includes(p.user_type)){let e=d||u.bw.HOME;return x.push(e),m||(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}return(0,t.jsx)(t.Fragment,{children:s})}function w(e){let{children:s,allowedUserTypes:r=[],redirectTo:a,fallback:l}=e;return(0,t.jsx)(y,{requireAuth:!0,allowedUserTypes:r,redirectTo:a,fallback:l,children:s})}function g(e){let{children:s,redirectTo:r,fallback:a}=e;return(0,t.jsx)(y,{requireGuest:!0,redirectTo:r,fallback:a,children:s})}r(3865),r(8359)}}]);