(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{2056:(e,s,r)=>{"use strict";r.d(s,{LP:()=>i,Ti:()=>c,To:()=>d,Uk:()=>a,Y9:()=>l,yH:()=>n});var t=r(7141);let a=()=>{try{let e=localStorage.getItem(t.d5.ACCESS_TOKEN),s=localStorage.getItem(t.d5.REFRESH_TOKEN);if(e&&s)return{access:e,refresh:s}}catch(e){console.error("Error getting stored tokens:",e)}return null},n=e=>{try{localStorage.setItem(t.d5.ACCESS_TOKEN,e.access),localStorage.setItem(t.d5.REFRESH_TOKEN,e.refresh)}catch(e){console.error("Error storing tokens:",e)}},l=()=>{try{localStorage.removeItem(t.d5.ACCESS_TOKEN),localStorage.removeItem(t.d5.REFRESH_TOKEN)}catch(e){console.error("Error removing tokens:",e)}},c=()=>{try{let e=localStorage.getItem(t.d5.USER);return e?JSON.parse(e):null}catch(e){return console.error("Error getting stored user:",e),null}},d=e=>{try{localStorage.setItem(t.d5.USER,JSON.stringify(e))}catch(e){console.error("Error storing user:",e)}},i=()=>{try{localStorage.removeItem(t.d5.USER)}catch(e){console.error("Error removing user:",e)}}},2302:(e,s,r)=>{"use strict";r.d(s,{uE:()=>c});var t=r(3464),a=r(7141),n=r(2056);class l{setupInterceptors(){this.client.interceptors.request.use(e=>{let s=(0,n.Uk)();return(null==s?void 0:s.access)&&e.headers&&(e.headers.Authorization="Bearer ".concat(s.access)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{if(e&&"object"==typeof e&&"config"in e&&"response"in e){var s;let r=e.config;if((null==(s=e.response)?void 0:s.status)===401&&!r._retry){r._retry=!0;try{let e=(0,n.Uk)();if(null==e?void 0:e.refresh){let s=(await this.refreshToken(e.refresh)).data;return(0,n.yH)(s),r.headers&&(r.headers.Authorization="Bearer ".concat(s.access)),this.client(r)}}catch(e){(0,n.Y9)(),window.location.href="/auth/login"}}}return Promise.reject(e)})}async refreshToken(e){return this.client.post("/auth/refresh/",{refresh:e})}async get(e,s){try{let r=await this.client.get(e,s);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}async post(e,s,r){try{let t=await this.client.post(e,s,r);return{success:!0,data:t.data}}catch(e){return this.handleError(e)}}async put(e,s,r){try{let t=await this.client.put(e,s,r);return{success:!0,data:t.data}}catch(e){return this.handleError(e)}}async patch(e,s,r){try{let t=await this.client.patch(e,s,r);return{success:!0,data:t.data}}catch(e){return this.handleError(e)}}async delete(e,s){try{let r=await this.client.delete(e,s);return{success:!0,data:r.data}}catch(e){return this.handleError(e)}}handleError(e){if(e&&"object"==typeof e&&"response"in e){if(e.response){var s,r,t,a,n,l,c;return{success:!1,error:{message:(null==(r=e.response.data)||null==(s=r.error)?void 0:s.message)||(null==(t=e.response.data)?void 0:t.message)||"An error occurred",code:(null==(n=e.response.data)||null==(a=n.error)?void 0:a.code)||"api_error",status_code:e.response.status,details:(null==(c=e.response.data)||null==(l=c.error)?void 0:l.details)||e.response.data}}}if(e.request)return{success:!1,error:{message:"Network error. Please check your connection.",code:"network_error",status_code:0}}}return{success:!1,error:{message:e instanceof Error?e.message:"An unexpected error occurred",code:"unknown_error",status_code:0}}}constructor(){this.client=t.A.create({baseURL:a.JR,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let c=new l,{get:d,post:i,put:o,patch:m,delete:u}=c},3544:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(5155),a=r(2115),n=r(5695),l=r(4540),c=r(6322),d=r(5111),i=r(3165),o=r(6766);let m=e=>{let{onMethodSelect:s}=e,r=(0,c.jL)(),{paymentMethods:n,selectedPaymentMethod:d,loading:m,error:u}=(0,l.d4)(e=>e.payments);(0,a.useEffect)(()=>{r((0,i.v4)())},[r]);let h=e=>{r((0,i.BG)(e)),s&&s(e)},x=n.reduce((e,s)=>{let r=s.method_type;return e[r]||(e[r]=[]),e[r].push(s),e},{}),p={CARD:"Credit/Debit Cards",UPI:"UPI Payment",WALLET:"Digital Wallets",NETBANKING:"Net Banking",COD:"Cash on Delivery",GIFT_CARD:"Gift Cards",IMPS:"IMPS Transfer",RTGS:"RTGS Transfer",NEFT:"NEFT Transfer"};return m?(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsx)("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-4 py-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]})]})})}):u?(0,t.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,t.jsxs)("p",{children:["Error loading payment methods: ",u]}),(0,t.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",onClick:()=>r((0,i.v4)()),children:"Retry"})]}):0===n.length?(0,t.jsx)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:(0,t.jsx)("p",{children:"No payment methods available at the moment. Please try again later."})}):(0,t.jsxs)("div",{className:"payment-methods-container",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Select Payment Method"}),Object.entries(x).map(e=>{let[s,r]=e;return(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium mb-2",children:p[s]||s}),(0,t.jsx)("div",{className:"space-y-2",children:r.map(e=>(0,t.jsxs)("div",{className:"\n                  p-4 border rounded-lg cursor-pointer transition-all\n                  ".concat(d===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-blue-300 hover:bg-gray-50","\n                "),onClick:()=>h(e.id),children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 mr-3",children:e.icon?(0,t.jsx)(o.default,{src:e.icon,alt:e.name,width:32,height:32,className:"object-contain"}):(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xs",children:e.name.substring(0,2)})})}),(0,t.jsxs)("div",{className:"flex-grow",children:[(0,t.jsx)("h5",{className:"font-medium",children:e.name}),e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.description})]}),(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("input",{type:"radio",checked:d===e.id,onChange:()=>h(e.id),className:"h-5 w-5 text-blue-600"})})]}),e.processing_fee_percentage>0||e.processing_fee_fixed>0?(0,t.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["Processing fee:",e.processing_fee_percentage>0&&(0,t.jsxs)("span",{children:[" ",e.processing_fee_percentage,"%"]}),e.processing_fee_percentage>0&&e.processing_fee_fixed>0&&(0,t.jsx)("span",{children:" + "}),e.processing_fee_fixed>0&&(0,t.jsxs)("span",{children:["$",e.processing_fee_fixed.toFixed(2)]})]}):null]},e.id))})]},s)})]})},u=e=>{var s,r,n;let{amount:d,onCurrencyChange:o}=e,m=(0,c.jL)(),{currencies:u,selectedCurrency:h,loading:x,error:p,currencyConversion:g}=(0,l.d4)(e=>e.payments);if((0,a.useEffect)(()=>{m((0,i.CW)())},[m]),(0,a.useEffect)(()=>{h&&d>0&&m((0,i.hZ)({from_currency:"USD",to_currency:h,amount:d}))},[m,h,d]),(0,a.useEffect)(()=>{g&&o&&o(g.to_currency,g.converted_amount)},[g,o]),x&&0===u.length)return(0,t.jsx)("div",{className:"animate-pulse",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32"})});if(p&&0===u.length)return(0,t.jsx)("div",{className:"text-red-500 text-sm",children:"Error loading currencies"});let b=u.find(e=>e.code===h);return(0,t.jsxs)("div",{className:"currency-selector",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("select",{value:h,onChange:e=>{let s=e.target.value;m((0,i.$f)(s))},className:"form-select rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",disabled:x,children:u.map(e=>(0,t.jsxs)("option",{value:e.code,children:[e.code," (",e.symbol,")"]},e.code))}),g?(0,t.jsx)("div",{className:"text-sm text-gray-500",children:(0,t.jsxs)("span",{children:["Exchange rate: 1 USD = ",null==b||null==(s=b.exchange_rate)?void 0:s.toFixed(4)," ",h]})}):null]}),g?(0,t.jsx)("div",{className:"mt-2 text-sm",children:(0,t.jsxs)("span",{className:"font-medium",children:[null==(r=g.amount)?void 0:r.toFixed(2)," USD ="," ",null==b?void 0:b.symbol,null==(n=g.converted_amount)?void 0:n.toFixed(2)," ",h]})}):null]})},h=e=>{let{amount:s,onProceed:r}=e,{selectedCurrency:n,currencies:c}=(0,l.d4)(e=>e.payments),[d,i]=(0,a.useState)({cardNumber:"",cardholderName:"",expiryMonth:"",expiryYear:"",cvv:"",saveCard:!1}),[o,m]=(0,a.useState)({}),u=e=>{let{name:s,value:r,type:t}=e.target,a="checkbox"===t?e.target.checked:void 0;i(e=>({...e,[s]:"checkbox"===t?a:r})),o[s]&&m(e=>({...e,[s]:void 0}))},h=Array.from({length:12},(e,s)=>{let r=(s+1).toString().padStart(2,"0");return(0,t.jsx)("option",{value:r,children:r},r)}),x=new Date().getFullYear(),p=Array.from({length:11},(e,s)=>{let r=x+s;return(0,t.jsx)("option",{value:r,children:r},r)}),g=c.find(e=>e.code===n)||{code:"USD",symbol:"$"};return(0,t.jsxs)("div",{className:"credit-card-payment p-4 border rounded-lg",children:[(0,t.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Credit/Debit Card Payment"}),(0,t.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Payment Amount:"}),(0,t.jsxs)("span",{className:"font-medium",children:[g.symbol,s.toFixed(2)," ",g.code]})]})}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{let e={},s=d.cardNumber.replace(/\D/g,"");return(!s||s.length<13||s.length>19)&&(e.cardNumber="Please enter a valid card number"),d.cardholderName.trim()||(e.cardholderName="Cardholder name is required"),d.expiryMonth||(e.expiryMonth="Required"),d.expiryYear||(e.expiryYear="Required"),(!d.cvv||d.cvv.length<3||d.cvv.length>4)&&(e.cvv="Invalid CVV"),m(e),0===Object.keys(e).length})()&&r(d)},children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"cardNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Card Number"}),(0,t.jsx)("input",{id:"cardNumber",name:"cardNumber",type:"text",value:d.cardNumber,onChange:e=>{let s=(e=>{let s=e.replace(/\D/g,""),r="";for(let e=0;e<s.length;e+=4)r+=s.slice(e,e+4)+" ";return r.trim()})(e.target.value);i(e=>({...e,cardNumber:s})),o.cardNumber&&m(e=>({...e,cardNumber:void 0}))},placeholder:"1234 5678 9012 3456",maxLength:19,className:"w-full form-input rounded-md ".concat(o.cardNumber?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"," focus:ring focus:ring-opacity-50")}),o.cardNumber&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.cardNumber})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"cardholderName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cardholder Name"}),(0,t.jsx)("input",{id:"cardholderName",name:"cardholderName",type:"text",value:d.cardholderName,onChange:u,placeholder:"John Doe",className:"w-full form-input rounded-md ".concat(o.cardholderName?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"," focus:ring focus:ring-opacity-50")}),o.cardholderName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.cardholderName})]}),(0,t.jsxs)("div",{className:"flex mb-4 space-x-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("label",{htmlFor:"expiryMonth",className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("select",{id:"expiryMonth",name:"expiryMonth",value:d.expiryMonth,onChange:u,className:"w-full form-select rounded-md ".concat(o.expiryMonth?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"," focus:ring focus:ring-opacity-50"),children:[(0,t.jsx)("option",{value:"",children:"Month"}),h]}),(0,t.jsxs)("select",{id:"expiryYear",name:"expiryYear",value:d.expiryYear,onChange:u,className:"w-full form-select rounded-md ".concat(o.expiryYear?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"," focus:ring focus:ring-opacity-50"),children:[(0,t.jsx)("option",{value:"",children:"Year"}),p]})]}),(o.expiryMonth||o.expiryYear)&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Please select a valid expiry date"})]}),(0,t.jsxs)("div",{className:"w-1/3",children:[(0,t.jsx)("label",{htmlFor:"cvv",className:"block text-sm font-medium text-gray-700 mb-1",children:"CVV"}),(0,t.jsx)("input",{id:"cvv",name:"cvv",type:"password",value:d.cvv,onChange:u,placeholder:"123",maxLength:4,className:"w-full form-input rounded-md ".concat(o.cvv?"border-red-300 focus:border-red-500 focus:ring-red-200":"border-gray-300 focus:border-blue-300 focus:ring-blue-200"," focus:ring focus:ring-opacity-50")}),o.cvv&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.cvv})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"saveCard",name:"saveCard",type:"checkbox",checked:d.saveCard,onChange:u,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"saveCard",className:"ml-2 block text-sm text-gray-700",children:"Save card for future payments"})]})}),(0,t.jsxs)("button",{type:"submit",className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:["Pay ",g.symbol,s.toFixed(2)]})]})]})},x=e=>{let{amount:s,onProceed:r}=e,n=(0,c.jL)(),{wallet:d,loading:o,error:m}=(0,l.d4)(e=>e.payments);(0,a.useEffect)(()=>{n((0,i.OS)())},[n]);let u=()=>{r()},h=d&&d.balance>=s;return o?(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsx)("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-4 py-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]})})}):m?(0,t.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,t.jsxs)("p",{children:["Error loading wallet: ",m]}),(0,t.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",onClick:()=>n((0,i.OS)()),children:"Retry"})]}):d?(0,t.jsxs)("div",{className:"wallet-payment p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h4",{className:"text-lg font-medium",children:"Your Wallet"}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Available Balance"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[d.currency.symbol,d.balance.toFixed(2)," ",d.currency.code]})]})]}),(0,t.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Payment Amount:"}),(0,t.jsxs)("span",{className:"font-medium",children:[d.currency.symbol,s.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,t.jsx)("span",{children:"Balance After Payment:"}),(0,t.jsxs)("span",{className:"font-medium ".concat(h?"text-green-600":"text-red-600"),children:[d.currency.symbol,Math.max(0,d.balance-s).toFixed(2)]})]})]}),!h&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 rounded-md",children:(0,t.jsx)("p",{children:"Insufficient wallet balance. Please add funds or choose another payment method."})}),(0,t.jsx)("button",{className:"w-full py-2 rounded-md transition-colors ".concat(h?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"),onClick:u,disabled:!h,children:h?"Pay from Wallet":"Insufficient Balance"})]}):(0,t.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,t.jsx)("p",{children:"You don't have a wallet yet. A wallet will be created for you when you proceed."}),(0,t.jsx)("button",{className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",onClick:u,children:"Proceed with Payment"})]})},p=e=>{let{amount:s,onProceed:r}=e,n=(0,c.jL)(),{giftCard:d,loading:o,error:m}=(0,l.d4)(e=>e.payments),[u,h]=(0,a.useState)(""),x=d&&d.current_balance>=s;return(0,t.jsxs)("div",{className:"gift-card-payment p-4 border rounded-lg",children:[(0,t.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Gift Card Payment"}),d?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4 p-3 bg-green-50 rounded-md",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-green-700",children:"Valid Gift Card"}),(0,t.jsx)("button",{onClick:()=>{n((0,i.ON)()),h("")},className:"text-sm text-blue-600 hover:text-blue-800",children:"Change"})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Card Number"}),(0,t.jsx)("div",{className:"font-medium",children:d.code})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Available Balance"}),(0,t.jsxs)("div",{className:"font-bold",children:[d.currency.symbol,d.current_balance.toFixed(2)," ",d.currency.code]})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Expires On"}),(0,t.jsx)("div",{children:new Date(d.expiry_date).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Payment Amount:"}),(0,t.jsxs)("span",{className:"font-medium",children:[d.currency.symbol,s.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,t.jsx)("span",{children:"Balance After Payment:"}),(0,t.jsxs)("span",{className:"font-medium ".concat(x?"text-green-600":"text-red-600"),children:[d.currency.symbol,Math.max(0,d.current_balance-s).toFixed(2)]})]})]}),!x&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 rounded-md",children:(0,t.jsx)("p",{children:"Insufficient gift card balance. Please use another gift card or choose another payment method."})}),(0,t.jsx)("button",{className:"w-full py-2 rounded-md transition-colors ".concat(x?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"),onClick:()=>{d&&d.current_balance>=s&&r(d.code)},disabled:!x,children:x?"Pay with Gift Card":"Insufficient Balance"})]}):(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"gift-card-code",className:"block text-sm font-medium text-gray-700 mb-1",children:"Enter Gift Card Code"}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("input",{id:"gift-card-code",type:"text",value:u,onChange:e=>h(e.target.value),placeholder:"XXXX-XXXX-XXXX-XXXX",className:"flex-grow form-input rounded-l-md border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",disabled:o}),(0,t.jsx)("button",{onClick:()=>{u.trim()&&n((0,i.Pw)(u.trim()))},disabled:!u.trim()||o,className:"px-4 py-2 rounded-r-md ".concat(!u.trim()||o?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:o?"Validating...":"Validate"})]}),m&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:m})]})})]})},g=e=>{let{orderId:s,amount:r,onSuccess:n,onFailure:d,onCancel:o}=e,m=(0,c.jL)(),{selectedCurrency:u,selectedPaymentMethod:h,currentPayment:x,paymentProcessing:p,paymentSuccess:g,paymentError:b,giftCard:y}=(0,l.d4)(e=>e.payments),[f,N]=(0,a.useState)(!1),[j,v]=(0,a.useState)(null),[E,S]=(0,a.useState)(null);return((0,a.useEffect)(()=>()=>{E&&clearInterval(E),m((0,i.vg)())},[m,E]),(0,a.useEffect)(()=>{g&&x&&(E&&(clearInterval(E),S(null)),n(x.id))},[g,x,E,n]),(0,a.useEffect)(()=>{b&&!p&&d(b)},[b,p,d]),f&&p)?(0,t.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Processing Payment"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we process your payment..."})]})}):j?(0,t.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Verifying Payment"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we verify your payment..."})]})}):g&&x?(0,t.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-12 w-12 rounded-full bg-green-100",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-green-800 mb-2",children:"Payment Successful"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Your payment has been processed successfully."}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Payment ID: ",x.id]})]})}):b?(0,t.jsx)("div",{className:"payment-processor p-6 border rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-12 w-12 rounded-full bg-red-100",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-red-800 mb-2",children:"Payment Failed"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:b}),(0,t.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,t.jsx)("button",{onClick:()=>m((0,i.vg)()),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Try Again"}),(0,t.jsx)("button",{onClick:o,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300",children:"Cancel"})]})]})}):null},b=e=>{let{orderId:s,amount:r,onPaymentSuccess:n,onPaymentFailure:d,onCancel:o}=e,b=(0,c.jL)(),{paymentMethods:y,selectedPaymentMethod:f,selectedCurrency:N,loading:j}=(0,l.d4)(e=>e.payments),[v,E]=(0,a.useState)(r),[S,R]=(0,a.useState)(!1),[C,w]=(0,a.useState)(null);(0,a.useEffect)(()=>{b((0,i.v4)()),b((0,i.CW)())},[b]);let T=e=>{w({type:"card",data:e}),R(!0)},A=()=>{w({type:"wallet"}),R(!0)},_=e=>{w({type:"gift_card",data:{code:e}}),R(!0)},O=y.find(e=>e.id===f);return j&&0===y.length?(0,t.jsx)("div",{className:"checkout-payment p-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4"}),(0,t.jsx)("div",{className:"h-40 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-40 bg-gray-200 rounded"})]})}):(0,t.jsx)("div",{className:"checkout-payment",children:S?(0,t.jsx)(g,{orderId:s,amount:v,onSuccess:n,onFailure:d,onCancel:()=>{R(!1),w(null)}}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Payment"}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)(u,{amount:r,onCurrencyChange:(e,s)=>{E(s)}})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"md:col-span-1",children:(0,t.jsx)(m,{onMethodSelect:e=>{w(null),R(!1)}})}),(0,t.jsx)("div",{className:"md:col-span-2",children:(()=>{if(!O)return null;switch(O.method_type){case"CARD":return(0,t.jsx)(h,{amount:v,onProceed:T});case"WALLET":return(0,t.jsx)(x,{amount:v,onProceed:A});case"GIFT_CARD":return(0,t.jsx)(p,{amount:v,onProceed:_});case"COD":return(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h4",{className:"text-lg font-medium mb-4",children:"Cash on Delivery"}),(0,t.jsxs)("p",{className:"mb-4",children:["You will pay ",N," ",v.toFixed(2)," when your order is delivered."]}),(0,t.jsx)("button",{onClick:()=>{w({type:"cod"}),R(!0)},className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Place Order with COD"})]});default:return(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h4",{className:"text-lg font-medium mb-4",children:O.name}),(0,t.jsx)("p",{className:"mb-4",children:"Please proceed to complete your payment."}),(0,t.jsx)("button",{onClick:()=>{w({type:O.method_type.toLowerCase()}),R(!0)},className:"w-full py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Proceed to Payment"})]})}})()})]}),(0,t.jsx)("div",{className:"mt-6 flex justify-between",children:(0,t.jsx)("button",{onClick:o,className:"px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors",children:"Back"})})]})})};var y=r(7141);let f=e=>({id:e.id||"",type:e.type||"HOME",is_default:e.is_default||!1,first_name:e.first_name||"",last_name:e.last_name||"",address_line_1:e.address_line_1||"",address_line_2:e.address_line_2,city:e.city||"",state:e.state||"",postal_code:e.postal_code||"",country:e.country||""});var N=function(e){return e.SHIPPING="shipping",e.PAYMENT="payment",e.CONFIRMATION="confirmation",e}(N||{});let j=()=>{let e=(0,n.useRouter)(),s=(0,c.jL)(),{items:r}=(0,l.d4)(e=>e.cart),{currentOrder:i,loading:o,error:m}=(0,l.d4)(e=>e.orders),[u,h]=(0,a.useState)("shipping"),[x,p]=(0,a.useState)(null),[g,N]=(0,a.useState)(null),[j,v]=(0,a.useState)(""),[E,S]=(0,a.useState)("");(0,a.useEffect)(()=>{r&&0!==r.length||e.push(y.bw.CART)},[r,e]);let R=e=>{S(e),h("confirmation")},C=e=>{console.error("Payment failed:",e)};return o?(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsx)("div",{className:"h-60 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-40 bg-gray-200 rounded"})]})}):m?(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"p-4 bg-red-50 text-red-700 rounded-lg",children:[(0,t.jsxs)("p",{children:["Error: ",m]}),(0,t.jsx)("button",{onClick:()=>e.push(y.bw.CART),className:"mt-4 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Return to Cart"})]})}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsxs)("div",{className:"step-item ".concat("shipping"===u?"active":"completed"),children:[(0,t.jsx)("div",{className:"step-circle",children:"1"}),(0,t.jsx)("div",{className:"step-text",children:"Shipping"})]}),(0,t.jsx)("div",{className:"step-line"}),(0,t.jsxs)("div",{className:"step-item ".concat("payment"===u?"active":"confirmation"===u?"completed":""),children:[(0,t.jsx)("div",{className:"step-circle",children:"2"}),(0,t.jsx)("div",{className:"step-text",children:"Payment"})]}),(0,t.jsx)("div",{className:"step-line"}),(0,t.jsxs)("div",{className:"step-item ".concat("confirmation"===u?"active":""),children:[(0,t.jsx)("div",{className:"step-circle",children:"3"}),(0,t.jsx)("div",{className:"step-text",children:"Confirmation"})]})]})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(()=>{switch(u){case"shipping":default:return(0,t.jsxs)("div",{className:"shipping-step",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Shipping Information"}),(0,t.jsxs)("div",{className:"mb-6 p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,t.jsx)("p",{children:"This is a placeholder for the shipping form."}),(0,t.jsx)("p",{children:"In a real implementation, this would include address fields, shipping method selection, etc."})]}),(0,t.jsx)("button",{onClick:()=>{p(f({first_name:"John",last_name:"Doe",address_line_1:"123 Main St",city:"Anytown",state:"CA",postal_code:"12345",country:"US"})),N(f({first_name:"John",last_name:"Doe",address_line_1:"123 Main St",city:"Anytown",state:"CA",postal_code:"12345",country:"US"})),v("standard"),(()=>{if(!x||!j)return;let e={shipping_address:x,billing_address:g||x,shipping_method:j,items:(null==r?void 0:r.map(e=>({product_id:e.product.id,quantity:e.quantity})))||[]};s((0,d.fS)(e))})(),h("payment")},className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Continue to Payment"})]});case"payment":return i?(0,t.jsx)(b,{orderId:i.id,amount:i.total_amount,onPaymentSuccess:R,onPaymentFailure:C,onCancel:()=>h("shipping")}):(0,t.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,t.jsx)("p",{children:"Order information is not available. Please go back to the shipping step."}),(0,t.jsx)("button",{onClick:()=>h("shipping"),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Back to Shipping"})]});case"confirmation":return(()=>{var s;return i?(0,t.jsxs)("div",{className:"confirmation-step",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center h-16 w-16 rounded-full bg-green-100",children:(0,t.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-green-800 mb-2",children:"Order Confirmed!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Thank you for your purchase."})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Order Summary"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("p",{className:"text-gray-600",children:["Order Number: ",(0,t.jsx)("span",{className:"font-medium",children:i.order_number})]}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Payment ID: ",(0,t.jsx)("span",{className:"font-medium",children:E})]})]}),(0,t.jsxs)("div",{className:"border-t pt-4 mb-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Items"}),(0,t.jsx)("ul",{className:"space-y-2",children:null==i||null==(s=i.items)?void 0:s.map(e=>{var s,r;return(0,t.jsxs)("li",{className:"flex justify-between",children:[(0,t.jsxs)("span",{children:[null==(s=e.product)?void 0:s.name," \xd7 ",e.quantity]}),(0,t.jsxs)("span",{className:"font-medium",children:["$",null==(r=e.total_price)?void 0:r.toFixed(2)]})]},e.id)})})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{children:"Subtotal"}),(0,t.jsxs)("span",{children:["$",(((null==i?void 0:i.total_amount)||0)-((null==i?void 0:i.shipping_amount)||0)-((null==i?void 0:i.tax_amount)||0)+((null==i?void 0:i.discount_amount)||0)).toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{children:"Shipping"}),(0,t.jsxs)("span",{children:["$",((null==i?void 0:i.shipping_amount)||0).toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)("span",{children:"Tax"}),(0,t.jsxs)("span",{children:["$",((null==i?void 0:i.tax_amount)||0).toFixed(2)]})]}),((null==i?void 0:i.discount_amount)||0)>0&&(0,t.jsxs)("div",{className:"flex justify-between mb-2 text-green-600",children:[(0,t.jsx)("span",{children:"Discount"}),(0,t.jsxs)("span",{children:["-$",((null==i?void 0:i.discount_amount)||0).toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg mt-2 pt-2 border-t",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsxs)("span",{children:["$",((null==i?void 0:i.total_amount)||0).toFixed(2)]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("button",{onClick:()=>e.push(y.Hp.ORDERS),className:"px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors",children:"View Orders"}),(0,t.jsx)("button",{onClick:()=>e.push(y.bw.HOME),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Continue Shopping"})]})]}):(0,t.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-700 rounded-lg",children:[(0,t.jsx)("p",{children:"Order information is not available."}),(0,t.jsx)("button",{onClick:()=>e.push(y.bw.HOME),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Return to Home"})]})})()}})()})]})}},3801:(e,s,r)=>{Promise.resolve().then(r.bind(r,3544))},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6389:(e,s,r)=>{"use strict";r.d(s,{B0:()=>c,Ei:()=>a,F$:()=>l,Hp:()=>n,Ss:()=>d,l$:()=>t});let t={HOME:"/",PRODUCTS:"/products",PRODUCT_DETAIL:e=>"/products/".concat(e),CART:"/cart",CHECKOUT:"/checkout",SEARCH:"/search",ABOUT:"/about",CONTACT:"/contact",TERMS:"/terms",PRIVACY:"/privacy",FAQ:"/faq"},a={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email"},n={DASHBOARD:"/profile",ORDERS:"/profile/orders",ORDER_DETAIL:e=>"/profile/orders/".concat(e),ADDRESSES:"/profile/addresses",WISHLIST:"/profile/wishlist",SETTINGS:"/profile/settings",NOTIFICATIONS:"/profile/notifications"},l={DASHBOARD:"/admin",ANALYTICS:"/admin/analytics",ORDERS:"/admin/orders",ORDER_DETAIL:e=>"/admin/orders/".concat(e),PRODUCTS:"/admin/products",PRODUCT_EDIT:e=>"/admin/products/".concat(e,"/edit"),PRODUCT_CREATE:"/admin/products/create",CUSTOMERS:"/admin/customers",CUSTOMER_DETAIL:e=>"/admin/customers/".concat(e),CONTENT:"/admin/content",REPORTS:"/admin/reports",SYSTEM:"/admin/system",NOTIFICATIONS:"/admin/notifications",SETTINGS:"/admin/settings"},c={DASHBOARD:"/seller/dashboard",PRODUCTS:"/seller/products",PRODUCT_EDIT:e=>"/seller/products/".concat(e,"/edit"),PRODUCT_CREATE:"/seller/products/create",ORDERS:"/seller/orders",ORDER_DETAIL:e=>"/seller/orders/".concat(e),PROFILE:"/seller/profile",KYC:"/seller/kyc",BANK_ACCOUNTS:"/seller/bank-accounts",PAYOUTS:"/seller/payouts",ANALYTICS:"/seller/analytics",SETTINGS:"/seller/settings"},d={"/":"Home","/products":"Products","/cart":"Shopping Cart","/checkout":"Checkout","/search":"Search","/about":"About Us","/contact":"Contact Us","/terms":"Terms of Service","/privacy":"Privacy Policy","/faq":"FAQ","/auth/login":"Login","/auth/register":"Register","/auth/forgot-password":"Forgot Password","/auth/reset-password":"Reset Password","/auth/verify-email":"Verify Email","/profile":"My Account","/profile/orders":"My Orders","/profile/addresses":"My Addresses","/profile/wishlist":"My Wishlist","/profile/settings":"Account Settings","/profile/notifications":"Notifications","/admin":"Admin Dashboard","/admin/analytics":"Analytics","/admin/orders":"Orders Management","/admin/products":"Products Management","/admin/products/create":"Create Product","/admin/customers":"Customers Management","/admin/content":"Content Management","/admin/reports":"Reports","/admin/system":"System Health","/admin/notifications":"Notifications","/admin/settings":"Admin Settings","/seller/dashboard":"Seller Dashboard","/seller/products":"My Products","/seller/products/create":"Add New Product","/seller/orders":"My Orders","/seller/profile":"Seller Profile","/seller/kyc":"KYC Verification","/seller/bank-accounts":"Bank Accounts","/seller/payouts":"Payouts","/seller/analytics":"Sales Analytics","/seller/settings":"Seller Settings"}},6654:(e,s,r)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"useMergedRef",{enumerable:!0,get:function(){return a}});let t=r(2115);function a(e,s){let r=(0,t.useRef)(null),a=(0,t.useRef)(null);return(0,t.useCallback)(t=>{if(null===t){let e=r.current;e&&(r.current=null,e());let s=a.current;s&&(a.current=null,s())}else e&&(r.current=n(e,t)),s&&(a.current=n(s,t))},[e,s])}function n(e,s){if("function"!=typeof e)return e.current=s,()=>{e.current=null};{let r=e(s);return"function"==typeof r?r:()=>e(null)}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},7141:(e,s,r)=>{"use strict";r.d(s,{Cy:()=>c,Hp:()=>t.Hp,JR:()=>a,Sn:()=>n,bw:()=>i,d5:()=>l,oO:()=>o,w8:()=>d});var t=r(6389);let a="http://localhost:8000/api/v1",n={AUTH:{LOGIN:"/auth/login/",REGISTER:"/auth/register/",LOGOUT:"/auth/logout/",REFRESH:"/auth/refresh/",PROFILE:"/auth/profile/",FORGOT_PASSWORD:"/auth/forgot-password/",RESET_PASSWORD:"/auth/reset-password/",VALIDATE_RESET_TOKEN:e=>"/auth/validate-reset-token/".concat(e,"/")},PRODUCTS:{LIST:"/products/",DETAIL:e=>"/products/".concat(e,"/"),CATEGORIES:"/products/categories/"},CART:{LIST:"/cart/",ADD:"/cart/add/",UPDATE:e=>"/cart/".concat(e,"/"),REMOVE:e=>"/cart/".concat(e,"/")},ORDERS:{LIST:"/orders/",DETAIL:e=>"/orders/".concat(e,"/"),CREATE:"/orders/",CANCEL:e=>"/orders/".concat(e,"/cancel/"),TIMELINE:e=>"/orders/".concat(e,"/timeline/"),INVOICE:e=>"/orders/".concat(e,"/invoice/"),DOWNLOAD_INVOICE:e=>"/orders/".concat(e,"/download_invoice/")},RETURNS:{LIST:"/return-requests/",DETAIL:e=>"/return-requests/".concat(e,"/"),CREATE:"/return-requests/"},REPLACEMENTS:{LIST:"/replacements/",DETAIL:e=>"/replacements/".concat(e,"/"),CREATE:"/replacements/",UPDATE_STATUS:e=>"/replacements/".concat(e,"/update_status/")},SEARCH:{PRODUCTS:"/search/products/",SUGGESTIONS:"/search/suggestions/",FILTERS:"/search/filters/",POPULAR:"/search/popular/",RELATED:"/search/related/"},CUSTOMER:{PROFILE:"/customer/profile/",ADDRESSES:"/customer/addresses/",ADDRESS_DETAIL:e=>"/customer/addresses/".concat(e,"/"),PREFERENCES:"/customer/preferences/"},WISHLIST:{LIST:"/wishlist/",ADD:"/wishlist/add/",REMOVE:e=>"/wishlist/".concat(e,"/"),CLEAR:"/wishlist/clear/"},PAYMENTS:{METHODS:"/payments/methods/",CURRENCIES:"/payments/currencies/",CREATE:"/payments/create/",VERIFY:"/payments/verify/",STATUS:e=>"/payments/".concat(e,"/status/"),WALLET:"/payments/wallet/",GIFT_CARD:{VALIDATE:"/payments/gift-card/validate/",BALANCE:e=>"/payments/gift-card/".concat(e,"/balance/")},CONVERT_CURRENCY:"/payments/convert-currency/"},ADMIN:{DASHBOARD:"/admin/dashboard/",ANALYTICS:"/admin/analytics/",PRODUCTS:"/admin/products/",ORDERS:"/admin/orders/",CUSTOMERS:"/admin/customers/",SETTINGS:"/admin/settings/"},SELLER:{DASHBOARD:"/seller/dashboard/",PRODUCTS:"/seller/products/",ORDERS:"/seller/orders/",PROFILE:"/seller/profile/",KYC:"/seller/kyc/",PAYOUTS:"/seller/payouts/"}},l={ACCESS_TOKEN:"access_token",REFRESH_TOKEN:"refresh_token",USER:"user",CART:"cart"},c={CUSTOMER:"customer",SELLER:"seller",ADMIN:"admin"},d={PENDING:"PENDING",CONFIRMED:"CONFIRMED",PROCESSING:"PROCESSING",SHIPPED:"SHIPPED",DELIVERED:"DELIVERED",CANCELLED:"CANCELLED",RETURNED:"RETURNED"},i={...t.l$,...t.Ei,PROFILE:t.Hp.DASHBOARD,ORDERS:t.Hp.ORDERS,PROFILE_ADDRESSES:t.Hp.ADDRESSES,PROFILE_WISHLIST:t.Hp.WISHLIST,PROFILE_PREFERENCES:t.Hp.SETTINGS,ADMIN:t.F$.DASHBOARD,SELLER:t.B0.DASHBOARD},o={PASSWORD_MIN_LENGTH:8,USERNAME_MIN_LENGTH:3,PHONE_REGEX:/^[\+]?[1-9][\d]{0,15}$/,EMAIL_REGEX:/^[^\s@]+@[^\s@]+\.[^\s@]+$/}}},e=>{e.O(0,[3464,4540,1990,6766,6322,4288,7398,7544,1142,945,6711,9248,5359,7358],()=>e(e.s=3801)),_N_E=e.O()}]);